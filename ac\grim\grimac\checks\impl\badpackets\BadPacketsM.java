package ac.grim.grimac.checks.impl.badpackets;

import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.CheckData;
import ac.grim.grimac.checks.type.PacketCheck;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketReceiveEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.entity.type.EntityTypes;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.packettype.PacketType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.ClientVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientInteractEntity;
import ac.grim.grimac.utils.data.packetentity.PacketEntity;

@CheckData(
   name = "BadPacketsM"
)
public class BadPacketsM extends Check implements PacketCheck {
   private final boolean exempt;
   private boolean sentInteractAt;

   public BadPacketsM(GrimPlayer player) {
      super(player);
      this.exempt = this.player.getClientVersion().isOlderThanOrEquals(ClientVersion.V_1_7_10);
      this.sentInteractAt = false;
   }

   public void onPacketReceive(PacketReceiveEvent event) {
      if (event.getPacketType() == PacketType.Play.Client.INTERACT_ENTITY && !this.exempt) {
         WrapperPlayClientInteractEntity wrapper = new WrapperPlayClientInteractEntity(event);
         PacketEntity entity = (PacketEntity)this.player.compensatedEntities.entityMap.get(wrapper.getEntityId());
         if (entity != null && entity.getType() == EntityTypes.ARMOR_STAND) {
            return;
         }

         switch(wrapper.getAction()) {
         case INTERACT:
            if (!this.sentInteractAt && this.flagAndAlert("Missed Interact-At") && this.shouldModifyPackets()) {
               event.setCancelled(true);
               this.player.onPacketCancel();
            }

            this.sentInteractAt = false;
            break;
         case INTERACT_AT:
            if (this.sentInteractAt && this.flagAndAlert("Missed Interact") && this.shouldModifyPackets()) {
               event.setCancelled(true);
               this.player.onPacketCancel();
            }

            this.sentInteractAt = true;
         }
      }

   }
}
