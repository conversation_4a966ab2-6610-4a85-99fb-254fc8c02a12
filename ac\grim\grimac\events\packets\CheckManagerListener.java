package ac.grim.grimac.events.packets;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.events.packets.patch.ResyncWorldUtil;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.PacketEvents;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketListenerAbstract;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketListenerPriority;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketReceiveEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketSendEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.manager.server.ServerVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.ConnectionState;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.attribute.Attributes;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.item.ItemStack;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.item.type.ItemType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.item.type.ItemTypes;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.packettype.PacketType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.ClientVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.DiggingAction;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.GameMode;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.InteractionHand;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.world.BlockFace;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.world.Location;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.world.states.WrappedBlockState;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.world.states.defaulttags.BlockTags;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.world.states.type.StateType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.world.states.type.StateTypes;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.world.states.type.StateValue;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.util.Vector3d;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.util.Vector3f;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.util.Vector3i;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.PacketWrapper;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientPlayerBlockPlacement;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientPlayerDigging;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientPlayerFlying;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientUseItem;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientVehicleMove;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerAcknowledgeBlockChanges;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerSetSlot;
import ac.grim.grimac.shaded.io.github.retrooper.packetevents.util.SpigotConversionUtil;
import ac.grim.grimac.utils.anticheat.update.BlockBreak;
import ac.grim.grimac.utils.anticheat.update.BlockPlace;
import ac.grim.grimac.utils.anticheat.update.PositionUpdate;
import ac.grim.grimac.utils.anticheat.update.PredictionComplete;
import ac.grim.grimac.utils.anticheat.update.RotationUpdate;
import ac.grim.grimac.utils.anticheat.update.VehiclePositionUpdate;
import ac.grim.grimac.utils.blockplace.BlockPlaceResult;
import ac.grim.grimac.utils.blockplace.ConsumesBlockPlace;
import ac.grim.grimac.utils.change.BlockModification;
import ac.grim.grimac.utils.collisions.CollisionData;
import ac.grim.grimac.utils.collisions.HitboxData;
import ac.grim.grimac.utils.collisions.datatypes.CollisionBox;
import ac.grim.grimac.utils.collisions.datatypes.SimpleCollisionBox;
import ac.grim.grimac.utils.data.BlockPlaceSnapshot;
import ac.grim.grimac.utils.data.HeadRotation;
import ac.grim.grimac.utils.data.HitData;
import ac.grim.grimac.utils.data.Pair;
import ac.grim.grimac.utils.data.TeleportAcceptData;
import ac.grim.grimac.utils.data.VelocityData;
import ac.grim.grimac.utils.latency.CompensatedWorld;
import ac.grim.grimac.utils.math.GrimMath;
import ac.grim.grimac.utils.math.VectorUtils;
import ac.grim.grimac.utils.nmsutil.BlockBreakSpeed;
import ac.grim.grimac.utils.nmsutil.BoundingBoxSize;
import ac.grim.grimac.utils.nmsutil.Collisions;
import ac.grim.grimac.utils.nmsutil.Materials;
import ac.grim.grimac.utils.nmsutil.Ray;
import ac.grim.grimac.utils.nmsutil.ReachUtils;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;
import org.bukkit.util.Vector;

public class CheckManagerListener extends PacketListenerAbstract {
   private static final Function<StateType, Boolean> BREAKABLE = (type) -> {
      return !type.isAir() && type.getHardness() != -1.0F && type != StateTypes.WATER && type != StateTypes.LAVA;
   };

   public CheckManagerListener() {
      super(PacketListenerPriority.LOW);
   }

   public static HitData traverseBlocks(GrimPlayer player, Vector3d start, Vector3d end, BiFunction<WrappedBlockState, Vector3i, HitData> predicate) {
      double endX = GrimMath.lerp(-1.0E-7D, end.x, start.x);
      double endY = GrimMath.lerp(-1.0E-7D, end.y, start.y);
      double endZ = GrimMath.lerp(-1.0E-7D, end.z, start.z);
      double startX = GrimMath.lerp(-1.0E-7D, start.x, end.x);
      double startY = GrimMath.lerp(-1.0E-7D, start.y, end.y);
      double startZ = GrimMath.lerp(-1.0E-7D, start.z, end.z);
      int floorStartX = GrimMath.floor(startX);
      int floorStartY = GrimMath.floor(startY);
      int floorStartZ = GrimMath.floor(startZ);
      if (start.equals(end)) {
         return null;
      } else {
         WrappedBlockState state = player.compensatedWorld.getBlock(floorStartX, floorStartY, floorStartZ);
         HitData apply = (HitData)predicate.apply(state, new Vector3i(floorStartX, floorStartY, floorStartZ));
         if (apply != null) {
            return apply;
         } else {
            double xDiff = endX - startX;
            double yDiff = endY - startY;
            double zDiff = endZ - startZ;
            double xSign = Math.signum(xDiff);
            double ySign = Math.signum(yDiff);
            double zSign = Math.signum(zDiff);
            double posXInverse = xSign == 0.0D ? Double.MAX_VALUE : xSign / xDiff;
            double posYInverse = ySign == 0.0D ? Double.MAX_VALUE : ySign / yDiff;
            double posZInverse = zSign == 0.0D ? Double.MAX_VALUE : zSign / zDiff;
            double d12 = posXInverse * (xSign > 0.0D ? 1.0D - GrimMath.frac(startX) : GrimMath.frac(startX));
            double d13 = posYInverse * (ySign > 0.0D ? 1.0D - GrimMath.frac(startY) : GrimMath.frac(startY));
            double d14 = posZInverse * (zSign > 0.0D ? 1.0D - GrimMath.frac(startZ) : GrimMath.frac(startZ));

            do {
               if (!(d12 <= 1.0D) && !(d13 <= 1.0D) && !(d14 <= 1.0D)) {
                  return null;
               }

               if (d12 < d13) {
                  if (d12 < d14) {
                     floorStartX = (int)((double)floorStartX + xSign);
                     d12 += posXInverse;
                  } else {
                     floorStartZ = (int)((double)floorStartZ + zSign);
                     d14 += posZInverse;
                  }
               } else if (d13 < d14) {
                  floorStartY = (int)((double)floorStartY + ySign);
                  d13 += posYInverse;
               } else {
                  floorStartZ = (int)((double)floorStartZ + zSign);
                  d14 += posZInverse;
               }

               state = player.compensatedWorld.getBlock(floorStartX, floorStartY, floorStartZ);
               apply = (HitData)predicate.apply(state, new Vector3i(floorStartX, floorStartY, floorStartZ));
            } while(apply == null);

            return apply;
         }
      }
   }

   private static void placeWaterLavaSnowBucket(GrimPlayer player, ItemStack held, StateType toPlace, InteractionHand hand) {
      HitData data = getNearestHitResult(player, StateTypes.AIR, false, true, true);
      if (data != null) {
         BlockPlace blockPlace = new BlockPlace(player, hand, data.getPosition(), data.getClosestDirection().getFaceValue(), data.getClosestDirection(), held, data);
         boolean didPlace = false;
         if (Materials.isPlaceableWaterBucket(blockPlace.getItemStack().getType()) && PacketEvents.getAPI().getServerManager().getVersion().isNewerThanOrEquals(ServerVersion.V_1_13)) {
            blockPlace.setReplaceClicked(true);
            WrappedBlockState existing = blockPlace.getExistingBlockData();
            if (!(Boolean)existing.getInternalData().getOrDefault(StateValue.WATERLOGGED, true)) {
               didPlace = true;
            }
         }

         if (!didPlace) {
            blockPlace.setReplaceClicked(false);
            blockPlace.set(toPlace);
         }

         if (player.gamemode != GameMode.CREATIVE) {
            player.getInventory().markSlotAsResyncing(blockPlace);
            if (hand == InteractionHand.MAIN_HAND) {
               player.getInventory().inventory.setHeldItem(ItemStack.builder().type(ItemTypes.BUCKET).amount(1).build());
            } else {
               player.getInventory().inventory.setPlayerInventoryItem(45, ItemStack.builder().type(ItemTypes.BUCKET).amount(1).build());
            }
         }
      }

   }

   public static void handleQueuedPlaces(GrimPlayer player, boolean hasLook, float pitch, float yaw, long now) {
      BlockPlaceSnapshot snapshot;
      while((snapshot = (BlockPlaceSnapshot)player.placeUseItemPackets.poll()) != null) {
         double lastX = player.x;
         double lastY = player.y;
         double lastZ = player.z;
         player.x = player.packetStateData.lastClaimedPosition.getX();
         player.y = player.packetStateData.lastClaimedPosition.getY();
         player.z = player.packetStateData.lastClaimedPosition.getZ();
         boolean lastSneaking = player.isSneaking;
         player.isSneaking = snapshot.isSneaking();
         if (player.inVehicle()) {
            Vector3d posFromVehicle = BoundingBoxSize.getRidingOffsetFromVehicle(player.compensatedEntities.self.getRiding(), player);
            player.x = posFromVehicle.getX();
            player.y = posFromVehicle.getY();
            player.z = posFromVehicle.getZ();
         }

         if ((now - player.lastBlockPlaceUseItem < 15L || player.getClientVersion().isOlderThan(ClientVersion.V_1_9)) && hasLook) {
            player.xRot = yaw;
            player.yRot = pitch;
         }

         player.compensatedWorld.startPredicting();
         handleBlockPlaceOrUseItem(snapshot.getWrapper(), player);
         player.compensatedWorld.stopPredicting(snapshot.getWrapper());
         player.x = lastX;
         player.y = lastY;
         player.z = lastZ;
         player.isSneaking = lastSneaking;
      }

   }

   public static void handleQueuedBreaks(GrimPlayer player, boolean hasLook, float pitch, float yaw, long now) {
      BlockBreak blockBreak;
      while((blockBreak = (BlockBreak)player.queuedBreaks.poll()) != null) {
         double lastX = player.x;
         double lastY = player.y;
         double lastZ = player.z;
         player.x = player.packetStateData.lastClaimedPosition.getX();
         player.y = player.packetStateData.lastClaimedPosition.getY();
         player.z = player.packetStateData.lastClaimedPosition.getZ();
         if (player.inVehicle()) {
            Vector3d posFromVehicle = BoundingBoxSize.getRidingOffsetFromVehicle(player.compensatedEntities.self.getRiding(), player);
            player.x = posFromVehicle.getX();
            player.y = posFromVehicle.getY();
            player.z = posFromVehicle.getZ();
         }

         if ((now - player.lastBlockBreak < 15L || player.getClientVersion().isOlderThan(ClientVersion.V_1_9)) && hasLook) {
            player.xRot = yaw;
            player.yRot = pitch;
         }

         player.checkManager.onPostFlyingBlockBreak(blockBreak);
         player.x = lastX;
         player.y = lastY;
         player.z = lastZ;
      }

   }

   private static void handleUseItem(GrimPlayer player, ItemStack placedWith, InteractionHand hand) {
      if (placedWith.getType() == ItemTypes.LILY_PAD) {
         placeLilypad(player, hand);
      } else {
         StateType toBucketMat = Materials.transformBucketMaterial(placedWith.getType());
         if (toBucketMat != null) {
            placeWaterLavaSnowBucket(player, placedWith, toBucketMat, hand);
         }

         if (placedWith.getType() == ItemTypes.BUCKET) {
            placeBucket(player, hand);
         }

      }
   }

   private static void handleBlockPlaceOrUseItem(PacketWrapper<?> packet, GrimPlayer player) {
      WrapperPlayClientPlayerBlockPlacement place;
      ItemStack placedWith;
      if (packet instanceof WrapperPlayClientPlayerBlockPlacement) {
         place = (WrapperPlayClientPlayerBlockPlacement)packet;
         if (PacketEvents.getAPI().getServerManager().getVersion().isOlderThan(ServerVersion.V_1_9)) {
            if (player.gamemode == GameMode.SPECTATOR || player.gamemode == GameMode.ADVENTURE) {
               return;
            }

            if (place.getFace() == BlockFace.OTHER) {
               placedWith = player.getInventory().getHeldItem();
               if (place.getHand() == InteractionHand.OFF_HAND) {
                  placedWith = player.getInventory().getOffHand();
               }

               handleUseItem(player, placedWith, place.getHand());
               return;
            }
         }
      }

      if (packet instanceof WrapperPlayClientUseItem) {
         WrapperPlayClientUseItem place = (WrapperPlayClientUseItem)packet;
         if (player.gamemode == GameMode.SPECTATOR || player.gamemode == GameMode.ADVENTURE) {
            return;
         }

         placedWith = player.getInventory().getHeldItem();
         if (place.getHand() == InteractionHand.OFF_HAND) {
            placedWith = player.getInventory().getOffHand();
         }

         handleUseItem(player, placedWith, place.getHand());
      }

      if (packet instanceof WrapperPlayClientPlayerBlockPlacement) {
         place = (WrapperPlayClientPlayerBlockPlacement)packet;
         placedWith = player.getInventory().getHeldItem();
         ItemStack offhand = player.getInventory().getOffHand();
         boolean onlyAir = placedWith.isEmpty() && offhand.isEmpty();
         if ((!player.isSneaking || onlyAir) && place.getHand() == InteractionHand.MAIN_HAND) {
            Vector3i blockPosition = place.getBlockPosition();
            BlockPlace blockPlace = new BlockPlace(player, place.getHand(), blockPosition, place.getFaceId(), place.getFace(), placedWith, getNearestHitResult(player, (StateType)null, true, false, false));
            StateType placedAgainst = blockPlace.getPlacedAgainstMaterial();
            if (player.getClientVersion().isOlderThan(ClientVersion.V_1_11) && (placedAgainst == StateTypes.IRON_TRAPDOOR || placedAgainst == StateTypes.IRON_DOOR || BlockTags.FENCES.contains(placedAgainst)) || player.getClientVersion().isOlderThanOrEquals(ClientVersion.V_1_8) && BlockTags.CAULDRONS.contains(placedAgainst) || Materials.isClientSideInteractable(placedAgainst)) {
               if (!player.inVehicle()) {
                  player.checkManager.onPostFlyingBlockPlace(blockPlace);
               }

               Vector3i location = blockPlace.getPlacedAgainstBlockLocation();
               player.compensatedWorld.tickOpenable(location.getX(), location.getY(), location.getZ());
               return;
            }

            if (ConsumesBlockPlace.consumesPlace(player, player.compensatedWorld.getBlock(blockPlace.getPlacedAgainstBlockLocation()), blockPlace)) {
               if (!player.inVehicle()) {
                  player.checkManager.onPostFlyingBlockPlace(blockPlace);
               }

               return;
            }
         }
      }

      if (packet instanceof WrapperPlayClientPlayerBlockPlacement) {
         place = (WrapperPlayClientPlayerBlockPlacement)packet;
         if (player.gamemode == GameMode.SPECTATOR || player.gamemode == GameMode.ADVENTURE) {
            return;
         }

         Vector3i blockPosition = place.getBlockPosition();
         BlockFace face = place.getFace();
         ItemStack placedWith = player.getInventory().getHeldItem();
         if (place.getHand() == InteractionHand.OFF_HAND) {
            placedWith = player.getInventory().getOffHand();
         }

         BlockPlace blockPlace = new BlockPlace(player, place.getHand(), blockPosition, place.getFaceId(), face, placedWith, getNearestHitResult(player, (StateType)null, true, false, false));
         if (!player.inVehicle()) {
            player.checkManager.onPostFlyingBlockPlace(blockPlace);
         }

         blockPlace.setInside((Boolean)place.getInsideBlock().orElse(false));
         if (placedWith.getType().getPlacedType() != null || placedWith.getType() == ItemTypes.FLINT_AND_STEEL || placedWith.getType() == ItemTypes.FIRE_CHARGE) {
            BlockPlaceResult.getMaterialData(placedWith.getType()).applyBlockPlaceToWorld(player, blockPlace);
         }
      }

   }

   private boolean isMojangStupid(GrimPlayer player, PacketReceiveEvent event, WrapperPlayClientPlayerFlying flying) {
      if (player.packetStateData.lastPacketWasTeleport) {
         return false;
      } else if (player.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_21)) {
         return false;
      } else {
         Location location = flying.getLocation();
         double threshold = player.getMovementThreshold();
         if (player.packetStateData.lastPacketWasTeleport || !flying.hasPositionChanged() || !flying.hasRotationChanged() || (flying.isOnGround() != player.packetStateData.packetPlayerOnGround || !player.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_17) || !(player.filterMojangStupidityOnMojangStupidity.distanceSquared(location.getPosition()) < threshold * threshold)) && !player.inVehicle()) {
            return false;
         } else {
            if (PacketEvents.getAPI().getServerManager().getVersion().isOlderThanOrEquals(ServerVersion.V_1_9)) {
               if (player.isCancelDuplicatePacket()) {
                  player.packetStateData.cancelDuplicatePacket = true;
               }
            } else {
               flying.setLocation(new Location(player.filterMojangStupidityOnMojangStupidity.getX(), player.filterMojangStupidityOnMojangStupidity.getY(), player.filterMojangStupidityOnMojangStupidity.getZ(), location.getYaw(), location.getPitch()));
               event.markForReEncode(true);
            }

            player.packetStateData.lastPacketWasOnePointSeventeenDuplicate = true;
            if (!player.isIgnoreDuplicatePacketRotation()) {
               if (player.xRot != location.getYaw() || player.yRot != location.getPitch()) {
                  player.lastXRot = player.xRot;
                  player.lastYRot = player.yRot;
               }

               player.xRot = location.getYaw();
               player.yRot = location.getPitch();
            }

            player.packetStateData.lastClaimedPosition = location.getPosition();
            return true;
         }
      }
   }

   public void onPacketReceive(PacketReceiveEvent event) {
      GrimPlayer player = GrimAPI.INSTANCE.getPlayerDataManager().getPlayer(event.getUser());
      if (player != null) {
         if (event.getConnectionState() != ConnectionState.PLAY) {
            if (event.getConnectionState() == ConnectionState.CONFIGURATION) {
               player.checkManager.onPacketReceive(event);
            }
         } else {
            if (event.getPacketType() == PacketType.Play.Client.VEHICLE_MOVE) {
               WrapperPlayClientVehicleMove move = new WrapperPlayClientVehicleMove(event);
               Vector3d position = move.getPosition();
               player.packetStateData.lastPacketWasTeleport = player.getSetbackTeleportUtil().checkVehicleTeleportQueue(position.getX(), position.getY(), position.getZ());
            }

            TeleportAcceptData teleportData = null;
            Vector3d position;
            WrapperPlayClientPlayerFlying flying;
            if (WrapperPlayClientPlayerFlying.isFlying(event.getPacketType())) {
               flying = new WrapperPlayClientPlayerFlying(event);
               position = VectorUtils.clampVector(flying.getLocation().getPosition());
               teleportData = flying.hasPositionChanged() && flying.hasRotationChanged() ? player.getSetbackTeleportUtil().checkTeleportQueue(position.getX(), position.getY(), position.getZ()) : new TeleportAcceptData();
               player.packetStateData.lastPacketWasTeleport = teleportData.isTeleport();
               player.packetStateData.lastPacketWasOnePointSeventeenDuplicate = this.isMojangStupid(player, event, flying);
            }

            label229: {
               if (player.inVehicle()) {
                  if (event.getPacketType() != PacketType.Play.Client.VEHICLE_MOVE) {
                     break label229;
                  }
               } else if (!WrapperPlayClientPlayerFlying.isFlying(event.getPacketType()) || player.packetStateData.lastPacketWasOnePointSeventeenDuplicate) {
                  break label229;
               }

               int kbEntityId = player.inVehicle() ? player.getRidingVehicleId() : player.entityID;
               VelocityData calculatedFirstBreadKb = player.checkManager.getKnockbackHandler().calculateFirstBreadKnockback(kbEntityId, player.lastTransactionReceived.get());
               VelocityData calculatedRequireKb = player.checkManager.getKnockbackHandler().calculateRequiredKB(kbEntityId, player.lastTransactionReceived.get(), false);
               player.firstBreadKB = calculatedFirstBreadKb == null ? player.firstBreadKB : calculatedFirstBreadKb;
               player.likelyKB = calculatedRequireKb == null ? player.likelyKB : calculatedRequireKb;
               VelocityData calculateFirstBreadExplosion = player.checkManager.getExplosionHandler().getFirstBreadAddedExplosion(player.lastTransactionReceived.get());
               VelocityData calculateRequiredExplosion = player.checkManager.getExplosionHandler().getPossibleExplosions(player.lastTransactionReceived.get(), false);
               player.firstBreadExplosion = calculateFirstBreadExplosion == null ? player.firstBreadExplosion : calculateFirstBreadExplosion;
               player.likelyExplosions = calculateRequiredExplosion == null ? player.likelyExplosions : calculateRequiredExplosion;
            }

            player.checkManager.onPrePredictionReceivePacket(event);
            if (!event.isCancelled() || !WrapperPlayClientPlayerFlying.isFlying(event.getPacketType()) && event.getPacketType() != PacketType.Play.Client.VEHICLE_MOVE) {
               if (WrapperPlayClientPlayerFlying.isFlying(event.getPacketType())) {
                  flying = new WrapperPlayClientPlayerFlying(event);
                  Location pos = flying.getLocation();
                  boolean ignoreRotation = player.packetStateData.lastPacketWasOnePointSeventeenDuplicate && player.isIgnoreDuplicatePacketRotation();
                  this.handleFlying(player, pos.getX(), pos.getY(), pos.getZ(), ignoreRotation ? player.xRot : pos.getYaw(), ignoreRotation ? player.yRot : pos.getPitch(), flying.hasPositionChanged(), flying.hasRotationChanged(), flying.isOnGround(), teleportData, event);
               }

               if (event.getPacketType() == PacketType.Play.Client.VEHICLE_MOVE && player.inVehicle()) {
                  WrapperPlayClientVehicleMove move = new WrapperPlayClientVehicleMove(event);
                  position = move.getPosition();
                  player.lastX = player.x;
                  player.lastY = player.y;
                  player.lastZ = player.z;
                  Vector3d clamp = VectorUtils.clampVector(position);
                  player.x = clamp.getX();
                  player.y = clamp.getY();
                  player.z = clamp.getZ();
                  player.xRot = move.getYaw();
                  player.yRot = move.getPitch();
                  VehiclePositionUpdate update = new VehiclePositionUpdate(clamp, position, move.getYaw(), move.getPitch(), player.packetStateData.lastPacketWasTeleport);
                  player.checkManager.onVehiclePositionUpdate(update);
                  player.packetStateData.receivedSteerVehicle = false;
               }

               if (event.getPacketType() == PacketType.Play.Client.PLAYER_DIGGING) {
                  player.lastBlockBreak = System.currentTimeMillis();
                  WrapperPlayClientPlayerDigging packet = new WrapperPlayClientPlayerDigging(event);
                  DiggingAction action = packet.getAction();
                  if (action == DiggingAction.START_DIGGING || action == DiggingAction.FINISHED_DIGGING || action == DiggingAction.CANCELLED_DIGGING) {
                     BlockBreak blockBreak = new BlockBreak(player, packet.getBlockPosition(), packet.getBlockFace(), packet.getBlockFaceId(), action, player.compensatedWorld.getBlock(packet.getBlockPosition()));
                     player.checkManager.onBlockBreak(blockBreak);
                     if (blockBreak.isCancelled()) {
                        event.setCancelled(true);
                        player.onPacketCancel();
                        ResyncWorldUtil.resyncPosition(player, blockBreak.position, packet.getSequence());
                     } else {
                        player.queuedBreaks.add(blockBreak);
                        if (action == DiggingAction.FINISHED_DIGGING && (Boolean)BREAKABLE.apply(blockBreak.block.getType())) {
                           player.compensatedWorld.startPredicting();
                           player.compensatedWorld.updateBlock(blockBreak.position.x, blockBreak.position.y, blockBreak.position.z, 0);
                           player.compensatedWorld.stopPredicting(packet);
                        }

                        if (action == DiggingAction.START_DIGGING) {
                           double damage = BlockBreakSpeed.getBlockDamage(player, blockBreak.position);
                           if (damage >= 1.0D) {
                              player.compensatedWorld.startPredicting();
                              player.blockHistory.add(new BlockModification(player.compensatedWorld.getBlock(blockBreak.position), WrappedBlockState.getByGlobalId(0), blockBreak.position, GrimAPI.INSTANCE.getTickManager().currentTick, BlockModification.Cause.START_DIGGING));
                              if (player.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_13) && Materials.isWaterSource(player.getClientVersion(), blockBreak.block)) {
                                 player.compensatedWorld.updateBlock(blockBreak.position, StateTypes.WATER.createBlockState(CompensatedWorld.blockVersion));
                              } else {
                                 player.compensatedWorld.updateBlock(blockBreak.position.x, blockBreak.position.y, blockBreak.position.z, 0);
                              }

                              player.compensatedWorld.stopPredicting(packet);
                           }
                        }

                        player.compensatedWorld.handleBlockBreakPrediction(packet);
                     }
                  }
               }

               if (event.getPacketType() == PacketType.Play.Client.PLAYER_BLOCK_PLACEMENT) {
                  WrapperPlayClientPlayerBlockPlacement packet = new WrapperPlayClientPlayerBlockPlacement(event);
                  player.lastBlockPlaceUseItem = System.currentTimeMillis();
                  ItemStack placedWith = player.getInventory().getHeldItem();
                  if (packet.getHand() == InteractionHand.OFF_HAND) {
                     placedWith = player.getInventory().getOffHand();
                  }

                  if (packet.getFace() == BlockFace.OTHER && PacketEvents.getAPI().getServerManager().getVersion().isOlderThan(ServerVersion.V_1_9)) {
                     player.placeUseItemPackets.add(new BlockPlaceSnapshot(packet, player.isSneaking));
                  } else {
                     BlockPlace blockPlace = new BlockPlace(player, packet.getHand(), packet.getBlockPosition(), packet.getFaceId(), packet.getFace(), placedWith, getNearestHitResult(player, (StateType)null, true, false, false));
                     blockPlace.setCursor(packet.getCursorPosition());
                     if (PacketEvents.getAPI().getServerManager().getVersion().isNewerThanOrEquals(ServerVersion.V_1_11) && player.getClientVersion().isOlderThan(ClientVersion.V_1_11) && packet.getCursorPosition().getX() * 15.0F % 1.0F == 0.0F && packet.getCursorPosition().getY() * 15.0F % 1.0F == 0.0F && packet.getCursorPosition().getZ() * 15.0F % 1.0F == 0.0F) {
                        int trueByteX = (int)(packet.getCursorPosition().getX() * 15.0F);
                        int trueByteY = (int)(packet.getCursorPosition().getY() * 15.0F);
                        int trueByteZ = (int)(packet.getCursorPosition().getZ() * 15.0F);
                        blockPlace.setCursor(new Vector3f((float)trueByteX / 16.0F, (float)trueByteY / 16.0F, (float)trueByteZ / 16.0F));
                     }

                     if (!player.inVehicle()) {
                        player.checkManager.onBlockPlace(blockPlace);
                     }

                     if (!event.isCancelled() && !blockPlace.isCancelled() && !player.getSetbackTeleportUtil().shouldBlockMovement()) {
                        player.placeUseItemPackets.add(new BlockPlaceSnapshot(packet, player.isSneaking));
                     } else {
                        if (!event.isCancelled()) {
                           event.setCancelled(true);
                           player.onPacketCancel();
                        }

                        Vector3i facePos = new Vector3i(packet.getBlockPosition().getX() + packet.getFace().getModX(), packet.getBlockPosition().getY() + packet.getFace().getModY(), packet.getBlockPosition().getZ() + packet.getFace().getModZ());
                        if (player.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_19) && PacketEvents.getAPI().getServerManager().getVersion().isNewerThanOrEquals(ServerVersion.V_1_19)) {
                           player.user.sendPacket((PacketWrapper)(new WrapperPlayServerAcknowledgeBlockChanges(packet.getSequence())));
                        } else {
                           ResyncWorldUtil.resyncPosition(player, packet.getBlockPosition());
                           ResyncWorldUtil.resyncPosition(player, facePos);
                        }

                        if (player.bukkitPlayer != null) {
                           ItemStack mainHand;
                           if (packet.getHand() == InteractionHand.MAIN_HAND) {
                              mainHand = SpigotConversionUtil.fromBukkitItemStack(player.bukkitPlayer.getInventory().getItemInHand());
                              int var10005 = 36 + player.packetStateData.lastSlotSelected;
                              player.user.sendPacket((PacketWrapper)(new WrapperPlayServerSetSlot(0, player.getInventory().stateID, var10005, mainHand)));
                           } else {
                              mainHand = SpigotConversionUtil.fromBukkitItemStack(player.bukkitPlayer.getInventory().getItemInOffHand());
                              player.user.sendPacket((PacketWrapper)(new WrapperPlayServerSetSlot(0, player.getInventory().stateID, 45, mainHand)));
                           }
                        }
                     }
                  }
               }

               if (event.getPacketType() == PacketType.Play.Client.USE_ITEM) {
                  WrapperPlayClientUseItem packet = new WrapperPlayClientUseItem(event);
                  player.placeUseItemPackets.add(new BlockPlaceSnapshot(packet, player.isSneaking));
                  player.lastBlockPlaceUseItem = System.currentTimeMillis();
               }

               player.checkManager.onPacketReceive(event);
               if (player.packetStateData.cancelDuplicatePacket) {
                  event.setCancelled(true);
                  player.packetStateData.cancelDuplicatePacket = false;
               }

               if (event.getPacketType() == PacketType.Play.Client.CLIENT_TICK_END) {
                  if (!player.packetStateData.didSendMovementBeforeTickEnd) {
                     player.packetStateData.didLastLastMovementIncludePosition = player.packetStateData.didLastMovementIncludePosition;
                     player.packetStateData.didLastMovementIncludePosition = false;
                  }

                  player.packetStateData.didSendMovementBeforeTickEnd = false;
               }

               player.packetStateData.lastPacketWasOnePointSeventeenDuplicate = false;
               player.packetStateData.lastPacketWasTeleport = false;
            } else {
               player.packetStateData.cancelDuplicatePacket = false;
            }
         }
      }
   }

   private static void placeBucket(GrimPlayer player, InteractionHand hand) {
      HitData data = getNearestHitResult(player, (StateType)null, true, false, true);
      if (data != null) {
         BlockPlace blockPlace = new BlockPlace(player, hand, data.getPosition(), data.getClosestDirection().getFaceValue(), data.getClosestDirection(), ItemStack.EMPTY, data);
         blockPlace.setReplaceClicked(true);
         boolean placed = false;
         ItemType type = null;
         if (data.getState().getType() == StateTypes.POWDER_SNOW) {
            blockPlace.set(StateTypes.AIR);
            type = ItemTypes.POWDER_SNOW_BUCKET;
            placed = true;
         }

         if (data.getState().getType() == StateTypes.LAVA) {
            blockPlace.set(StateTypes.AIR);
            type = ItemTypes.LAVA_BUCKET;
            placed = true;
         }

         if (!placed && !player.compensatedWorld.isWaterSourceBlock(data.getPosition().getX(), data.getPosition().getY(), data.getPosition().getZ())) {
            return;
         }

         if (data.getState().getType() == StateTypes.KELP || data.getState().getType() == StateTypes.SEAGRASS || data.getState().getType() == StateTypes.TALL_SEAGRASS) {
            return;
         }

         if (!placed) {
            type = ItemTypes.WATER_BUCKET;
         }

         if (PacketEvents.getAPI().getServerManager().getVersion().isNewerThanOrEquals(ServerVersion.V_1_13)) {
            WrappedBlockState existing = blockPlace.getExistingBlockData();
            if (existing.getInternalData().containsKey(StateValue.WATERLOGGED)) {
               existing.setWaterlogged(false);
               blockPlace.set(existing);
               placed = true;
            }
         }

         if (!placed) {
            blockPlace.set(StateTypes.AIR);
         }

         if (player.gamemode != GameMode.CREATIVE) {
            player.getInventory().markSlotAsResyncing(blockPlace);
            setPlayerItem(player, hand, type);
         }
      }

   }

   public static void setPlayerItem(GrimPlayer player, InteractionHand hand, ItemType type) {
      if (player.gamemode != GameMode.CREATIVE) {
         if (hand == InteractionHand.MAIN_HAND) {
            if (player.getInventory().getHeldItem().getAmount() == 1) {
               player.getInventory().inventory.setHeldItem(ItemStack.builder().type(type).amount(1).build());
            } else {
               player.getInventory().inventory.add(ItemStack.builder().type(type).amount(1).build());
               player.getInventory().getHeldItem().setAmount(player.getInventory().getHeldItem().getAmount() - 1);
            }
         } else if (player.getInventory().getOffHand().getAmount() == 1) {
            player.getInventory().inventory.setPlayerInventoryItem(45, ItemStack.builder().type(type).amount(1).build());
         } else {
            player.getInventory().inventory.add(45, ItemStack.builder().type(type).amount(1).build());
            player.getInventory().getOffHand().setAmount(player.getInventory().getOffHand().getAmount() - 1);
         }
      }

   }

   private void handleFlying(GrimPlayer player, double x, double y, double z, float yaw, float pitch, boolean hasPosition, boolean hasLook, boolean onGround, TeleportAcceptData teleportData, PacketReceiveEvent event) {
      long now = System.currentTimeMillis();
      if (!hasPosition) {
         player.uncertaintyHandler.lastPointThree.reset();
      }

      if (hasLook && (!player.packetStateData.lastPacketWasOnePointSeventeenDuplicate || player.xRot != yaw || player.yRot != pitch)) {
         player.lastXRot = player.xRot;
         player.lastYRot = player.yRot;
      }

      handleQueuedPlaces(player, hasLook, pitch, yaw, now);
      handleQueuedBreaks(player, hasLook, pitch, yaw, now);
      if (hasPosition) {
         player.packetStateData.lastClaimedPosition = new Vector3d(x, y, z);
      }

      if (!hasPosition && onGround != player.packetStateData.packetPlayerOnGround && !player.inVehicle()) {
         player.lastOnGround = onGround;
         player.clientClaimsLastOnGround = onGround;
         player.uncertaintyHandler.onGroundUncertain = true;
         boolean canFeasiblyPointThree = Collisions.slowCouldPointThreeHitGround(player, player.x, player.y, player.z);
         if (!canFeasiblyPointThree && !player.compensatedWorld.isNearHardEntity(player.boundingBox.copy().expand(4.0D)) || player.clientVelocity.getY() > 0.06D) {
            player.getSetbackTeleportUtil().executeForceResync();
         }
      }

      if (!player.packetStateData.lastPacketWasTeleport) {
         player.packetStateData.packetPlayerOnGround = onGround;
      }

      if (hasLook) {
         player.xRot = yaw;
         player.yRot = pitch;
         float deltaXRot = player.xRot - player.lastXRot;
         float deltaYRot = player.yRot - player.lastYRot;
         RotationUpdate update = new RotationUpdate(new HeadRotation(player.lastXRot, player.lastYRot), new HeadRotation(player.xRot, player.yRot), deltaXRot, deltaYRot);
         player.checkManager.onRotationUpdate(update);
      }

      if (hasPosition) {
         Vector3d position = new Vector3d(x, y, z);
         Vector3d clampVector = VectorUtils.clampVector(position);
         PositionUpdate update = new PositionUpdate(new Vector3d(player.x, player.y, player.z), position, onGround, teleportData.getSetback(), teleportData.getTeleportData(), teleportData.isTeleport());
         if (!player.packetStateData.lastPacketWasOnePointSeventeenDuplicate) {
            player.filterMojangStupidityOnMojangStupidity = clampVector;
         }

         if (!player.inVehicle() && !player.packetStateData.lastPacketWasOnePointSeventeenDuplicate) {
            player.lastX = player.x;
            player.lastY = player.y;
            player.lastZ = player.z;
            player.x = clampVector.getX();
            player.y = clampVector.getY();
            player.z = clampVector.getZ();
            player.checkManager.onPositionUpdate(update);
         } else if (update.isTeleport()) {
            player.getSetbackTeleportUtil().onPredictionComplete(new PredictionComplete(0.0D, update, true));
         }
      }

      player.packetStateData.didLastLastMovementIncludePosition = player.packetStateData.didLastMovementIncludePosition;
      player.packetStateData.didLastMovementIncludePosition = hasPosition;
      if (!player.packetStateData.lastPacketWasTeleport) {
         player.packetStateData.didSendMovementBeforeTickEnd = true;
      }

      player.packetStateData.horseInteractCausedForcedRotation = false;
   }

   private static void placeLilypad(GrimPlayer player, InteractionHand hand) {
      HitData data = getNearestHitResult(player, (StateType)null, true, false, true);
      if (data != null) {
         if (player.compensatedWorld.getFluidLevelAt(data.getPosition().getX(), data.getPosition().getY() + 1, data.getPosition().getZ()) > 0.0D) {
            return;
         }

         BlockPlace blockPlace = new BlockPlace(player, hand, data.getPosition(), data.getClosestDirection().getFaceValue(), data.getClosestDirection(), ItemStack.EMPTY, data);
         blockPlace.setReplaceClicked(false);
         if (player.compensatedWorld.getWaterFluidLevelAt(data.getPosition().getX(), data.getPosition().getY(), data.getPosition().getZ()) > 0.0D || data.getState().getType() == StateTypes.ICE || data.getState().getType() == StateTypes.FROSTED_ICE) {
            Vector3i pos = data.getPosition();
            pos = pos.add(0, 1, 0);
            blockPlace.set(pos, StateTypes.LILY_PAD.createBlockState(CompensatedWorld.blockVersion));
            if (player.gamemode != GameMode.CREATIVE) {
               player.getInventory().markSlotAsResyncing(blockPlace);
               if (hand == InteractionHand.MAIN_HAND) {
                  player.getInventory().inventory.getHeldItem().setAmount(player.getInventory().inventory.getHeldItem().getAmount() - 1);
               } else {
                  player.getInventory().getOffHand().setAmount(player.getInventory().getOffHand().getAmount() - 1);
               }
            }
         }
      }

   }

   private static HitData getNearestHitResult(GrimPlayer player, StateType heldItem, boolean sourcesHaveHitbox, boolean fluidPlacement, boolean itemUsePlacement) {
      Vector3d startingPos = new Vector3d(player.x, player.y + player.getEyeHeight(), player.z);
      Vector startingVec = new Vector(startingPos.getX(), startingPos.getY(), startingPos.getZ());
      Ray trace = new Ray(player, startingPos.getX(), startingPos.getY(), startingPos.getZ(), player.xRot, player.yRot);
      double distance = itemUsePlacement && player.getClientVersion().isOlderThan(ClientVersion.V_1_20_5) ? 5.0D : player.compensatedEntities.self.getAttributeValue(Attributes.BLOCK_INTERACTION_RANGE);
      Vector endVec = trace.getPointAtDistance(distance);
      Vector3d endPos = new Vector3d(endVec.getX(), endVec.getY(), endVec.getZ());
      return traverseBlocks(player, startingPos, endPos, (block, vector3i) -> {
         if (fluidPlacement && player.getClientVersion().isOlderThan(ClientVersion.V_1_13) && CollisionData.getData(block.getType()).getMovementCollisionBox(player, player.getClientVersion(), block, vector3i.getX(), vector3i.getY(), vector3i.getZ()).isNull()) {
            return null;
         } else {
            CollisionBox data = HitboxData.getBlockHitbox(player, heldItem, player.getClientVersion(), block, false, vector3i.getX(), vector3i.getY(), vector3i.getZ());
            List<SimpleCollisionBox> boxes = new ArrayList();
            data.downCast((List)boxes);
            double bestHitResult = Double.MAX_VALUE;
            Vector bestHitLoc = null;
            BlockFace bestFace = null;
            Iterator var16 = boxes.iterator();

            while(var16.hasNext()) {
               SimpleCollisionBox box = (SimpleCollisionBox)var16.next();
               Pair<Vector, BlockFace> interceptx = ReachUtils.calculateIntercept(box, trace.getOrigin(), trace.getPointAtDistance(distance));
               if (interceptx.first() != null) {
                  Vector hitLoc = (Vector)interceptx.first();
                  if (hitLoc.distanceSquared(startingVec) < bestHitResult) {
                     bestHitResult = hitLoc.distanceSquared(startingVec);
                     bestHitLoc = hitLoc;
                     bestFace = (BlockFace)interceptx.second();
                  }
               }
            }

            if (bestHitLoc != null) {
               return new HitData(vector3i, bestHitLoc, bestFace, block);
            } else {
               if (sourcesHaveHitbox && (player.compensatedWorld.isWaterSourceBlock(vector3i.getX(), vector3i.getY(), vector3i.getZ()) || player.compensatedWorld.getLavaFluidLevelAt(vector3i.getX(), vector3i.getY(), vector3i.getZ()) == 0.8888888955116272D)) {
                  double waterHeight = player.getClientVersion().isOlderThan(ClientVersion.V_1_13) ? 1.0D : player.compensatedWorld.getFluidLevelAt(vector3i.getX(), vector3i.getY(), vector3i.getZ());
                  SimpleCollisionBox boxx = new SimpleCollisionBox((double)vector3i.getX(), (double)vector3i.getY(), (double)vector3i.getZ(), (double)(vector3i.getX() + 1), (double)vector3i.getY() + waterHeight, (double)(vector3i.getZ() + 1));
                  Pair<Vector, BlockFace> intercept = ReachUtils.calculateIntercept(boxx, trace.getOrigin(), trace.getPointAtDistance(distance));
                  if (intercept.first() != null) {
                     return new HitData(vector3i, (Vector)intercept.first(), (BlockFace)intercept.second(), block);
                  }
               }

               return null;
            }
         }
      });
   }

   public void onPacketSend(PacketSendEvent event) {
      if (event.getConnectionState() == ConnectionState.PLAY) {
         GrimPlayer player = GrimAPI.INSTANCE.getPlayerDataManager().getPlayer(event.getUser());
         if (player != null) {
            player.checkManager.onPacketSend(event);
         }
      }
   }
}
