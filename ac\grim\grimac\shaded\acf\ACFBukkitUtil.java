package ac.grim.grimac.shaded.acf;

import ac.grim.grimac.shaded.jetbrains.annotations.NotNull;
import ac.grim.grimac.shaded.jetbrains.annotations.Nullable;
import ac.grim.grimac.shaded.locales.MessageKeyProvider;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

public class ACFBukkitUtil {
   public static String formatLocation(Location loc) {
      return loc == null ? null : loc.getWorld().getName() + ":" + loc.getBlockX() + "," + loc.getBlockY() + "," + loc.getBlockZ();
   }

   public static String color(String message) {
      return ChatColor.translateAlternateColorCodes('&', message);
   }

   /** @deprecated */
   @Deprecated
   public static void sendMsg(CommandSender player, String message) {
      message = color(message);
      String[] var2 = ACFPatterns.NEWLINE.split(message);
      int var3 = var2.length;

      for(int var4 = 0; var4 < var3; ++var4) {
         String msg = var2[var4];
         player.sendMessage(msg);
      }

   }

   public static Location stringToLocation(String storedLoc) {
      return stringToLocation(storedLoc, (World)null);
   }

   public static Location stringToLocation(String storedLoc, World forcedWorld) {
      if (storedLoc == null) {
         return null;
      } else {
         String[] args = ACFPatterns.COLON.split(storedLoc);
         double x;
         double y;
         double z;
         if (args.length < 4 && (args.length != 3 || forcedWorld == null)) {
            if (args.length == 2) {
               String[] args2 = ACFPatterns.COMMA.split(args[1]);
               if (args2.length == 3) {
                  String world = forcedWorld != null ? forcedWorld.getName() : args[0];
                  x = Double.parseDouble(args2[0]);
                  y = Double.parseDouble(args2[1]);
                  z = Double.parseDouble(args2[2]);
                  return new Location(Bukkit.getWorld(world), x, y, z);
               }
            }

            return null;
         } else {
            String world = forcedWorld != null ? forcedWorld.getName() : args[0];
            int i = args.length == 3 ? 0 : 1;
            x = Double.parseDouble(args[i]);
            y = Double.parseDouble(args[i + 1]);
            z = Double.parseDouble(args[i + 2]);
            Location loc = new Location(Bukkit.getWorld(world), x, y, z);
            if (args.length >= 6) {
               loc.setPitch(Float.parseFloat(args[4]));
               loc.setYaw(Float.parseFloat(args[5]));
            }

            return loc;
         }
      }
   }

   public static String fullLocationToString(Location loc) {
      return loc == null ? null : (new StringBuilder(64)).append(loc.getWorld().getName()).append(':').append(ACFUtil.precision(loc.getX(), 4)).append(':').append(ACFUtil.precision(loc.getY(), 4)).append(':').append(ACFUtil.precision(loc.getZ(), 4)).append(':').append(ACFUtil.precision((double)loc.getPitch(), 4)).append(':').append(ACFUtil.precision((double)loc.getYaw(), 4)).toString();
   }

   public static String fullBlockLocationToString(Location loc) {
      return loc == null ? null : (new StringBuilder(64)).append(loc.getWorld().getName()).append(':').append(loc.getBlockX()).append(':').append(loc.getBlockY()).append(':').append(loc.getBlockZ()).append(':').append(ACFUtil.precision((double)loc.getPitch(), 4)).append(':').append(ACFUtil.precision((double)loc.getYaw(), 4)).toString();
   }

   public static String blockLocationToString(Location loc) {
      return loc == null ? null : (new StringBuilder(32)).append(loc.getWorld().getName()).append(':').append(loc.getBlockX()).append(':').append(loc.getBlockY()).append(':').append(loc.getBlockZ()).toString();
   }

   public static double distance(@NotNull Entity e1, @NotNull Entity e2) {
      return distance(e1.getLocation(), e2.getLocation());
   }

   public static double distance2d(@NotNull Entity e1, @NotNull Entity e2) {
      return distance2d(e1.getLocation(), e2.getLocation());
   }

   public static double distance2d(@NotNull Location loc1, @NotNull Location loc2) {
      loc1 = loc1.clone();
      loc1.setY(loc2.getY());
      return distance(loc1, loc2);
   }

   public static double distance(@NotNull Location loc1, @NotNull Location loc2) {
      return loc1.getWorld() != loc2.getWorld() ? 0.0D : loc1.distance(loc2);
   }

   public static Location getTargetLoc(Player player) {
      return getTargetLoc(player, 128);
   }

   public static Location getTargetLoc(Player player, int maxDist) {
      return getTargetLoc(player, maxDist, 1.5D);
   }

   public static Location getTargetLoc(Player player, int maxDist, double addY) {
      try {
         Location target = player.getTargetBlock((Set)null, maxDist).getLocation();
         target.setY(target.getY() + addY);
         return target;
      } catch (Exception var5) {
         return null;
      }
   }

   public static Location getRandLoc(Location loc, int radius) {
      return getRandLoc(loc, radius, radius, radius);
   }

   public static Location getRandLoc(Location loc, int xzRadius, int yRadius) {
      return getRandLoc(loc, xzRadius, yRadius, xzRadius);
   }

   @NotNull
   public static Location getRandLoc(Location loc, int xRadius, int yRadius, int zRadius) {
      Location newLoc = loc.clone();
      newLoc.setX(ACFUtil.rand(loc.getX() - (double)xRadius, loc.getX() + (double)xRadius));
      newLoc.setY(ACFUtil.rand(loc.getY() - (double)yRadius, loc.getY() + (double)yRadius));
      newLoc.setZ(ACFUtil.rand(loc.getZ() - (double)zRadius, loc.getZ() + (double)zRadius));
      return newLoc;
   }

   public static String removeColors(String msg) {
      return ChatColor.stripColor(color(msg));
   }

   public static String replaceChatString(String message, String replace, String with) {
      return replaceChatString(message, Pattern.compile(Pattern.quote(replace), 2), with);
   }

   public static String replaceChatString(String message, Pattern replace, String with) {
      String[] split = replace.split(message + "1");
      if (split.length < 2) {
         return replace.matcher(message).replaceAll(with);
      } else {
         message = split[0];

         for(int i = 1; i < split.length; ++i) {
            String prev = ChatColor.getLastColors(message);
            message = message + with + prev + split[i];
         }

         return message.substring(0, message.length() - 1);
      }
   }

   public static boolean isWithinDistance(@NotNull Player p1, @NotNull Player p2, int dist) {
      return isWithinDistance(p1.getLocation(), p2.getLocation(), dist);
   }

   public static boolean isWithinDistance(@NotNull Location loc1, @NotNull Location loc2, int dist) {
      return loc1.getWorld() == loc2.getWorld() && loc1.distance(loc2) <= (double)dist;
   }

   /** @deprecated */
   public static Player findPlayerSmart(CommandSender requester, String search) {
      CommandManager manager = CommandManager.getCurrentCommandManager();
      if (manager != null) {
         return findPlayerSmart(manager.getCommandIssuer(requester), search);
      } else {
         throw new IllegalStateException("You may not use the ACFBukkitUtil#findPlayerSmart(CommandSender) async to the command execution.");
      }
   }

   public static Player findPlayerSmart(CommandIssuer issuer, String search) {
      CommandSender requester = (CommandSender)issuer.getIssuer();
      if (search == null) {
         return null;
      } else {
         String name = ACFUtil.replace(search, ":confirm", "");
         List<Player> matches = Bukkit.getServer().matchPlayer(name);
         List<Player> confirmList = new ArrayList();
         findMatches(search, requester, matches, confirmList);
         if (matches.size() <= 1 && confirmList.size() <= 1) {
            if (matches.isEmpty()) {
               if (!issuer.getManager().isValidName(name)) {
                  issuer.sendError((MessageKeyProvider)MinecraftMessageKeys.IS_NOT_A_VALID_NAME, "{name}", name);
                  return null;
               } else {
                  Player player = (Player)ACFUtil.getFirstElement(confirmList);
                  if (player == null) {
                     issuer.sendError((MessageKeyProvider)MinecraftMessageKeys.NO_PLAYER_FOUND_SERVER, "{search}", name);
                     return null;
                  } else {
                     issuer.sendInfo((MessageKeyProvider)MinecraftMessageKeys.PLAYER_IS_VANISHED_CONFIRM, "{vanished}", player.getName());
                     return null;
                  }
               }
            } else {
               return (Player)matches.get(0);
            }
         } else {
            String allMatches = (String)matches.stream().map(Player::getName).collect(Collectors.joining(", "));
            issuer.sendError((MessageKeyProvider)MinecraftMessageKeys.MULTIPLE_PLAYERS_MATCH, "{search}", name, "{all}", allMatches);
            return null;
         }
      }
   }

   private static void findMatches(String search, CommandSender requester, List<Player> matches, List<Player> confirmList) {
      Iterator iter = matches.iterator();

      while(iter.hasNext()) {
         Player player = (Player)iter.next();
         if (requester instanceof Player && !((Player)requester).canSee(player)) {
            if (requester.hasPermission("acf.seevanish")) {
               if (!search.endsWith(":confirm")) {
                  confirmList.add(player);
                  iter.remove();
               }
            } else {
               iter.remove();
            }
         }
      }

   }

   public static boolean isValidName(@Nullable String name) {
      return name != null && !name.isEmpty() && ACFPatterns.VALID_NAME_PATTERN.matcher(name).matches();
   }

   static boolean isValidItem(ItemStack item) {
      return item != null && item.getType() != Material.AIR && item.getAmount() > 0;
   }

   public static Locale stringToLocale(String locale) {
      String[] split = ACFPatterns.UNDERSCORE.split(locale);
      return split.length > 1 ? new Locale(split[0], split[1]) : new Locale(split[0]);
   }
}
