package ac.grim.grimac.predictionengine.predictions;

import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.utils.data.VectorData;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;
import org.bukkit.util.Vector;

public class PredictionEngineWaterLegacy extends PredictionEngine {
   double playerGravity;
   float swimmingSpeed;
   float swimmingFriction;
   double lastY;

   public void guessBestMovement(float swimmingSpeed, GrimPlayer player, double playerGravity, float swimmingFriction, double lastY) {
      this.playerGravity = playerGravity;
      this.swimmingSpeed = swimmingSpeed;
      this.swimmingFriction = swimmingFriction;
      this.lastY = lastY;
      super.guessBestMovement(swimmingSpeed, player);
   }

   public Vector getMovementResultFromInput(GrimPlayer player, Vector inputVector, float f, float f2) {
      float lengthSquared = (float)inputVector.lengthSquared();
      if (lengthSquared >= 1.0E-4F) {
         lengthSquared = (float)Math.sqrt((double)lengthSquared);
         if (lengthSquared < 1.0F) {
            lengthSquared = 1.0F;
         }

         lengthSquared = this.swimmingSpeed / lengthSquared;
         inputVector.multiply(lengthSquared);
         float sinResult = player.trigHandler.sin(player.xRot * 0.017453292F);
         float cosResult = player.trigHandler.cos(player.xRot * 0.017453292F);
         return new Vector(inputVector.getX() * (double)cosResult - inputVector.getZ() * (double)sinResult, inputVector.getY(), inputVector.getZ() * (double)cosResult + inputVector.getX() * (double)sinResult);
      } else {
         return new Vector();
      }
   }

   public void addJumpsToPossibilities(GrimPlayer player, Set<VectorData> existingVelocities) {
      Iterator var3 = (new HashSet(existingVelocities)).iterator();

      while(var3.hasNext()) {
         VectorData vector = (VectorData)var3.next();
         existingVelocities.add(new VectorData(vector.vector.clone().add(new Vector(0.0F, 0.04F, 0.0F)), vector, VectorData.VectorType.Jump));
         if (player.skippedTickInActualMovement) {
            existingVelocities.add(new VectorData(vector.vector.clone().add(new Vector(0.0F, 0.02F, 0.0F)), vector, VectorData.VectorType.Jump));
         }
      }

   }

   public void endOfTick(GrimPlayer player, double playerGravity) {
      super.endOfTick(player, playerGravity);
      Iterator var4 = player.getPossibleVelocitiesMinusKnockback().iterator();

      while(var4.hasNext()) {
         VectorData vector = (VectorData)var4.next();
         vector.vector.multiply(new Vector(this.swimmingFriction, 0.8F, this.swimmingFriction));
         vector.vector.setY(vector.vector.getY() - 0.02D);
      }

   }
}
