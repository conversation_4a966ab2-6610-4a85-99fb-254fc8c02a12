package ac.grim.grimac.events.packets;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.PacketEvents;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketListenerAbstract;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketListenerPriority;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketReceiveEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.manager.server.ServerVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.packettype.PacketType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.ClientVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientPlayerInput;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientSteerVehicle;
import ac.grim.grimac.utils.collisions.datatypes.SimpleCollisionBox;
import ac.grim.grimac.utils.data.KnownInput;
import ac.grim.grimac.utils.data.packetentity.PacketEntity;
import ac.grim.grimac.utils.data.packetentity.PacketEntityHorse;

public class PacketPlayerSteer extends PacketListenerAbstract {
   public PacketPlayerSteer() {
      super(PacketListenerPriority.LOW);
   }

   public void onPacketReceive(PacketReceiveEvent event) {
      GrimPlayer player;
      if (event.getPacketType() == PacketType.Play.Client.STEER_VEHICLE) {
         player = GrimAPI.INSTANCE.getPlayerDataManager().getPlayer(event.getUser());
         if (player == null) {
            return;
         }

         WrapperPlayClientSteerVehicle steer = new WrapperPlayClientSteerVehicle(event);
         float forwards = steer.getForward();
         float sideways = steer.getSideways();
         player.vehicleData.nextVehicleForward = forwards;
         player.vehicleData.nextVehicleHorizontal = sideways;
         PacketEntity riding = player.compensatedEntities.self.getRiding();
         if (player.packetStateData.receivedSteerVehicle && riding != null) {
            if ((riding.isBoat() || riding instanceof PacketEntityHorse) && riding.passengers.get(0) == player.compensatedEntities.self && player.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_9) && PacketEvents.getAPI().getServerManager().getVersion().isNewerThanOrEquals(ServerVersion.V_1_9)) {
               return;
            }

            player.compensatedWorld.tickPlayerInPistonPushingArea();
            player.compensatedEntities.tick();
            player.vehicleData.lastDummy = true;
            int controllingEntityId = player.inVehicle() ? player.getRidingVehicleId() : player.entityID;
            player.firstBreadKB = player.checkManager.getKnockbackHandler().calculateFirstBreadKnockback(controllingEntityId, player.lastTransactionReceived.get());
            player.likelyKB = player.checkManager.getKnockbackHandler().calculateRequiredKB(controllingEntityId, player.lastTransactionReceived.get(), false);
            if (player.firstBreadKB != null) {
               player.clientVelocity = player.firstBreadKB.vector;
            }

            if (player.likelyKB != null) {
               player.clientVelocity = player.likelyKB.vector;
            }

            player.firstBreadExplosion = player.checkManager.getExplosionHandler().getFirstBreadAddedExplosion(player.lastTransactionReceived.get());
            player.likelyExplosions = player.checkManager.getExplosionHandler().getPossibleExplosions(player.lastTransactionReceived.get(), false);
            player.checkManager.getExplosionHandler().forceExempt();
            player.checkManager.getKnockbackHandler().forceExempt();
            player.lastX = player.x;
            player.lastY = player.y;
            player.lastZ = player.z;
            SimpleCollisionBox vehiclePos = player.compensatedEntities.self.getRiding().getPossibleCollisionBoxes();
            player.x = (vehiclePos.minX + vehiclePos.maxX) / 2.0D;
            player.y = (vehiclePos.minY + vehiclePos.maxY) / 2.0D;
            player.z = (vehiclePos.minZ + vehiclePos.maxZ) / 2.0D;
            if (player.isSprinting != player.lastSprinting) {
               player.compensatedEntities.hasSprintingAttributeEnabled = player.isSprinting;
            }

            player.lastSprinting = player.isSprinting;
         }

         player.packetStateData.receivedSteerVehicle = true;
      } else if (event.getPacketType() == PacketType.Play.Client.PLAYER_INPUT) {
         player = GrimAPI.INSTANCE.getPlayerDataManager().getPlayer(event.getUser());
         if (player == null) {
            return;
         }

         WrapperPlayClientPlayerInput input = new WrapperPlayClientPlayerInput(event);
         byte forward = 0;
         byte sideways = 0;
         if (input.isForward()) {
            ++forward;
         }

         if (input.isBackward()) {
            --forward;
         }

         if (input.isLeft()) {
            ++sideways;
         }

         if (input.isRight()) {
            --sideways;
         }

         player.vehicleData.nextVehicleForward = (float)forward * 0.98F;
         player.vehicleData.nextVehicleHorizontal = (float)sideways * 0.98F;
         player.packetStateData.knownInput = new KnownInput(input.isForward(), input.isBackward(), input.isLeft(), input.isRight(), input.isJump(), input.isShift(), input.isSprint());
      }

   }
}
