package ac.grim.grimac.manager.config;

import ac.grim.grimac.api.config.ConfigManager;
import ac.grim.grimac.utils.anticheat.LogUtil;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

public class BaseConfigManager {
   private ConfigManager config = null;
   private boolean printAlertsToConsole = false;
   private String prefix = "&bGrim &8»";
   private String disconnectTimeout;
   private String disconnectClosed;
   private String disconnectPacketError;
   private final List<Pattern> ignoredClientPatterns = new ArrayList();

   public void load(ConfigManager config) {
      this.config = config;
      int configuredMaxTransactionTime = config.getIntElse("max-transaction-time", 60);
      if (configuredMaxTransactionTime > 180 || configuredMaxTransactionTime < 1) {
         LogUtil.warn("Detected invalid max-transaction-time! This setting is clamped between 1 and 180 to prevent issues. Attempting to disable or set this too high can result in memory usage issues.");
      }

      this.ignoredClientPatterns.clear();
      Iterator var3 = config.getStringList("client-brand.ignored-clients").iterator();

      while(var3.hasNext()) {
         String string = (String)var3.next();

         try {
            this.ignoredClientPatterns.add(Pattern.compile(string));
         } catch (PatternSyntaxException var6) {
            throw new RuntimeException("Failed to compile client pattern", var6);
         }
      }

      this.printAlertsToConsole = config.getBooleanElse("alerts.print-to-console", true);
      this.prefix = config.getStringElse("prefix", "&bGrim &8»");
      this.disconnectTimeout = config.getStringElse("disconnect.timeout", "<lang:disconnect.timeout>");
      this.disconnectClosed = config.getStringElse("disconnect.closed", "<lang:disconnect.timeout>");
      this.disconnectPacketError = config.getStringElse("disconnect.error", "<red>An error occurred whilst processing packets. Please contact the administrators.");
   }

   public void start() {
   }

   public boolean isIgnoredClient(String brand) {
      Iterator var2 = this.ignoredClientPatterns.iterator();

      Pattern pattern;
      do {
         if (!var2.hasNext()) {
            return false;
         }

         pattern = (Pattern)var2.next();
      } while(!pattern.matcher(brand).find());

      return true;
   }

   public ConfigManager getConfig() {
      return this.config;
   }

   public boolean isPrintAlertsToConsole() {
      return this.printAlertsToConsole;
   }

   public String getPrefix() {
      return this.prefix;
   }

   public String getDisconnectTimeout() {
      return this.disconnectTimeout;
   }

   public String getDisconnectClosed() {
      return this.disconnectClosed;
   }

   public String getDisconnectPacketError() {
      return this.disconnectPacketError;
   }
}
