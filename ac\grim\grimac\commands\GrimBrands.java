package ac.grim.grimac.commands;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.shaded.acf.BaseCommand;
import ac.grim.grimac.shaded.acf.annotation.CommandAlias;
import ac.grim.grimac.shaded.acf.annotation.CommandPermission;
import ac.grim.grimac.shaded.acf.annotation.Subcommand;
import org.bukkit.entity.Player;

@CommandAlias("grim|grimac")
public class GrimBrands extends BaseCommand {
   @Subcommand("brands")
   @CommandPermission("grim.brand")
   public void onBrands(Player player) {
      GrimAPI.INSTANCE.getAlertManager().toggleBrands(player);
   }
}
