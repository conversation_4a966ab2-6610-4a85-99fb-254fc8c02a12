package ac.grim.grimac.utils.ai.models;

import java.util.HashMap;
import java.util.Map;

/**
 * Training data for AI model improvement
 */
public class AITrainingData {
    private final PlayerBehaviorData playerData;
    private final String behaviorType;
    private final Object behaviorData;
    private final AIDetectionResult result;
    private final long timestamp;
    
    // Additional context for training
    private final Map<String, Object> context;
    
    public AITrainingData(PlayerBehaviorData playerData, String behaviorType, 
                         Object behaviorData, AIDetectionResult result) {
        this.playerData = playerData;
        this.behaviorType = behaviorType;
        this.behaviorData = behaviorData;
        this.result = result;
        this.timestamp = System.currentTimeMillis();
        this.context = new HashMap<>();
        
        // Add contextual information
        addContext();
    }
    
    private void addContext() {
        if (playerData != null) {
            context.put("sessionDuration", playerData.getSessionDurationMs());
            context.put("totalPackets", playerData.getTotalPacketsReceived());
            context.put("suspiciousEvents", playerData.getSuspiciousEvents());
            context.put("violationCount", playerData.getViolationHistory().size());
            context.put("recentPatterns", playerData.getRecentPatterns());
        }
        
        context.put("behaviorType", behaviorType);
        context.put("timestamp", timestamp);
        
        if (result != null) {
            context.put("resultConfidence", result.getConfidence());
            context.put("resultSeverity", result.getSeverity());
            context.put("resultCheatType", result.getCheatType());
        }
    }
    
    // Getters
    public PlayerBehaviorData getPlayerData() { return playerData; }
    public String getBehaviorType() { return behaviorType; }
    public Object getBehaviorData() { return behaviorData; }
    public AIDetectionResult getResult() { return result; }
    public long getTimestamp() { return timestamp; }
    public Map<String, Object> getContext() { return new HashMap<>(context); }
    
    // Utility methods
    public boolean isPositiveExample() {
        return result != null && result.isSuspicious();
    }
    
    public boolean isNegativeExample() {
        return result != null && !result.isSuspicious() && !result.isError();
    }
    
    public boolean isHighConfidence() {
        return result != null && result.getConfidence() >= 0.8;
    }
    
    public String getTrainingLabel() {
        if (result == null || result.isError()) {
            return "UNKNOWN";
        }
        return result.isSuspicious() ? "SUSPICIOUS" : "NORMAL";
    }
    
    public Map<String, Object> toTrainingMap() {
        Map<String, Object> trainingMap = new HashMap<>();
        
        // Player context
        if (playerData != null) {
            trainingMap.put("playerId", playerData.getPlayerId().toString());
            trainingMap.put("playerName", playerData.getPlayerName());
            trainingMap.put("sessionDuration", playerData.getSessionDurationMs());
            trainingMap.put("totalPackets", playerData.getTotalPacketsReceived());
            trainingMap.put("suspiciousEvents", playerData.getSuspiciousEvents());
            trainingMap.put("suspiciousRate", playerData.getSuspiciousEventRate());
            
            // Movement statistics
            if (playerData.getMovementStats() != null) {
                trainingMap.put("avgSpeed", playerData.getMovementStats().getAverageSpeed());
                trainingMap.put("maxSpeed", playerData.getMovementStats().getMaxSpeed());
                trainingMap.put("jumpCount", playerData.getMovementStats().getJumpCount());
            }
            
            // Combat statistics
            if (playerData.getCombatStats() != null) {
                trainingMap.put("combatAccuracy", playerData.getCombatStats().getAverageAccuracy());
                trainingMap.put("avgReach", playerData.getCombatStats().getAverageReachDistance());
                trainingMap.put("totalAttacks", playerData.getCombatStats().getTotalAttacks());
            }
            
            // Building statistics
            if (playerData.getBuildingStats() != null) {
                trainingMap.put("buildSpeed", playerData.getBuildingStats().getAverageBuildSpeed());
                trainingMap.put("totalBlocks", playerData.getBuildingStats().getTotalBlocks());
            }
        }
        
        // Behavior data
        trainingMap.put("behaviorType", behaviorType);
        trainingMap.put("behaviorData", behaviorData);
        
        // Result data
        if (result != null) {
            trainingMap.put("label", getTrainingLabel());
            trainingMap.put("suspicious", result.isSuspicious());
            trainingMap.put("confidence", result.getConfidence());
            trainingMap.put("reasoning", result.getReasoning());
            trainingMap.put("cheatType", result.getCheatType());
            trainingMap.put("severity", result.getSeverity());
        }
        
        // Context
        trainingMap.putAll(context);
        
        return trainingMap;
    }
    
    @Override
    public String toString() {
        return String.format("AITrainingData{player=%s, behavior=%s, label=%s, confidence=%.2f}", 
                           playerData != null ? playerData.getPlayerName() : "unknown",
                           behaviorType, getTrainingLabel(), 
                           result != null ? result.getConfidence() : 0.0);
    }
}
