package ac.grim.grimac.manager.player.features.types;

import ac.grim.grimac.api.config.ConfigManager;
import ac.grim.grimac.api.feature.FeatureState;
import ac.grim.grimac.player.GrimPlayer;

public abstract class GrimFeature {
   private final String name;

   public abstract void setState(GrimPlayer var1, ConfigManager var2, FeatureState var3);

   public abstract boolean isEnabled(GrimPlayer var1);

   public abstract boolean isEnabledInConfig(GrimPlayer var1, ConfigManager var2);

   public GrimFeature(String name) {
      this.name = name;
   }

   public String getName() {
      return this.name;
   }
}
