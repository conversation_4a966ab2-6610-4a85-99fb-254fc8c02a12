package ac.grim.grimac.checks.impl.scaffolding;

import ac.grim.grimac.checks.CheckData;
import ac.grim.grimac.checks.type.BlockPlaceCheck;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.attribute.Attributes;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.GameMode;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.world.states.type.StateTypes;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.util.Vector3i;
import ac.grim.grimac.utils.anticheat.update.BlockPlace;
import ac.grim.grimac.utils.collisions.datatypes.SimpleCollisionBox;
import ac.grim.grimac.utils.math.VectorUtils;
import org.bukkit.util.Vector;

@CheckData(
   name = "FarPlace",
   description = "Placing blocks from too far away"
)
public class FarPlace extends BlockPlaceCheck {
   public FarPlace(GrimPlayer player) {
      super(player);
   }

   public void onBlockPlace(BlockPlace place) {
      if (this.player.gamemode != GameMode.SPECTATOR) {
         Vector3i blockPos = place.getPlacedAgainstBlockLocation();
         if (place.getMaterial() != StateTypes.SCAFFOLDING) {
            double min = Double.MAX_VALUE;
            double[] possibleEyeHeights = this.player.getPossibleEyeHeights();
            double[] var6 = possibleEyeHeights;
            int var7 = possibleEyeHeights.length;

            for(int var8 = 0; var8 < var7; ++var8) {
               double d = var6[var8];
               SimpleCollisionBox box = new SimpleCollisionBox(blockPos);
               Vector eyes = new Vector(this.player.x, this.player.y + d, this.player.z);
               Vector best = VectorUtils.cutBoxToVector(eyes, box);
               min = Math.min(min, eyes.distanceSquared(best));
            }

            double maxReach = this.player.compensatedEntities.self.getAttributeValue(Attributes.BLOCK_INTERACTION_RANGE);
            double threshold = this.player.getMovementThreshold();
            maxReach += Math.hypot(threshold, threshold);
            if (min > maxReach * maxReach && this.flagAndAlert() && this.shouldModifyPackets() && this.shouldCancel()) {
               place.resync();
            }

         }
      }
   }
}
