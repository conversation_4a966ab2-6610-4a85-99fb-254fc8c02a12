package ac.grim.grimac.predictionengine.predictions.rideable;

import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.predictionengine.predictions.PredictionEngine;
import ac.grim.grimac.predictionengine.predictions.PredictionEngineNormal;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.attribute.Attributes;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.potion.PotionTypes;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.util.Vector3d;
import ac.grim.grimac.utils.data.VectorData;
import ac.grim.grimac.utils.data.packetentity.PacketEntity;
import ac.grim.grimac.utils.data.packetentity.PacketEntityCamel;
import ac.grim.grimac.utils.data.packetentity.PacketEntityHorse;
import ac.grim.grimac.utils.nmsutil.BlockProperties;
import ac.grim.grimac.utils.nmsutil.JumpPower;
import ac.grim.grimac.utils.nmsutil.ReachUtils;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.OptionalInt;
import java.util.Set;
import org.bukkit.util.Vector;

public final class PredictionEngineRideableUtils {
   public static Set<VectorData> handleJumps(GrimPlayer player, Set<VectorData> possibleVectors) {
      PacketEntity var3 = player.compensatedEntities.self.getRiding();
      if (var3 instanceof PacketEntityHorse) {
         PacketEntityHorse horse = (PacketEntityHorse)var3;
         if (horse instanceof PacketEntityCamel) {
            PacketEntityCamel camel = (PacketEntityCamel)horse;
            handleCamelDash(player, possibleVectors, camel);
         } else {
            handleHorseJumping(player, possibleVectors, horse);
         }

         if (player.lastOnGround) {
            player.vehicleData.horseJump = 0.0F;
            player.vehicleData.horseJumping = false;
         }

         return possibleVectors;
      } else {
         return possibleVectors;
      }
   }

   private static void handleCamelDash(GrimPlayer player, Set<VectorData> possibleVectors, PacketEntityCamel camel) {
      boolean wantsToJump = player.vehicleData.horseJump > 0.0F && !player.vehicleData.horseJumping && player.lastOnGround;
      if (wantsToJump) {
         double jumpFactor = camel.getAttributeValue(Attributes.JUMP_STRENGTH) * (double)JumpPower.getPlayerJumpFactor(player);
         OptionalInt jumpBoost = player.compensatedEntities.getPotionLevelForPlayer(PotionTypes.JUMP_BOOST);
         double jumpYVelocity;
         if (jumpBoost.isPresent()) {
            jumpYVelocity = jumpFactor + (double)((float)(jumpBoost.getAsInt() + 1) * 0.1F);
         } else {
            jumpYVelocity = jumpFactor;
         }

         double multiplier = (double)(22.2222F * player.vehicleData.horseJump) * camel.getAttributeValue(Attributes.MOVEMENT_SPEED) * (double)BlockProperties.getBlockSpeedFactor(player, player.mainSupportingBlockData, new Vector3d(player.lastX, player.lastY, player.lastZ));
         Vector jumpVelocity = ReachUtils.getLook(player, player.xRot, player.yRot).multiply(new Vector(1.0D, 0.0D, 1.0D)).normalize().multiply(multiplier).add(new Vector(0.0D, (double)(1.4285F * player.vehicleData.horseJump) * jumpYVelocity, 0.0D));
         Iterator var12 = possibleVectors.iterator();

         while(var12.hasNext()) {
            VectorData vectorData = (VectorData)var12.next();
            vectorData.vector.add(jumpVelocity);
         }

         player.vehicleData.horseJumping = true;
         player.vehicleData.camelDashCooldown = 55;
      }
   }

   private static void handleHorseJumping(GrimPlayer player, Set<VectorData> possibleVectors, PacketEntityHorse horse) {
      boolean wantsToJump = player.vehicleData.horseJump > 0.0F && !player.vehicleData.horseJumping && player.lastOnGround;
      if (wantsToJump) {
         float forwardInput = player.vehicleData.vehicleForward;
         if (forwardInput <= 0.0F) {
            forwardInput *= 0.25F;
         }

         double jumpFactor = horse.getAttributeValue(Attributes.JUMP_STRENGTH) * (double)player.vehicleData.horseJump * (double)JumpPower.getPlayerJumpFactor(player);
         OptionalInt jumpBoost = player.compensatedEntities.getPotionLevelForPlayer(PotionTypes.JUMP_BOOST);
         double jumpVelocity;
         if (jumpBoost.isPresent()) {
            jumpVelocity = jumpFactor + (double)((float)(jumpBoost.getAsInt() + 1) * 0.1F);
         } else {
            jumpVelocity = jumpFactor;
         }

         player.vehicleData.horseJumping = true;
         float f2 = player.trigHandler.sin(player.xRot * 0.017453292F);
         float f3 = player.trigHandler.cos(player.xRot * 0.017453292F);
         Iterator var12 = possibleVectors.iterator();

         while(var12.hasNext()) {
            VectorData vectorData = (VectorData)var12.next();
            vectorData.vector.setY(jumpVelocity);
            if (forwardInput > 0.0F) {
               vectorData.vector.add(new Vector((double)(-0.4F * f2 * player.vehicleData.horseJump), 0.0D, (double)(0.4F * f3 * player.vehicleData.horseJump)));
            }
         }

         player.vehicleData.horseJump = 0.0F;
      }
   }

   public static List<VectorData> applyInputsToVelocityPossibilities(Vector movementVector, GrimPlayer player, Set<VectorData> possibleVectors, float speed) {
      List<VectorData> returnVectors = new ArrayList();
      Iterator var5 = possibleVectors.iterator();

      while(var5.hasNext()) {
         VectorData possibleLastTickOutput = (VectorData)var5.next();
         VectorData result = new VectorData(possibleLastTickOutput.vector.clone().add((new PredictionEngine()).getMovementResultFromInput(player, movementVector, speed, player.xRot)), possibleLastTickOutput, VectorData.VectorType.InputResult);
         result = result.returnNewModified(result.vector.clone().multiply(player.stuckSpeedMultiplier), VectorData.VectorType.StuckMultiplier);
         result = result.returnNewModified((new PredictionEngineNormal()).handleOnClimbable(result.vector.clone(), player), VectorData.VectorType.Climbable);
         returnVectors.add(result);
         result = new VectorData(possibleLastTickOutput.vector.clone(), possibleLastTickOutput, VectorData.VectorType.InputResult);
         result = result.returnNewModified(result.vector.clone().multiply(player.stuckSpeedMultiplier), VectorData.VectorType.StuckMultiplier);
         result = result.returnNewModified((new PredictionEngineNormal()).handleOnClimbable(result.vector.clone(), player), VectorData.VectorType.Climbable);
         returnVectors.add(result);
      }

      return returnVectors;
   }
}
