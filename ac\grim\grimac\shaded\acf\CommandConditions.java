package ac.grim.grimac.shaded.acf;

import ac.grim.grimac.shaded.acf.lib.util.Table;
import ac.grim.grimac.shaded.jetbrains.annotations.NotNull;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

public class CommandConditions<I extends CommandIssuer, CEC extends CommandExecutionContext<CEC, I>, CC extends ConditionContext<I>> {
   private CommandManager manager;
   private Map<String, CommandConditions.Condition<I>> conditions = new HashMap();
   private Table<Class<?>, String, CommandConditions.ParameterCondition<?, ?, ?>> paramConditions = new Table();

   CommandConditions(CommandManager manager) {
      this.manager = manager;
   }

   public CommandConditions.Condition<I> addCondition(@NotNull String id, @NotNull CommandConditions.Condition<I> handler) {
      return (CommandConditions.Condition)this.conditions.put(id.toLowerCase(Locale.ENGLISH), handler);
   }

   public <P> CommandConditions.ParameterCondition addCondition(Class<P> clazz, @NotNull String id, @NotNull CommandConditions.ParameterCondition<P, CEC, I> handler) {
      return (CommandConditions.ParameterCondition)this.paramConditions.put(clazz, id.toLowerCase(Locale.ENGLISH), handler);
   }

   void validateConditions(CommandOperationContext context) throws InvalidCommandArgument {
      RegisteredCommand cmd = context.getRegisteredCommand();
      this.validateConditions(cmd.conditions, context);
      this.validateConditions(cmd.scope, context);
   }

   private void validateConditions(BaseCommand scope, CommandOperationContext operationContext) throws InvalidCommandArgument {
      this.validateConditions(scope.conditions, operationContext);
      if (scope.parentCommand != null) {
         this.validateConditions(scope.parentCommand, operationContext);
      }

   }

   private void validateConditions(String conditions, CommandOperationContext context) throws InvalidCommandArgument {
      if (conditions != null) {
         conditions = this.manager.getCommandReplacements().replace(conditions);
         CommandIssuer issuer = context.getCommandIssuer();
         String[] var4 = ACFPatterns.PIPE.split(conditions);
         int var5 = var4.length;

         for(int var6 = 0; var6 < var5; ++var6) {
            String cond = var4[var6];
            String[] split = ACFPatterns.COLON.split(cond, 2);
            String id = split[0].toLowerCase(Locale.ENGLISH);
            CommandConditions.Condition<I> condition = (CommandConditions.Condition)this.conditions.get(id);
            if (condition == null) {
               RegisteredCommand cmd = context.getRegisteredCommand();
               this.manager.log(LogLevel.ERROR, "Could not find command condition " + id + " for " + cmd.method.getName());
            } else {
               String config = split.length == 2 ? split[1] : null;
               CC conditionContext = this.manager.createConditionContext(issuer, config);
               condition.validateCondition(conditionContext);
            }
         }

      }
   }

   void validateConditions(CEC execContext, Object value) throws InvalidCommandArgument {
      String conditions = execContext.getCommandParameter().getConditions();
      if (conditions != null) {
         conditions = this.manager.getCommandReplacements().replace(conditions);
         I issuer = execContext.getIssuer();
         String[] var5 = ACFPatterns.PIPE.split(conditions);
         int var6 = var5.length;

         for(int var7 = 0; var7 < var6; ++var7) {
            String cond = var5[var7];
            String[] split = ACFPatterns.COLON.split(cond, 2);
            Class<?> cls = execContext.getParam().getType();
            String id = split[0].toLowerCase(Locale.ENGLISH);

            CommandConditions.ParameterCondition condition;
            do {
               condition = (CommandConditions.ParameterCondition)this.paramConditions.get(cls, id);
               if (condition != null || cls.getSuperclass() == null || cls.getSuperclass() == Object.class) {
                  break;
               }

               cls = cls.getSuperclass();
            } while(cls != null);

            if (condition == null) {
               RegisteredCommand cmd = execContext.getCmd();
               this.manager.log(LogLevel.ERROR, "Could not find command condition " + id + " for " + cmd.method.getName() + "::" + execContext.getParam().getName());
            } else {
               String config = split.length == 2 ? split[1] : null;
               CC conditionContext = this.manager.createConditionContext(issuer, config);
               condition.validateCondition(conditionContext, execContext, value);
            }
         }

      }
   }

   public interface Condition<I extends CommandIssuer> {
      void validateCondition(ConditionContext<I> context) throws InvalidCommandArgument;
   }

   public interface ParameterCondition<P, CEC extends CommandExecutionContext, I extends CommandIssuer> {
      void validateCondition(ConditionContext<I> context, CEC execContext, P value) throws InvalidCommandArgument;
   }
}
