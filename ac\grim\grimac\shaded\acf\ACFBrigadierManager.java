package ac.grim.grimac.shaded.acf;

import com.mojang.brigadier.Command;
import com.mojang.brigadier.arguments.ArgumentType;
import com.mojang.brigadier.arguments.BoolArgumentType;
import com.mojang.brigadier.arguments.DoubleArgumentType;
import com.mojang.brigadier.arguments.FloatArgumentType;
import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.builder.LiteralArgumentBuilder;
import com.mojang.brigadier.builder.RequiredArgumentBuilder;
import com.mojang.brigadier.suggestion.SuggestionProvider;
import com.mojang.brigadier.tree.CommandNode;
import com.mojang.brigadier.tree.LiteralCommandNode;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.function.BiPredicate;
import java.util.function.Predicate;

/** @deprecated */
@Deprecated
@UnstableAPI
public class ACFBrigadierManager<S> {
   protected final CommandManager<?, ?, ?, ?, ?, ?> manager;
   private final Map<Class<?>, ArgumentType<?>> arguments = new HashMap();

   ACFBrigadierManager(CommandManager<?, ?, ?, ?, ?, ?> manager) {
      manager.verifyUnstableAPI("brigadier");
      this.manager = manager;
      this.registerArgument(String.class, StringArgumentType.word());
      this.registerArgument(Float.TYPE, FloatArgumentType.floatArg());
      this.registerArgument(Float.class, FloatArgumentType.floatArg());
      this.registerArgument(Double.TYPE, DoubleArgumentType.doubleArg());
      this.registerArgument(Double.class, DoubleArgumentType.doubleArg());
      this.registerArgument(Boolean.TYPE, BoolArgumentType.bool());
      this.registerArgument(Boolean.class, BoolArgumentType.bool());
      this.registerArgument(Integer.TYPE, IntegerArgumentType.integer());
      this.registerArgument(Integer.class, IntegerArgumentType.integer());
      this.registerArgument(Long.TYPE, IntegerArgumentType.integer());
      this.registerArgument(Long.class, IntegerArgumentType.integer());
   }

   <T> void registerArgument(Class<T> clazz, ArgumentType<?> type) {
      this.arguments.put(clazz, type);
   }

   ArgumentType<Object> getArgumentTypeByClazz(CommandParameter param) {
      return (ArgumentType)(param.consumesRest ? StringArgumentType.greedyString() : (ArgumentType)this.arguments.getOrDefault(param.getType(), StringArgumentType.string()));
   }

   LiteralCommandNode<S> register(RootCommand rootCommand, LiteralCommandNode<S> root, SuggestionProvider<S> suggestionProvider, Command<S> executor, BiPredicate<RootCommand, S> permCheckerRoot, BiPredicate<RegisteredCommand, S> permCheckerSub) {
      LiteralArgumentBuilder<S> rootBuilder = (LiteralArgumentBuilder)LiteralArgumentBuilder.literal(root.getLiteral()).requires((sender) -> {
         return permCheckerRoot.test(rootCommand, sender);
      });
      RegisteredCommand defaultCommand = rootCommand.getDefaultRegisteredCommand();
      if (defaultCommand != null && defaultCommand.requiredResolvers == 0) {
         rootBuilder.executes(executor);
      }

      root = rootBuilder.build();
      boolean isForwardingCommand = rootCommand.getDefCommand() instanceof ForwardingCommand;
      if (defaultCommand != null) {
         this.registerParameters(defaultCommand, root, suggestionProvider, executor, permCheckerSub);
      }

      Iterator var10 = rootCommand.getSubCommands().entries().iterator();

      while(true) {
         Entry subCommand;
         do {
            do {
               if (!var10.hasNext()) {
                  return root;
               }

               subCommand = (Entry)var10.next();
            } while(BaseCommand.isSpecialSubcommand((String)subCommand.getKey()) && !isForwardingCommand);
         } while(!((String)subCommand.getKey()).equals("help") && ((RegisteredCommand)subCommand.getValue()).prefSubCommand.equals("help"));

         String commandName = (String)subCommand.getKey();
         CommandNode<S> currentParent = root;
         Predicate<S> subPermChecker = (sender) -> {
            return permCheckerSub.test((RegisteredCommand)subCommand.getValue(), sender);
         };
         Object subCommandNode;
         if (isForwardingCommand) {
            subCommandNode = root;
         } else {
            if (commandName.contains(" ")) {
               String[] split = ACFPatterns.SPACE.split(commandName);

               for(int i = 0; i < split.length - 1; ++i) {
                  if (((CommandNode)currentParent).getChild(split[i]) == null) {
                     LiteralCommandNode<S> sub = ((LiteralArgumentBuilder)LiteralArgumentBuilder.literal(split[i]).requires(subPermChecker)).build();
                     ((CommandNode)currentParent).addChild(sub);
                     currentParent = sub;
                  } else {
                     currentParent = ((CommandNode)currentParent).getChild(split[i]);
                  }
               }

               commandName = split[split.length - 1];
            }

            subCommandNode = ((CommandNode)currentParent).getChild(commandName);
            if (subCommandNode == null) {
               LiteralArgumentBuilder<S> argumentBuilder = (LiteralArgumentBuilder)LiteralArgumentBuilder.literal(commandName).requires(subPermChecker);
               if (((RegisteredCommand)subCommand.getValue()).requiredResolvers == 0) {
                  argumentBuilder.executes(executor);
               }

               subCommandNode = argumentBuilder.build();
            }
         }

         this.registerParameters((RegisteredCommand)subCommand.getValue(), (CommandNode)subCommandNode, suggestionProvider, executor, permCheckerSub);
         if (!isForwardingCommand) {
            ((CommandNode)currentParent).addChild((CommandNode)subCommandNode);
         }
      }
   }

   void registerParameters(RegisteredCommand command, CommandNode<S> node, SuggestionProvider<S> suggestionProvider, Command<S> executor, BiPredicate<RegisteredCommand, S> permChecker) {
      for(int i = 0; i < command.parameters.length; ++i) {
         CommandParameter param = command.parameters[i];
         CommandParameter nextParam = param.getNextParam();
         if (!param.isCommandIssuer() && (!param.canExecuteWithoutInput() || nextParam == null || nextParam.canExecuteWithoutInput())) {
            RequiredArgumentBuilder<S, Object> builder = (RequiredArgumentBuilder)RequiredArgumentBuilder.argument(param.getName(), this.getArgumentTypeByClazz(param)).suggests(suggestionProvider).requires((sender) -> {
               return permChecker.test(command, sender);
            });
            if (nextParam == null || nextParam.canExecuteWithoutInput()) {
               builder.executes(executor);
            }

            CommandNode<S> subSubCommand = builder.build();
            ((CommandNode)node).addChild(subSubCommand);
            node = subSubCommand;
         }
      }

   }
}
