# GrimAC AI Enhancement

This project adds comprehensive AI-powered anti-cheat capabilities to GrimAC, leveraging OpenAI's GPT models for advanced behavior analysis and pattern recognition.

## 🚀 Features

### Core AI Infrastructure
- **AIManager**: Central management system for all AI operations
- **OpenAI Integration**: Direct integration with OpenAI's API for behavior analysis
- **Advanced Configuration**: Comprehensive configuration system with adaptive thresholds
- **Data Collection**: Intelligent behavior data collection and processing
- **Training System**: Continuous learning and model improvement

### AI-Powered Checks
- **Movement Analysis**: AI-powered movement pattern detection
- **Combat Detection**: Advanced combat behavior analysis
- **Exploit Recognition**: Learning-based exploit pattern detection
- **Building Analysis**: Intelligent building pattern recognition

### Learning & Adaptation
- **Adaptive Thresholds**: Self-adjusting detection thresholds based on performance
- **Pattern Learning**: Continuous pattern recognition improvement
- **Feedback Loops**: Learning from false positives and negatives
- **Performance Optimization**: Automatic optimization based on server performance

## 📋 Requirements

### Dependencies
- **Java 11+**: Required for compilation and runtime
- **OpenAI API Key**: Required for AI functionality
- **Bukkit/Spigot API**: For Minecraft server integration
- **Gson**: For JSON processing
- **PacketEvents**: For packet analysis

### Server Requirements
- **Minimum RAM**: 4GB (8GB+ recommended for AI features)
- **CPU**: Multi-core processor recommended
- **Network**: Stable internet connection for OpenAI API calls

## 🛠️ Installation

### 1. Obtain Dependencies
Create a `lib` directory and add the following JAR files:
```
lib/
├── bukkit-api.jar (or spigot-api.jar)
├── gson.jar
├── packetevents.jar
└── [other GrimAC dependencies]
```

### 2. Configure AI Settings
Edit `config/ai.yml`:
```yaml
ai:
  enabled: true
  training-mode: true
  openai:
    api-key: "your-openai-api-key-here"
    model: "gpt-4"
  thresholds:
    movement: 0.7
    combat: 0.8
    exploit: 0.9
    building: 0.6
```

### 3. Get OpenAI API Key
1. Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. Create an account and generate an API key
3. Add the key to your `ai.yml` configuration

### 4. Compile (if building from source)
```bash
# Windows
compile-ai.bat

# Linux/Mac
chmod +x compile-ai.sh
./compile-ai.sh
```

## 🎮 Usage

### AI Commands
All AI commands require the `grim.ai` permission.

```
/grimai status                 - Show AI system status
/grimai enable                 - Enable AI system
/grimai disable                - Disable AI system
/grimai training enable        - Enable training mode
/grimai training disable       - Disable training mode
/grimai training start         - Start manual training session
/grimai training clear         - Clear training data
/grimai analyze <player>       - Analyze specific player with AI
/grimai stats                  - Show detailed AI statistics
/grimai reload                 - Reload AI configuration
```

### Configuration Options

#### Core Settings
```yaml
ai:
  enabled: false                    # Enable/disable AI system
  training-mode: false              # Enable learning mode
  thread-pool-size: 4               # AI processing threads
  
  cache:
    expiration-minutes: 30          # Cache duration
    max-size: 1000                  # Maximum cached results
```

#### OpenAI Configuration
```yaml
ai:
  openai:
    api-key: ""                     # Your OpenAI API key
    model: "gpt-4"                  # AI model (gpt-4, gpt-3.5-turbo)
    max-tokens: 1000                # Maximum tokens per request
    temperature: 0.3                # Response randomness (0.0-1.0)
```

#### Detection Thresholds
```yaml
ai:
  thresholds:
    movement: 0.7                   # Movement detection sensitivity
    combat: 0.8                     # Combat detection sensitivity
    exploit: 0.9                    # Exploit detection sensitivity
    building: 0.6                   # Building detection sensitivity
```

#### Training Configuration
```yaml
ai:
  training:
    min-data-size: 100              # Minimum data for training
    max-data-size: 10000            # Maximum training data
    batch-size: 50                  # Training batch size
    auto-training: true             # Enable automatic training
    interval-hours: 24              # Training frequency
```

## 🔧 Advanced Configuration

### AI Check Integration
```yaml
ai-checks:
  movement:
    enabled: true
    flag-threshold: 0.7             # Confidence to flag
    setback-threshold: 0.8          # Confidence to setback
    ai-weight: 0.5                  # AI vs traditional weight
  
  combat:
    enabled: true
    flag-threshold: 0.8
    setback-threshold: 0.9
    ai-weight: 0.6
```

### Performance Settings
```yaml
performance:
  max-analysis-time: 5000           # Max AI analysis time (ms)
  min-tps-threshold: 18.0           # Skip AI if TPS below this
  max-concurrent-analyses: 10       # Max simultaneous analyses
```

### Alert Configuration
```yaml
ai-alerts:
  enabled: true
  min-confidence: 0.7               # Minimum confidence for alerts
  include-reasoning: true           # Include AI reasoning in alerts
  log-to-console: false             # Log AI analysis to console
  log-training: true                # Log training sessions
```

## 📊 Monitoring & Analytics

### AI Status Monitoring
Use `/grimai status` to monitor:
- AI system status (enabled/disabled)
- Training mode status
- Current configuration summary
- Training statistics

### Performance Metrics
Use `/grimai stats` for detailed metrics:
- Training data collected
- Successful training sessions
- Detection accuracy
- Cache performance
- API usage statistics

## 🔒 Security Considerations

### API Key Security
- Store API keys securely
- Use environment variables in production
- Rotate keys regularly
- Monitor API usage

### Data Privacy
- AI analyzes behavior patterns, not personal data
- No chat messages or personal information sent to OpenAI
- Training data is anonymized
- Configurable data retention periods

## 🐛 Troubleshooting

### Common Issues

#### AI Not Starting
```
Error: AI Manager is not initialized!
```
**Solution**: Check that OpenAI API key is configured and valid.

#### Compilation Errors
```
Error: package org.bukkit does not exist
```
**Solution**: Ensure all dependencies are in the `lib` directory.

#### High API Usage
**Solution**: 
- Increase cache expiration time
- Reduce analysis frequency
- Adjust TPS threshold

#### False Positives
**Solution**:
- Enable training mode
- Adjust detection thresholds
- Review AI reasoning in alerts

### Debug Mode
Enable detailed logging:
```yaml
ai-alerts:
  log-to-console: true
  log-training: true
```

## 🤝 Contributing

### Development Setup
1. Clone the repository
2. Set up dependencies
3. Configure development environment
4. Run tests

### Code Structure
```
ac/grim/grimac/
├── manager/
│   ├── AIManager.java              # Core AI management
│   └── config/
│       └── AIConfigManager.java    # Configuration management
├── utils/ai/
│   ├── OpenAIClient.java           # OpenAI API integration
│   ├── AIDataCollector.java        # Data collection
│   ├── AITrainingManager.java      # Training management
│   ├── models/                     # Data models
│   ├── training/                   # Training system
│   ├── learning/                   # Learning algorithms
│   └── integration/                # Integration utilities
├── checks/impl/ai/                 # AI-powered checks
└── commands/
    └── GrimAI.java                 # AI commands
```

## 📄 License

This AI enhancement maintains compatibility with GrimAC's original license.

## 🆘 Support

For support with the AI enhancement:
1. Check the troubleshooting section
2. Review configuration documentation
3. Enable debug logging
4. Check OpenAI API status

## 🔮 Future Enhancements

### Planned Features
- **Multi-model Support**: Support for different AI models
- **Custom Training**: Server-specific model training
- **Real-time Analytics**: Live performance dashboards
- **Advanced Patterns**: More sophisticated pattern recognition
- **Integration APIs**: APIs for third-party integrations

### Experimental Features
- **Behavioral Profiling**: Long-term player behavior analysis
- **Predictive Detection**: Proactive cheat detection
- **Collaborative Learning**: Cross-server learning networks
- **Advanced Visualization**: AI decision visualization tools

---

**Note**: This is an experimental enhancement. Test thoroughly in development environments before production deployment.
