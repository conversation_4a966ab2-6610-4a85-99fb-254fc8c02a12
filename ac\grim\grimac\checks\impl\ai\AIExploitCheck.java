package ac.grim.grimac.checks.impl.ai;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.CheckData;
import ac.grim.grimac.checks.type.PacketCheck;
import ac.grim.grimac.manager.AIManager;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.utils.ai.models.AIDetectionResult;
import ac.grim.grimac.utils.ai.models.ExploitData;
import com.github.retrooper.packetevents.event.PacketReceiveEvent;
import com.github.retrooper.packetevents.protocol.packettype.PacketType;

import java.util.concurrent.CompletableFuture;
import java.util.logging.Logger;
import java.util.Set;
import java.util.HashSet;

/**
 * AI-powered exploit detection check
 * Learns and adapts to new exploit patterns
 */
@CheckData(
    name = "AIExploit",
    description = "AI-powered exploit pattern detection",
    experimental = true
)
public class AIExploitCheck extends Check implements PacketCheck {
    private static final Logger LOGGER = Logger.getLogger("GrimAC-AI-Exploit");
    
    private final AIManager aiManager;
    private final double flagThreshold;
    private final double setbackThreshold;
    private final double aiWeight;
    
    // Exploit tracking
    private final Set<String> suspiciousPacketTypes;
    private int exploitAttempts;
    private long lastExploitTime;
    private String lastExploitType;
    
    // Packet frequency tracking
    private final java.util.Map<PacketType, Integer> packetCounts;
    private final java.util.Map<PacketType, Long> lastPacketTimes;
    private long windowStartTime;
    private static final long ANALYSIS_WINDOW = 5000; // 5 seconds
    
    public AIExploitCheck(GrimPlayer player) {
        super(player);
        this.aiManager = GrimAPI.INSTANCE.getAIManager();
        
        // Load configuration
        this.flagThreshold = GrimAPI.INSTANCE.getConfigManager().getDoubleElse("ai-checks.exploit.flag-threshold", 0.9);
        this.setbackThreshold = GrimAPI.INSTANCE.getConfigManager().getDoubleElse("ai-checks.exploit.setback-threshold", 0.95);
        this.aiWeight = GrimAPI.INSTANCE.getConfigManager().getDoubleElse("ai-checks.exploit.ai-weight", 0.7);
        
        this.suspiciousPacketTypes = new HashSet<>();
        this.exploitAttempts = 0;
        this.lastExploitTime = 0;
        this.lastExploitType = "";
        
        this.packetCounts = new java.util.HashMap<>();
        this.lastPacketTimes = new java.util.HashMap<>();
        this.windowStartTime = System.currentTimeMillis();
    }
    
    @Override
    public void onPacketReceive(PacketReceiveEvent event) {
        // Skip if AI is not available or enabled
        if (aiManager == null || !aiManager.isEnabled()) {
            return;
        }
        
        // Skip if player is exempt
        if (player.disableGrim) {
            return;
        }
        
        try {
            PacketType packetType = event.getPacketType();
            long currentTime = System.currentTimeMillis();
            
            // Update packet frequency tracking
            updatePacketTracking(packetType, currentTime);
            
            // Check for suspicious packet patterns
            if (isSuspiciousPacket(packetType, currentTime)) {
                analyzeExploitAttempt(packetType, event, currentTime);
            }
            
            // Periodic analysis of packet patterns
            if (currentTime - windowStartTime > ANALYSIS_WINDOW) {
                analyzePacketPatterns(currentTime);
                resetWindow(currentTime);
            }
            
        } catch (Exception e) {
            LOGGER.warning("Error in AI exploit analysis for player " + player.user.getName() + ": " + e.getMessage());
        }
    }
    
    private void updatePacketTracking(PacketType packetType, long currentTime) {
        packetCounts.put(packetType, packetCounts.getOrDefault(packetType, 0) + 1);
        lastPacketTimes.put(packetType, currentTime);
    }
    
    private boolean isSuspiciousPacket(PacketType packetType, long currentTime) {
        // Check for packet spam
        Integer count = packetCounts.get(packetType);
        if (count != null && count > 50) { // More than 50 packets of same type in window
            return true;
        }
        
        // Check for rapid packet sending
        Long lastTime = lastPacketTimes.get(packetType);
        if (lastTime != null && currentTime - lastTime < 10) { // Less than 10ms between same packets
            return true;
        }
        
        // Check for known exploit-prone packet types
        return isExploitPronePacketType(packetType);
    }
    
    private boolean isExploitPronePacketType(PacketType packetType) {
        // Common packet types used in exploits
        return packetType == PacketType.Play.Client.PLAYER_POSITION ||
               packetType == PacketType.Play.Client.PLAYER_POSITION_AND_ROTATION ||
               packetType == PacketType.Play.Client.PLAYER_ROTATION ||
               packetType == PacketType.Play.Client.INTERACT_ENTITY ||
               packetType == PacketType.Play.Client.PLAYER_DIGGING ||
               packetType == PacketType.Play.Client.PLAYER_BLOCK_PLACEMENT ||
               packetType == PacketType.Play.Client.ANIMATION ||
               packetType == PacketType.Play.Client.CLICK_WINDOW;
    }
    
    private void analyzeExploitAttempt(PacketType packetType, PacketReceiveEvent event, long currentTime) {
        try {
            // Create exploit data
            ExploitData exploitData = new ExploitData(
                packetType.getName(),
                createExploitDetails(packetType, event, currentTime),
                currentTime
            );
            
            // Perform AI analysis asynchronously
            CompletableFuture<AIDetectionResult> analysisResult = aiManager.analyzePlayerBehavior(
                player, "exploit", exploitData
            );
            
            // Handle the result when it completes
            analysisResult.thenAccept(result -> handleAIResult(result, packetType.getName()));
            
        } catch (Exception e) {
            LOGGER.fine("Error analyzing exploit attempt: " + e.getMessage());
        }
    }
    
    private String createExploitDetails(PacketType packetType, PacketReceiveEvent event, long currentTime) {
        StringBuilder details = new StringBuilder();
        details.append("PacketType: ").append(packetType.getName()).append(", ");
        details.append("Count: ").append(packetCounts.getOrDefault(packetType, 0)).append(", ");
        details.append("TimeSinceLast: ").append(currentTime - lastPacketTimes.getOrDefault(packetType, currentTime)).append("ms, ");
        details.append("WindowTime: ").append(currentTime - windowStartTime).append("ms, ");
        details.append("PlayerPos: ").append(String.format("%.2f,%.2f,%.2f", player.x, player.y, player.z));
        
        // Add packet-specific details
        try {
            if (packetType == PacketType.Play.Client.PLAYER_POSITION ||
                packetType == PacketType.Play.Client.PLAYER_POSITION_AND_ROTATION) {
                details.append(", OnGround: ").append(player.onGround);
            }
        } catch (Exception e) {
            // Ignore errors in detail extraction
        }
        
        return details.toString();
    }
    
    private void analyzePacketPatterns(long currentTime) {
        try {
            // Create pattern analysis data
            java.util.Map<String, Object> patternData = new java.util.HashMap<>();
            patternData.put("totalPackets", packetCounts.values().stream().mapToInt(Integer::intValue).sum());
            patternData.put("uniquePacketTypes", packetCounts.size());
            patternData.put("windowDuration", currentTime - windowStartTime);
            patternData.put("packetDistribution", new java.util.HashMap<>(packetCounts));
            
            // Find most frequent packet type
            PacketType mostFrequent = packetCounts.entrySet().stream()
                .max(java.util.Map.Entry.comparingByValue())
                .map(java.util.Map.Entry::getKey)
                .orElse(null);
            
            if (mostFrequent != null) {
                patternData.put("mostFrequentPacket", mostFrequent.getName());
                patternData.put("mostFrequentCount", packetCounts.get(mostFrequent));
            }
            
            // Perform AI analysis on patterns
            CompletableFuture<AIDetectionResult> analysisResult = aiManager.analyzePlayerBehavior(
                player, "exploit", patternData
            );
            
            analysisResult.thenAccept(result -> handleAIResult(result, "PacketPattern"));
            
        } catch (Exception e) {
            LOGGER.fine("Error analyzing packet patterns: " + e.getMessage());
        }
    }
    
    private void handleAIResult(AIDetectionResult result, String exploitType) {
        if (result == null || result.isError()) {
            return;
        }
        
        // Log detailed analysis if enabled
        if (GrimAPI.INSTANCE.getConfigManager().getBooleanElse("ai-alerts.log-to-console", false)) {
            LOGGER.info("AI Exploit Analysis for " + player.user.getName() + " (" + exploitType + "): " + result.toString());
        }
        
        // Check if result is suspicious enough to flag
        if (result.isSuspicious() && result.getConfidence() >= flagThreshold) {
            exploitAttempts++;
            lastExploitTime = System.currentTimeMillis();
            lastExploitType = exploitType;
            suspiciousPacketTypes.add(exploitType);
            
            // Create alert message with AI reasoning
            String alertMessage = String.format("AI Exploit Detection (%s) - Confidence: %.2f, Type: %s", 
                exploitType, result.getConfidence(), result.getCheatType() != null ? result.getCheatType() : "Unknown");
            
            if (GrimAPI.INSTANCE.getConfigManager().getBooleanElse("ai-alerts.include-reasoning", true)) {
                alertMessage += " | Reasoning: " + result.getReasoning();
            }
            
            // Add exploit statistics
            alertMessage += String.format(" | Attempts: %d, LastType: %s", exploitAttempts, lastExploitType);
            
            // Flag with traditional check system
            if (flagAndAlert(alertMessage)) {
                // Exploits are serious - consider immediate action
                if (result.getConfidence() >= setbackThreshold || result.shouldBan()) {
                    // For exploits, we might want to kick or ban rather than just setback
                    if (result.getSeverity().equals("critical")) {
                        // This would trigger more severe punishment
                        violations += 10.0; // Heavy violation increase
                    }
                    setbackIfAboveSetbackVL();
                }
            }
        }
        
        // Update violation level based on AI confidence
        updateViolationLevel(result);
    }
    
    private void updateViolationLevel(AIDetectionResult result) {
        if (!result.isSuspicious()) {
            return; // Don't decrease violations for exploits
        }
        
        // Exploit violations are more serious
        double baseIncrease = result.getConfidence() * aiWeight * 2.0; // Double weight for exploits
        
        // Severity multiplier
        double severityMultiplier = switch (result.getSeverity().toLowerCase()) {
            case "critical" -> 5.0;
            case "high" -> 3.0;
            case "medium" -> 2.0;
            case "low" -> 1.0;
            default -> 2.0;
        };
        
        double violationIncrease = baseIncrease * severityMultiplier;
        violations += violationIncrease;
        
        // Higher cap for exploit violations
        violations = Math.min(violations, 200.0);
    }
    
    private void resetWindow(long currentTime) {
        packetCounts.clear();
        lastPacketTimes.clear();
        windowStartTime = currentTime;
    }
    
    @Override
    public void reload() {
        super.reload();
        // Reset state on reload
        suspiciousPacketTypes.clear();
        exploitAttempts = 0;
        resetWindow(System.currentTimeMillis());
    }
    
    // Utility methods
    public boolean hasRecentExploitAttempts() {
        return System.currentTimeMillis() - lastExploitTime < 30000; // 30 seconds
    }
    
    public int getExploitAttempts() {
        return exploitAttempts;
    }
    
    public Set<String> getSuspiciousPacketTypes() {
        return new HashSet<>(suspiciousPacketTypes);
    }
}
