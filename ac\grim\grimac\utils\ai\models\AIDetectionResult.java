package ac.grim.grimac.utils.ai.models;

import java.util.HashMap;
import java.util.Map;

/**
 * Result of AI-powered behavior analysis
 */
public class AIDetectionResult {
    private final boolean suspicious;
    private final double confidence;
    private final String reasoning;
    private final String cheatType;
    private final String severity;
    private final String behaviorType;
    private final long timestamp;
    private final long expirationTime;
    private final boolean isError;
    private final String errorMessage;
    
    // Additional metadata
    private final Map<String, Object> metadata;
    
    public AIDetectionResult(boolean suspicious, double confidence, String reasoning, 
                           String cheatType, String severity, String behaviorType) {
        this.suspicious = suspicious;
        this.confidence = Math.max(0.0, Math.min(1.0, confidence)); // Clamp between 0-1
        this.reasoning = reasoning != null ? reasoning : "No reasoning provided";
        this.cheatType = cheatType;
        this.severity = severity != null ? severity : "medium";
        this.behaviorType = behaviorType;
        this.timestamp = System.currentTimeMillis();
        this.expirationTime = timestamp + (30 * 60 * 1000); // 30 minutes default
        this.isError = false;
        this.errorMessage = null;
        this.metadata = new HashMap<>();
    }
    
    // Error constructor
    private AIDetectionResult(String errorMessage) {
        this.suspicious = false;
        this.confidence = 0.0;
        this.reasoning = "Error occurred during analysis";
        this.cheatType = null;
        this.severity = "none";
        this.behaviorType = "unknown";
        this.timestamp = System.currentTimeMillis();
        this.expirationTime = timestamp + (5 * 60 * 1000); // 5 minutes for errors
        this.isError = true;
        this.errorMessage = errorMessage;
        this.metadata = new HashMap<>();
    }
    
    // Neutral constructor
    private AIDetectionResult() {
        this.suspicious = false;
        this.confidence = 0.0;
        this.reasoning = "AI analysis disabled or neutral result";
        this.cheatType = null;
        this.severity = "none";
        this.behaviorType = "neutral";
        this.timestamp = System.currentTimeMillis();
        this.expirationTime = timestamp + (10 * 60 * 1000); // 10 minutes for neutral
        this.isError = false;
        this.errorMessage = null;
        this.metadata = new HashMap<>();
    }
    
    // Static factory methods
    public static AIDetectionResult createError(String errorMessage) {
        return new AIDetectionResult(errorMessage);
    }
    
    public static AIDetectionResult createNeutral() {
        return new AIDetectionResult();
    }
    
    public static AIDetectionResult createSuspicious(double confidence, String reasoning, 
                                                   String cheatType, String behaviorType) {
        return new AIDetectionResult(true, confidence, reasoning, cheatType, "high", behaviorType);
    }
    
    public static AIDetectionResult createNormal(double confidence, String reasoning, String behaviorType) {
        return new AIDetectionResult(false, confidence, reasoning, null, "none", behaviorType);
    }
    
    // Getters
    public boolean isSuspicious() { return suspicious; }
    public double getConfidence() { return confidence; }
    public String getReasoning() { return reasoning; }
    public String getCheatType() { return cheatType; }
    public String getSeverity() { return severity; }
    public String getBehaviorType() { return behaviorType; }
    public long getTimestamp() { return timestamp; }
    public boolean isError() { return isError; }
    public String getErrorMessage() { return errorMessage; }
    
    // Expiration methods
    public boolean isExpired() {
        return System.currentTimeMillis() > expirationTime;
    }
    
    public long getTimeUntilExpiration() {
        return Math.max(0, expirationTime - System.currentTimeMillis());
    }
    
    // Metadata methods
    public void addMetadata(String key, Object value) {
        metadata.put(key, value);
    }
    
    public Object getMetadata(String key) {
        return metadata.get(key);
    }
    
    public Map<String, Object> getAllMetadata() {
        return new HashMap<>(metadata);
    }
    
    // Utility methods
    public boolean shouldFlag() {
        return suspicious && !isError && confidence >= 0.5;
    }
    
    public boolean shouldSetback() {
        return suspicious && !isError && confidence >= 0.7 && 
               ("high".equals(severity) || "critical".equals(severity));
    }
    
    public boolean shouldBan() {
        return suspicious && !isError && confidence >= 0.9 && "critical".equals(severity);
    }
    
    public String getSeverityLevel() {
        if (isError) return "ERROR";
        if (!suspicious) return "NORMAL";
        
        if (confidence >= 0.9) return "CRITICAL";
        if (confidence >= 0.7) return "HIGH";
        if (confidence >= 0.5) return "MEDIUM";
        return "LOW";
    }
    
    public String getFormattedResult() {
        StringBuilder sb = new StringBuilder();
        sb.append("AI Detection Result:\n");
        sb.append("  Suspicious: ").append(suspicious).append("\n");
        sb.append("  Confidence: ").append(String.format("%.2f", confidence)).append("\n");
        sb.append("  Severity: ").append(getSeverityLevel()).append("\n");
        sb.append("  Behavior: ").append(behaviorType).append("\n");
        if (cheatType != null) {
            sb.append("  Cheat Type: ").append(cheatType).append("\n");
        }
        sb.append("  Reasoning: ").append(reasoning).append("\n");
        if (isError) {
            sb.append("  Error: ").append(errorMessage).append("\n");
        }
        return sb.toString();
    }
    
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("suspicious", suspicious);
        map.put("confidence", confidence);
        map.put("reasoning", reasoning);
        map.put("cheatType", cheatType);
        map.put("severity", severity);
        map.put("behaviorType", behaviorType);
        map.put("timestamp", timestamp);
        map.put("isError", isError);
        map.put("errorMessage", errorMessage);
        map.put("severityLevel", getSeverityLevel());
        map.put("shouldFlag", shouldFlag());
        map.put("shouldSetback", shouldSetback());
        map.put("shouldBan", shouldBan());
        map.putAll(metadata);
        return map;
    }
    
    @Override
    public String toString() {
        return String.format("AIDetectionResult{suspicious=%s, confidence=%.2f, severity=%s, behavior=%s, cheat=%s}", 
                           suspicious, confidence, getSeverityLevel(), behaviorType, cheatType);
    }
}
