package ac.grim.grimac.utils.ai;

import ac.grim.grimac.utils.ai.models.*;
import org.bukkit.util.Vector;

import java.util.logging.Logger;

/**
 * Collects and processes player behavior data for AI analysis
 */
public class AIDataCollector {
    private static final Logger LOGGER = Logger.getLogger("GrimAC-AI-DataCollector");
    
    public AIDataCollector() {
        LOGGER.info("AI Data Collector initialized");
    }
    
    /**
     * Update player behavior data based on behavior type
     */
    public void updatePlayerBehavior(PlayerBehaviorData playerData, String behaviorType, Object behaviorData) {
        try {
            switch (behaviorType.toLowerCase()) {
                case "movement":
                    handleMovementData(playerData, behaviorData);
                    break;
                case "combat":
                    handleCombatData(playerData, behaviorData);
                    break;
                case "building":
                    handleBuildingData(playerData, behaviorData);
                    break;
                case "exploit":
                    handleExploitData(playerData, behaviorData);
                    break;
                default:
                    LOGGER.warning("Unknown behavior type: " + behaviorType);
            }
            
            playerData.incrementPacketCount();
            
        } catch (Exception e) {
            LOGGER.severe("Error updating player behavior data: " + e.getMessage());
        }
    }
    
    private void handleMovementData(PlayerBehaviorData playerData, Object data) {
        if (data instanceof MovementData) {
            playerData.addMovementData((MovementData) data);
        } else {
            // Convert from various movement-related objects
            MovementData movementData = convertToMovementData(data);
            if (movementData != null) {
                playerData.addMovementData(movementData);
            }
        }
    }
    
    private void handleCombatData(PlayerBehaviorData playerData, Object data) {
        if (data instanceof CombatData) {
            playerData.addCombatData((CombatData) data);
        } else {
            // Convert from various combat-related objects
            CombatData combatData = convertToCombatData(data);
            if (combatData != null) {
                playerData.addCombatData(combatData);
            }
        }
    }
    
    private void handleBuildingData(PlayerBehaviorData playerData, Object data) {
        if (data instanceof BuildingData) {
            playerData.addBuildingData((BuildingData) data);
        } else {
            // Convert from various building-related objects
            BuildingData buildingData = convertToBuildingData(data);
            if (buildingData != null) {
                playerData.addBuildingData(buildingData);
            }
        }
    }
    
    private void handleExploitData(PlayerBehaviorData playerData, Object data) {
        if (data instanceof ExploitData) {
            playerData.addExploitData((ExploitData) data);
        } else {
            // Convert from various exploit-related objects
            ExploitData exploitData = convertToExploitData(data);
            if (exploitData != null) {
                playerData.addExploitData(exploitData);
            }
        }
    }
    
    // Conversion methods for different data types
    private MovementData convertToMovementData(Object data) {
        try {
            // Handle Vector movement
            if (data instanceof Vector) {
                Vector vector = (Vector) data;
                return new MovementData(
                    vector.getX(), vector.getY(), vector.getZ(),
                    0.0f, 0.0f, // yaw, pitch
                    false, false, false, // onGround, sprinting, sneaking
                    System.currentTimeMillis()
                );
            }
            
            // Handle position update data
            if (data.getClass().getSimpleName().contains("Position")) {
                // Use reflection to extract position data
                return extractMovementFromPositionUpdate(data);
            }
            
            return null;
        } catch (Exception e) {
            LOGGER.warning("Failed to convert movement data: " + e.getMessage());
            return null;
        }
    }
    
    private CombatData convertToCombatData(Object data) {
        try {
            // Handle combat-related packets
            if (data.getClass().getSimpleName().contains("Attack") || 
                data.getClass().getSimpleName().contains("Combat")) {
                return extractCombatData(data);
            }
            
            return null;
        } catch (Exception e) {
            LOGGER.warning("Failed to convert combat data: " + e.getMessage());
            return null;
        }
    }
    
    private BuildingData convertToBuildingData(Object data) {
        try {
            // Handle block place/break data
            if (data.getClass().getSimpleName().contains("Block")) {
                return extractBuildingData(data);
            }
            
            return null;
        } catch (Exception e) {
            LOGGER.warning("Failed to convert building data: " + e.getMessage());
            return null;
        }
    }
    
    private ExploitData convertToExploitData(Object data) {
        try {
            // Handle exploit-related data
            return new ExploitData(
                data.getClass().getSimpleName(),
                data.toString(),
                System.currentTimeMillis()
            );
        } catch (Exception e) {
            LOGGER.warning("Failed to convert exploit data: " + e.getMessage());
            return null;
        }
    }
    
    // Helper methods for data extraction
    private MovementData extractMovementFromPositionUpdate(Object positionUpdate) {
        try {
            // Use reflection to extract common fields
            Class<?> clazz = positionUpdate.getClass();
            
            double x = getFieldValue(positionUpdate, clazz, "x", "getX", 0.0);
            double y = getFieldValue(positionUpdate, clazz, "y", "getY", 0.0);
            double z = getFieldValue(positionUpdate, clazz, "z", "getZ", 0.0);
            float yaw = (float) getFieldValue(positionUpdate, clazz, "yaw", "getYaw", 0.0f);
            float pitch = (float) getFieldValue(positionUpdate, clazz, "pitch", "getPitch", 0.0f);
            boolean onGround = (boolean) getFieldValue(positionUpdate, clazz, "onGround", "isOnGround", false);
            
            return new MovementData(x, y, z, yaw, pitch, onGround, false, false, System.currentTimeMillis());
            
        } catch (Exception e) {
            LOGGER.fine("Could not extract movement data from position update: " + e.getMessage());
            return null;
        }
    }
    
    private CombatData extractCombatData(Object combatObject) {
        try {
            // Extract combat-related information
            return new CombatData(
                0, // entityId - would need to extract from actual object
                0.0, 0.0, 0.0, // target position
                "unknown", // attack type
                System.currentTimeMillis()
            );
        } catch (Exception e) {
            LOGGER.fine("Could not extract combat data: " + e.getMessage());
            return null;
        }
    }
    
    private BuildingData extractBuildingData(Object blockObject) {
        try {
            // Extract building-related information
            return new BuildingData(
                0, 0, 0, // block position
                "unknown", // block type
                "place", // action
                System.currentTimeMillis()
            );
        } catch (Exception e) {
            LOGGER.fine("Could not extract building data: " + e.getMessage());
            return null;
        }
    }
    
    // Utility method for reflection-based field access
    private Object getFieldValue(Object obj, Class<?> clazz, String fieldName, String methodName, Object defaultValue) {
        try {
            // Try method first
            try {
                return clazz.getMethod(methodName).invoke(obj);
            } catch (Exception ignored) {}
            
            // Try field access
            try {
                var field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                return field.get(obj);
            } catch (Exception ignored) {}
            
            return defaultValue;
        } catch (Exception e) {
            return defaultValue;
        }
    }
}
