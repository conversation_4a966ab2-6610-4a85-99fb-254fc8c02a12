package ac.grim.grimac.shaded.acf;

import ac.grim.grimac.shaded.jetbrains.annotations.NotNull;
import ac.grim.grimac.shaded.locales.LocaleManager;
import ac.grim.grimac.shaded.locales.MessageKey;
import ac.grim.grimac.shaded.locales.MessageKeyProvider;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.SetMultimap;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.Map.Entry;
import java.util.regex.Matcher;

public class Locales {
   public static final Locale ENGLISH;
   public static final Locale GERMAN;
   public static final Locale FRENCH;
   public static final Locale JAPANESE;
   public static final Locale ITALIAN;
   public static final Locale KOREAN;
   public static final Locale CHINESE;
   public static final Locale SIMPLIFIED_CHINESE;
   public static final Locale TRADITIONAL_CHINESE;
   public static final Locale SPANISH;
   public static final Locale DUTCH;
   public static final Locale DANISH;
   public static final Locale CZECH;
   public static final Locale GREEK;
   public static final Locale LATIN;
   public static final Locale BULGARIAN;
   public static final Locale AFRIKAANS;
   public static final Locale HINDI;
   public static final Locale HEBREW;
   public static final Locale POLISH;
   public static final Locale PORTUGUESE;
   public static final Locale FINNISH;
   public static final Locale SWEDISH;
   public static final Locale RUSSIAN;
   public static final Locale ROMANIAN;
   public static final Locale VIETNAMESE;
   public static final Locale THAI;
   public static final Locale TURKISH;
   public static final Locale UKRANIAN;
   public static final Locale ARABIC;
   public static final Locale WELSH;
   public static final Locale NORWEGIAN_BOKMAAL;
   public static final Locale NORWEGIAN_NYNORSK;
   public static final Locale HUNGARIAN;
   private final CommandManager manager;
   private final LocaleManager<CommandIssuer> localeManager;
   private final Map<ClassLoader, SetMultimap<String, Locale>> loadedBundles = new HashMap();
   private final List<ClassLoader> registeredClassLoaders = new ArrayList();

   public Locales(CommandManager manager) {
      this.manager = manager;
      Objects.requireNonNull(manager);
      this.localeManager = LocaleManager.create(manager::getIssuerLocale);
      this.addBundleClassLoader(this.getClass().getClassLoader());
   }

   public void loadLanguages() {
      this.addMessageBundles("acf-core");
   }

   public Locale getDefaultLocale() {
      return this.localeManager.getDefaultLocale();
   }

   public Locale setDefaultLocale(Locale locale) {
      return this.localeManager.setDefaultLocale(locale);
   }

   public void loadMissingBundles() {
      Set<Locale> supportedLanguages = this.manager.getSupportedLanguages();
      Iterator var2 = supportedLanguages.iterator();

      while(var2.hasNext()) {
         Locale locale = (Locale)var2.next();
         Iterator var4 = this.loadedBundles.values().iterator();

         while(var4.hasNext()) {
            SetMultimap<String, Locale> localeData = (SetMultimap)var4.next();
            Iterator var6 = (new HashSet(localeData.keys())).iterator();

            while(var6.hasNext()) {
               String bundleName = (String)var6.next();
               this.addMessageBundle(bundleName, locale);
            }
         }
      }

   }

   public void addMessageBundles(String... bundleNames) {
      String[] var2 = bundleNames;
      int var3 = bundleNames.length;

      for(int var4 = 0; var4 < var3; ++var4) {
         String bundleName = var2[var4];
         Set<Locale> supportedLanguages = this.manager.getSupportedLanguages();
         Iterator var7 = supportedLanguages.iterator();

         while(var7.hasNext()) {
            Locale locale = (Locale)var7.next();
            this.addMessageBundle(bundleName, locale);
         }
      }

   }

   public boolean addMessageBundle(String bundleName, Locale locale) {
      boolean found = false;
      Iterator var4 = this.registeredClassLoaders.iterator();

      while(var4.hasNext()) {
         ClassLoader classLoader = (ClassLoader)var4.next();
         if (this.addMessageBundle(classLoader, bundleName, locale)) {
            found = true;
         }
      }

      return found;
   }

   public boolean addMessageBundle(ClassLoader classLoader, String bundleName, Locale locale) {
      SetMultimap<String, Locale> classLoadersLocales = (SetMultimap)this.loadedBundles.getOrDefault(classLoader, HashMultimap.create());
      if (!classLoadersLocales.containsEntry(bundleName, locale) && this.localeManager.addMessageBundle(classLoader, bundleName, locale)) {
         classLoadersLocales.put(bundleName, locale);
         this.loadedBundles.put(classLoader, classLoadersLocales);
         return true;
      } else {
         return false;
      }
   }

   public void addMessageStrings(Locale locale, @NotNull Map<String, String> messages) {
      Map<MessageKey, String> map = new HashMap(messages.size());
      messages.forEach((key, value) -> {
         map.put(MessageKey.of(key), value);
      });
      this.localeManager.addMessages(locale, map);
   }

   public void addMessages(Locale locale, @NotNull Map<? extends MessageKeyProvider, String> messages) {
      Map<MessageKey, String> messagesMap = new LinkedHashMap();
      Iterator var4 = messages.entrySet().iterator();

      while(var4.hasNext()) {
         Entry<? extends MessageKeyProvider, String> entry = (Entry)var4.next();
         messagesMap.put(((MessageKeyProvider)entry.getKey()).getMessageKey(), (String)entry.getValue());
      }

      this.localeManager.addMessages(locale, messagesMap);
   }

   public String addMessage(Locale locale, MessageKeyProvider key, String message) {
      return this.localeManager.addMessage(locale, key.getMessageKey(), message);
   }

   public String getMessage(CommandIssuer issuer, MessageKeyProvider key) {
      MessageKey msgKey = key.getMessageKey();
      String message = this.localeManager.getMessage(issuer, msgKey);
      if (message == null) {
         this.manager.log(LogLevel.ERROR, "Missing Language Key: " + msgKey.getKey());
         message = "<MISSING_LANGUAGE_KEY:" + msgKey.getKey() + ">";
      }

      return message;
   }

   public String getOptionalMessage(CommandIssuer issuer, MessageKey key) {
      return issuer == null ? this.localeManager.getTable(this.getDefaultLocale()).getMessage(key) : this.localeManager.getMessage(issuer, key);
   }

   public String replaceI18NStrings(String message) {
      if (message == null) {
         return null;
      } else {
         Matcher matcher = ACFPatterns.I18N_STRING.matcher(message);
         if (!matcher.find()) {
            return message;
         } else {
            CommandIssuer issuer = CommandManager.getCurrentCommandIssuer();
            matcher.reset();
            StringBuffer sb = new StringBuffer(message.length());

            while(matcher.find()) {
               MessageKey key = MessageKey.of(matcher.group("key"));
               matcher.appendReplacement(sb, Matcher.quoteReplacement(this.getMessage(issuer, key)));
            }

            matcher.appendTail(sb);
            return sb.toString();
         }
      }
   }

   public boolean addBundleClassLoader(ClassLoader classLoader) {
      return !this.registeredClassLoaders.contains(classLoader) && this.registeredClassLoaders.add(classLoader);
   }

   static {
      ENGLISH = Locale.ENGLISH;
      GERMAN = Locale.GERMAN;
      FRENCH = Locale.FRENCH;
      JAPANESE = Locale.JAPANESE;
      ITALIAN = Locale.ITALIAN;
      KOREAN = Locale.KOREAN;
      CHINESE = Locale.CHINESE;
      SIMPLIFIED_CHINESE = Locale.SIMPLIFIED_CHINESE;
      TRADITIONAL_CHINESE = Locale.TRADITIONAL_CHINESE;
      SPANISH = new Locale("es");
      DUTCH = new Locale("nl");
      DANISH = new Locale("da");
      CZECH = new Locale("cs");
      GREEK = new Locale("el");
      LATIN = new Locale("la");
      BULGARIAN = new Locale("bg");
      AFRIKAANS = new Locale("af");
      HINDI = new Locale("hi");
      HEBREW = new Locale("he");
      POLISH = new Locale("pl");
      PORTUGUESE = new Locale("pt");
      FINNISH = new Locale("fi");
      SWEDISH = new Locale("sv");
      RUSSIAN = new Locale("ru");
      ROMANIAN = new Locale("ro");
      VIETNAMESE = new Locale("vi");
      THAI = new Locale("th");
      TURKISH = new Locale("tr");
      UKRANIAN = new Locale("uk");
      ARABIC = new Locale("ar");
      WELSH = new Locale("cy");
      NORWEGIAN_BOKMAAL = new Locale("nb");
      NORWEGIAN_NYNORSK = new Locale("nn");
      HUNGARIAN = new Locale("hu");
   }
}
