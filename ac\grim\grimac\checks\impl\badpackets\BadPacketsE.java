package ac.grim.grimac.checks.impl.badpackets;

import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.CheckData;
import ac.grim.grimac.checks.type.PacketCheck;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.PacketEvents;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketReceiveEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.manager.server.ServerVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.packettype.PacketType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.ClientVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientPlayerFlying;

@CheckData(
   name = "BadPacketsE"
)
public class BadPacketsE extends Check implements PacketCheck {
   private int noReminderTicks;

   public BadPacketsE(GrimPlayer player) {
      super(player);
   }

   public void onPacketReceive(PacketReceiveEvent event) {
      boolean isViaPleaseStopUsingProtocolHacksOnYourServer = this.player.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_21_2) || PacketEvents.getAPI().getServerManager().getVersion().isNewerThanOrEquals(ServerVersion.V_1_21_2);
      if (event.getPacketType() != PacketType.Play.Client.PLAYER_POSITION_AND_ROTATION && event.getPacketType() != PacketType.Play.Client.PLAYER_POSITION) {
         if (WrapperPlayClientPlayerFlying.isFlying(event.getPacketType())) {
            ++this.noReminderTicks;
         } else if (event.getPacketType() == PacketType.Play.Client.STEER_VEHICLE || isViaPleaseStopUsingProtocolHacksOnYourServer && this.player.inVehicle()) {
            this.noReminderTicks = 0;
         }
      } else {
         this.noReminderTicks = 0;
      }

      if (this.noReminderTicks > 20) {
         this.flagAndAlert("ticks=" + this.noReminderTicks);
      }

   }

   public void handleRespawn() {
      this.noReminderTicks = 0;
   }
}
