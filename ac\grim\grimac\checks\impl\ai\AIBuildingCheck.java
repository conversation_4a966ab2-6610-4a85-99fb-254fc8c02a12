package ac.grim.grimac.checks.impl.ai;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.CheckData;
import ac.grim.grimac.checks.type.BlockPlaceCheck;
import ac.grim.grimac.checks.type.BlockBreakCheck;
import ac.grim.grimac.manager.AIManager;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.utils.ai.models.AIDetectionResult;
import ac.grim.grimac.utils.ai.models.BuildingData;
import ac.grim.grimac.utils.anticheat.update.BlockBreak;
import ac.grim.grimac.utils.anticheat.update.BlockPlace;

import java.util.concurrent.CompletableFuture;
import java.util.logging.Logger;
import java.util.List;
import java.util.ArrayList;

/**
 * AI-powered building pattern analysis check
 * Detects suspicious building behaviors and patterns
 */
@CheckData(
    name = "AIBuilding",
    description = "AI-powered building pattern analysis",
    experimental = true
)
public class AIBuildingCheck extends Check implements BlockPlaceCheck, BlockBreakCheck {
    private static final Logger LOGGER = Logger.getLogger("GrimAC-AI-Building");
    
    private final AIManager aiManager;
    private final double flagThreshold;
    private final double setbackThreshold;
    private final double aiWeight;
    
    // Building tracking
    private final List<BuildingAction> recentActions;
    private long lastActionTime;
    private int blocksPerSecond;
    private int consecutiveSuspiciousActions;
    private String dominantBuildingPattern;
    
    // Pattern analysis
    private final java.util.Map<String, Integer> blockTypeFrequency;
    private final java.util.Map<String, Integer> actionTypeFrequency;
    private long patternAnalysisWindow;
    
    public AIBuildingCheck(GrimPlayer player) {
        super(player);
        this.aiManager = GrimAPI.INSTANCE.getAIManager();
        
        // Load configuration
        this.flagThreshold = GrimAPI.INSTANCE.getConfigManager().getDoubleElse("ai-checks.building.flag-threshold", 0.6);
        this.setbackThreshold = GrimAPI.INSTANCE.getConfigManager().getDoubleElse("ai-checks.building.setback-threshold", 0.8);
        this.aiWeight = GrimAPI.INSTANCE.getConfigManager().getDoubleElse("ai-checks.building.ai-weight", 0.4);
        
        this.recentActions = new ArrayList<>();
        this.lastActionTime = 0;
        this.blocksPerSecond = 0;
        this.consecutiveSuspiciousActions = 0;
        this.dominantBuildingPattern = "normal";
        
        this.blockTypeFrequency = new java.util.HashMap<>();
        this.actionTypeFrequency = new java.util.HashMap<>();
        this.patternAnalysisWindow = System.currentTimeMillis();
    }
    
    @Override
    public void onBlockPlace(BlockPlace blockPlace) {
        // Skip if AI is not available or enabled
        if (aiManager == null || !aiManager.isEnabled()) {
            return;
        }
        
        // Skip if player is exempt or in creative
        if (player.disableGrim || player.gamemode.name().equals("CREATIVE")) {
            return;
        }
        
        try {
            analyzeBuildingAction("place", blockPlace);
        } catch (Exception e) {
            LOGGER.warning("Error in AI building analysis (place) for player " + player.user.getName() + ": " + e.getMessage());
        }
    }
    
    @Override
    public void onBlockBreak(BlockBreak blockBreak) {
        // Skip if AI is not available or enabled
        if (aiManager == null || !aiManager.isEnabled()) {
            return;
        }
        
        // Skip if player is exempt or in creative
        if (player.disableGrim || player.gamemode.name().equals("CREATIVE")) {
            return;
        }
        
        try {
            analyzeBuildingAction("break", blockBreak);
        } catch (Exception e) {
            LOGGER.warning("Error in AI building analysis (break) for player " + player.user.getName() + ": " + e.getMessage());
        }
    }
    
    private void analyzeBuildingAction(String action, Object blockAction) {
        long currentTime = System.currentTimeMillis();
        
        // Extract block information
        BlockInfo blockInfo = extractBlockInfo(blockAction);
        if (blockInfo == null) {
            return;
        }
        
        // Create building action record
        BuildingAction buildingAction = new BuildingAction(
            action, blockInfo.x, blockInfo.y, blockInfo.z, 
            blockInfo.blockType, currentTime
        );
        
        // Add to recent actions
        recentActions.add(buildingAction);
        
        // Keep only recent actions (last 30 seconds)
        recentActions.removeIf(a -> currentTime - a.timestamp > 30000);
        
        // Update statistics
        updateBuildingStatistics(action, blockInfo.blockType, currentTime);
        
        // Create building data for AI analysis
        BuildingData buildingData = new BuildingData(
            blockInfo.x, blockInfo.y, blockInfo.z,
            blockInfo.blockType, action, currentTime
        );
        
        // Add context information
        java.util.Map<String, Object> context = new java.util.HashMap<>();
        context.put("recentActionCount", recentActions.size());
        context.put("blocksPerSecond", calculateBlocksPerSecond());
        context.put("dominantPattern", analyzeBuildingPattern());
        context.put("actionSequence", getRecentActionSequence());
        context.put("blockTypeVariety", blockTypeFrequency.size());
        context.put("playerPosition", String.format("%.2f,%.2f,%.2f", player.x, player.y, player.z));
        context.put("timeSinceLastAction", currentTime - lastActionTime);
        
        buildingData.addContext(context);
        
        // Perform AI analysis asynchronously
        CompletableFuture<AIDetectionResult> analysisResult = aiManager.analyzePlayerBehavior(
            player, "building", buildingData
        );
        
        // Handle the result when it completes
        analysisResult.thenAccept(this::handleAIResult);
        
        lastActionTime = currentTime;
    }
    
    private BlockInfo extractBlockInfo(Object blockAction) {
        try {
            if (blockAction instanceof BlockPlace) {
                BlockPlace place = (BlockPlace) blockAction;
                return new BlockInfo(
                    place.getPlacedAgainstBlockLocation().getX(),
                    place.getPlacedAgainstBlockLocation().getY(),
                    place.getPlacedAgainstBlockLocation().getZ(),
                    place.getPlacedBlockData().getType().name()
                );
            } else if (blockAction instanceof BlockBreak) {
                BlockBreak breakAction = (BlockBreak) blockAction;
                return new BlockInfo(
                    breakAction.getBlockPosition().getX(),
                    breakAction.getBlockPosition().getY(),
                    breakAction.getBlockPosition().getZ(),
                    breakAction.getBlock().getType().name()
                );
            }
        } catch (Exception e) {
            LOGGER.fine("Could not extract block info: " + e.getMessage());
        }
        return null;
    }
    
    private void updateBuildingStatistics(String action, String blockType, long currentTime) {
        // Update frequency maps
        actionTypeFrequency.put(action, actionTypeFrequency.getOrDefault(action, 0) + 1);
        blockTypeFrequency.put(blockType, blockTypeFrequency.getOrDefault(blockType, 0) + 1);
        
        // Calculate blocks per second
        long windowStart = currentTime - 1000; // 1 second window
        int actionsInWindow = (int) recentActions.stream()
            .filter(a -> a.timestamp > windowStart)
            .count();
        blocksPerSecond = actionsInWindow;
    }
    
    private int calculateBlocksPerSecond() {
        long currentTime = System.currentTimeMillis();
        long windowStart = currentTime - 1000; // 1 second window
        return (int) recentActions.stream()
            .filter(a -> a.timestamp > windowStart)
            .count();
    }
    
    private String analyzeBuildingPattern() {
        if (recentActions.size() < 3) {
            return "insufficient_data";
        }
        
        // Analyze spatial patterns
        boolean isLinear = isLinearPattern();
        boolean isRepeating = isRepeatingPattern();
        boolean isRapid = blocksPerSecond > 10;
        
        if (isRapid && isLinear) {
            return "rapid_linear";
        } else if (isRapid && isRepeating) {
            return "rapid_repeating";
        } else if (isLinear) {
            return "linear";
        } else if (isRepeating) {
            return "repeating";
        } else if (isRapid) {
            return "rapid_random";
        } else {
            return "normal";
        }
    }
    
    private boolean isLinearPattern() {
        if (recentActions.size() < 3) return false;
        
        // Check if recent actions form a line
        int consecutiveLinear = 0;
        for (int i = 2; i < recentActions.size(); i++) {
            BuildingAction a1 = recentActions.get(i - 2);
            BuildingAction a2 = recentActions.get(i - 1);
            BuildingAction a3 = recentActions.get(i);
            
            if (isLinear(a1, a2, a3)) {
                consecutiveLinear++;
            }
        }
        
        return consecutiveLinear >= 3;
    }
    
    private boolean isLinear(BuildingAction a1, BuildingAction a2, BuildingAction a3) {
        // Check if three points are roughly in a line
        int dx1 = a2.x - a1.x;
        int dy1 = a2.y - a1.y;
        int dz1 = a2.z - a1.z;
        
        int dx2 = a3.x - a2.x;
        int dy2 = a3.y - a2.y;
        int dz2 = a3.z - a2.z;
        
        // Simple linear check - same direction
        return (dx1 == dx2 && dy1 == dy2 && dz1 == dz2) ||
               (Math.abs(dx1) + Math.abs(dy1) + Math.abs(dz1) == 1 && 
                Math.abs(dx2) + Math.abs(dy2) + Math.abs(dz2) == 1);
    }
    
    private boolean isRepeatingPattern() {
        // Check if the same block type is being used repeatedly
        if (blockTypeFrequency.isEmpty()) return false;
        
        String mostCommon = blockTypeFrequency.entrySet().stream()
            .max(java.util.Map.Entry.comparingByValue())
            .map(java.util.Map.Entry::getKey)
            .orElse("");
        
        int totalActions = blockTypeFrequency.values().stream().mapToInt(Integer::intValue).sum();
        int mostCommonCount = blockTypeFrequency.getOrDefault(mostCommon, 0);
        
        return totalActions > 5 && (double) mostCommonCount / totalActions > 0.8;
    }
    
    private String getRecentActionSequence() {
        return recentActions.stream()
            .limit(10) // Last 10 actions
            .map(a -> a.action + ":" + a.blockType)
            .reduce((a, b) -> a + "," + b)
            .orElse("");
    }
    
    private void handleAIResult(AIDetectionResult result) {
        if (result == null || result.isError()) {
            return;
        }
        
        // Log detailed analysis if enabled
        if (GrimAPI.INSTANCE.getConfigManager().getBooleanElse("ai-alerts.log-to-console", false)) {
            LOGGER.info("AI Building Analysis for " + player.user.getName() + ": " + result.toString());
        }
        
        // Check if result is suspicious enough to flag
        if (result.isSuspicious() && result.getConfidence() >= flagThreshold) {
            consecutiveSuspiciousActions++;
            dominantBuildingPattern = analyzeBuildingPattern();
            
            // Create alert message with AI reasoning
            String alertMessage = String.format("AI Building Detection - Confidence: %.2f, Pattern: %s, Type: %s", 
                result.getConfidence(), dominantBuildingPattern, 
                result.getCheatType() != null ? result.getCheatType() : "Unknown");
            
            if (GrimAPI.INSTANCE.getConfigManager().getBooleanElse("ai-alerts.include-reasoning", true)) {
                alertMessage += " | Reasoning: " + result.getReasoning();
            }
            
            // Add building statistics
            alertMessage += String.format(" | BPS: %d, Actions: %d", blocksPerSecond, recentActions.size());
            
            // Flag with traditional check system
            if (flagAndAlert(alertMessage)) {
                // Building violations are usually less severe than combat/movement
                if (result.getConfidence() >= setbackThreshold || consecutiveSuspiciousActions >= 10) {
                    // For building, we might just slow down rather than setback
                    if (result.shouldSetback()) {
                        setbackIfAboveSetbackVL();
                    }
                    consecutiveSuspiciousActions = 0;
                }
            }
            
        } else {
            // Reset consecutive count if building is normal
            consecutiveSuspiciousActions = Math.max(0, consecutiveSuspiciousActions - 1);
        }
        
        // Update violation level
        updateViolationLevel(result);
    }
    
    private void updateViolationLevel(AIDetectionResult result) {
        if (!result.isSuspicious()) {
            // Decrease violation level for normal behavior
            violations = Math.max(0, violations - 0.02);
            return;
        }
        
        // Building violations are generally less severe
        double baseIncrease = result.getConfidence() * aiWeight * 0.5; // Half weight for building
        violations += baseIncrease;
        violations = Math.min(violations, 30.0); // Lower cap for building violations
    }
    
    @Override
    public void reload() {
        super.reload();
        // Reset state on reload
        recentActions.clear();
        consecutiveSuspiciousActions = 0;
        blockTypeFrequency.clear();
        actionTypeFrequency.clear();
        patternAnalysisWindow = System.currentTimeMillis();
    }
    
    // Utility classes and methods
    private static class BlockInfo {
        final int x, y, z;
        final String blockType;
        
        BlockInfo(int x, int y, int z, String blockType) {
            this.x = x; this.y = y; this.z = z;
            this.blockType = blockType;
        }
    }
    
    private static class BuildingAction {
        final String action;
        final int x, y, z;
        final String blockType;
        final long timestamp;
        
        BuildingAction(String action, int x, int y, int z, String blockType, long timestamp) {
            this.action = action;
            this.x = x; this.y = y; this.z = z;
            this.blockType = blockType;
            this.timestamp = timestamp;
        }
    }
    
    // Public utility methods
    public String getDominantBuildingPattern() {
        return dominantBuildingPattern;
    }
    
    public int getBlocksPerSecond() {
        return blocksPerSecond;
    }
    
    public int getConsecutiveSuspiciousActions() {
        return consecutiveSuspiciousActions;
    }
}
