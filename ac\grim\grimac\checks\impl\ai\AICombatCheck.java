package ac.grim.grimac.checks.impl.ai;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.CheckData;
import ac.grim.grimac.checks.type.PacketCheck;
import ac.grim.grimac.manager.AIManager;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.utils.ai.models.AIDetectionResult;
import ac.grim.grimac.utils.ai.models.CombatData;
import com.github.retrooper.packetevents.event.PacketReceiveEvent;
import com.github.retrooper.packetevents.protocol.packettype.PacketType;
import com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientInteractEntity;

import java.util.concurrent.CompletableFuture;
import java.util.logging.Logger;

/**
 * AI-powered combat analysis check
 * Analyzes combat patterns for suspicious behavior
 */
@CheckData(
    name = "AICombat",
    description = "AI-powered combat pattern analysis",
    experimental = true
)
public class AICombatCheck extends Check implements PacketCheck {
    private static final Logger LOGGER = Logger.getLogger("GrimAC-AI-Combat");
    
    private final AIManager aiManager;
    private final double flagThreshold;
    private final double setbackThreshold;
    private final double aiWeight;
    
    // Combat tracking
    private long lastAttackTime;
    private int attackCount;
    private int consecutiveSuspiciousAttacks;
    private double totalReachDistance;
    private int hitCount;
    
    public AICombatCheck(GrimPlayer player) {
        super(player);
        this.aiManager = GrimAPI.INSTANCE.getAIManager();
        
        // Load configuration
        this.flagThreshold = GrimAPI.INSTANCE.getConfigManager().getDoubleElse("ai-checks.combat.flag-threshold", 0.8);
        this.setbackThreshold = GrimAPI.INSTANCE.getConfigManager().getDoubleElse("ai-checks.combat.setback-threshold", 0.9);
        this.aiWeight = GrimAPI.INSTANCE.getConfigManager().getDoubleElse("ai-checks.combat.ai-weight", 0.6);
        
        this.lastAttackTime = 0;
        this.attackCount = 0;
        this.consecutiveSuspiciousAttacks = 0;
        this.totalReachDistance = 0.0;
        this.hitCount = 0;
    }
    
    @Override
    public void onPacketReceive(PacketReceiveEvent event) {
        // Skip if AI is not available or enabled
        if (aiManager == null || !aiManager.isEnabled()) {
            return;
        }
        
        // Skip if player is exempt
        if (player.disableGrim) {
            return;
        }
        
        // Handle attack packets
        if (event.getPacketType() == PacketType.Play.Client.INTERACT_ENTITY) {
            handleAttackPacket(event);
        }
    }
    
    private void handleAttackPacket(PacketReceiveEvent event) {
        try {
            WrapperPlayClientInteractEntity wrapper = new WrapperPlayClientInteractEntity(event);
            
            // Only analyze attack interactions
            if (wrapper.getAction() != WrapperPlayClientInteractEntity.InteractAction.ATTACK) {
                return;
            }
            
            long currentTime = System.currentTimeMillis();
            int entityId = wrapper.getEntityId();
            
            // Update combat statistics
            attackCount++;
            long timeSinceLastAttack = currentTime - lastAttackTime;
            lastAttackTime = currentTime;
            
            // Get target entity position (this would need to be implemented based on entity tracking)
            double[] targetPos = getEntityPosition(entityId);
            if (targetPos == null) {
                return; // Can't analyze without target position
            }
            
            // Calculate reach distance
            double reachDistance = calculateReachDistance(targetPos);
            totalReachDistance += reachDistance;
            
            // Create combat data for AI analysis
            CombatData combatData = new CombatData(
                entityId,
                targetPos[0], targetPos[1], targetPos[2],
                "attack",
                currentTime
            );
            
            // Add additional context
            combatData.addMetadata("timeSinceLastAttack", timeSinceLastAttack);
            combatData.addMetadata("reachDistance", reachDistance);
            combatData.addMetadata("attackCount", attackCount);
            combatData.addMetadata("averageReach", totalReachDistance / attackCount);
            combatData.addMetadata("playerYaw", player.yRot);
            combatData.addMetadata("playerPitch", player.xRot);
            
            // Perform AI analysis asynchronously
            CompletableFuture<AIDetectionResult> analysisResult = aiManager.analyzePlayerBehavior(
                player, "combat", combatData
            );
            
            // Handle the result when it completes
            analysisResult.thenAccept(this::handleAIResult);
            
        } catch (Exception e) {
            LOGGER.warning("Error in AI combat analysis for player " + player.user.getName() + ": " + e.getMessage());
        }
    }
    
    private double[] getEntityPosition(int entityId) {
        // This would need to be implemented based on GrimAC's entity tracking system
        // For now, return a placeholder
        try {
            // Get entity from compensated entities
            var entity = player.compensatedEntities.entityMap.get(entityId);
            if (entity != null) {
                return new double[]{entity.x, entity.y, entity.z};
            }
        } catch (Exception e) {
            LOGGER.fine("Could not get entity position for ID " + entityId);
        }
        return null;
    }
    
    private double calculateReachDistance(double[] targetPos) {
        double dx = player.x - targetPos[0];
        double dy = player.y - targetPos[1];
        double dz = player.z - targetPos[2];
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    
    private void handleAIResult(AIDetectionResult result) {
        if (result == null || result.isError()) {
            return;
        }
        
        // Log detailed analysis if enabled
        if (GrimAPI.INSTANCE.getConfigManager().getBooleanElse("ai-alerts.log-to-console", false)) {
            LOGGER.info("AI Combat Analysis for " + player.user.getName() + ": " + result.toString());
        }
        
        // Check if result is suspicious enough to flag
        if (result.isSuspicious() && result.getConfidence() >= flagThreshold) {
            consecutiveSuspiciousAttacks++;
            
            // Create alert message with AI reasoning
            String alertMessage = String.format("AI Combat Detection - Confidence: %.2f, Type: %s", 
                result.getConfidence(), result.getCheatType() != null ? result.getCheatType() : "Unknown");
            
            if (GrimAPI.INSTANCE.getConfigManager().getBooleanElse("ai-alerts.include-reasoning", true)) {
                alertMessage += " | Reasoning: " + result.getReasoning();
            }
            
            // Add combat statistics to alert
            double averageReach = attackCount > 0 ? totalReachDistance / attackCount : 0.0;
            alertMessage += String.format(" | Stats: Attacks=%d, AvgReach=%.2f", attackCount, averageReach);
            
            // Flag with traditional check system
            if (flagAndAlert(alertMessage)) {
                // Check if we should setback or take stronger action
                if (result.getConfidence() >= setbackThreshold || consecutiveSuspiciousAttacks >= 5) {
                    // For combat, we might want to cancel the attack instead of setback
                    if (result.shouldSetback()) {
                        setbackIfAboveSetbackVL();
                    }
                    consecutiveSuspiciousAttacks = 0; // Reset after action
                }
            }
            
        } else {
            // Reset consecutive count if combat is normal
            consecutiveSuspiciousAttacks = Math.max(0, consecutiveSuspiciousAttacks - 1);
        }
        
        // Update violation level based on AI confidence
        updateViolationLevel(result);
    }
    
    private void updateViolationLevel(AIDetectionResult result) {
        if (!result.isSuspicious()) {
            // Decrease violation level for normal behavior
            violations = Math.max(0, violations - 0.05);
            return;
        }
        
        // Calculate violation increase based on AI confidence and severity
        double baseIncrease = result.getConfidence() * aiWeight;
        
        // Increase based on severity
        double severityMultiplier = switch (result.getSeverity().toLowerCase()) {
            case "critical" -> 2.0;
            case "high" -> 1.5;
            case "medium" -> 1.0;
            case "low" -> 0.5;
            default -> 1.0;
        };
        
        double violationIncrease = baseIncrease * severityMultiplier;
        
        // Apply violation increase
        violations += violationIncrease;
        
        // Cap violations
        violations = Math.min(violations, 100.0);
    }
    
    @Override
    public void reload() {
        super.reload();
        // Reset state on reload
        consecutiveSuspiciousAttacks = 0;
        attackCount = 0;
        totalReachDistance = 0.0;
        hitCount = 0;
    }
    
    // Utility methods for integration
    public boolean isAISuspicious() {
        return consecutiveSuspiciousAttacks > 0;
    }
    
    public int getConsecutiveSuspiciousCount() {
        return consecutiveSuspiciousAttacks;
    }
    
    public double getAverageReachDistance() {
        return attackCount > 0 ? totalReachDistance / attackCount : 0.0;
    }
    
    public double getAccuracy() {
        return attackCount > 0 ? (double) hitCount / attackCount : 0.0;
    }
}
