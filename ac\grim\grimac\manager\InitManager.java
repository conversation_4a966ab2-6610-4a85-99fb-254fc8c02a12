package ac.grim.grimac.manager;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.manager.init.Initable;
import ac.grim.grimac.manager.init.load.PacketEventsInit;
import ac.grim.grimac.manager.init.start.BStats;
import ac.grim.grimac.manager.init.start.CommandRegister;
import ac.grim.grimac.manager.init.start.EventManager;
import ac.grim.grimac.manager.init.start.ExemptOnlinePlayers;
import ac.grim.grimac.manager.init.start.JavaVersion;
import ac.grim.grimac.manager.init.start.PacketLimiter;
import ac.grim.grimac.manager.init.start.PacketManager;
import ac.grim.grimac.manager.init.start.PlaceholderAPIExpansion;
import ac.grim.grimac.manager.init.start.TAB;
import ac.grim.grimac.manager.init.start.TickEndEvent;
import ac.grim.grimac.manager.init.start.TickRunner;
import ac.grim.grimac.manager.init.start.ViaBackwardsManager;
import ac.grim.grimac.manager.init.start.ViaVersion;
import ac.grim.grimac.manager.init.stop.TerminatePacketEvents;
import ac.grim.grimac.utils.anticheat.MessageUtil;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.UnmodifiableIterator;

public class InitManager {
   private final ImmutableList<Initable> initializersOnLoad = ImmutableList.builder().add(new PacketEventsInit()).add(() -> {
      GrimAPI.INSTANCE.getExternalAPI().load();
   }).build();
   private final ImmutableList<Initable> initializersOnStart;
   private final ImmutableList<Initable> initializersOnStop;
   private boolean loaded = false;
   private boolean started = false;
   private boolean stopped = false;

   public InitManager() {
      this.initializersOnStart = ImmutableList.builder().add(GrimAPI.INSTANCE.getExternalAPI()).add(new ExemptOnlinePlayers()).add(new EventManager()).add(new PacketManager()).add(new ViaBackwardsManager()).add(new TickRunner()).add(new TickEndEvent()).add(new CommandRegister()).add(new BStats()).add(new PacketLimiter()).add(GrimAPI.INSTANCE.getDiscordManager()).add(GrimAPI.INSTANCE.getSpectateManager()).add(new JavaVersion()).add(new ViaVersion()).add(new TAB()).add(() -> {
         if (MessageUtil.hasPlaceholderAPI) {
            (new PlaceholderAPIExpansion()).register();
         }

      }).build();
      this.initializersOnStop = ImmutableList.builder().add(new TerminatePacketEvents()).build();
   }

   public void load() {
      UnmodifiableIterator var1 = this.initializersOnLoad.iterator();

      while(var1.hasNext()) {
         Initable initable = (Initable)var1.next();
         this.handle(initable);
      }

      this.loaded = true;
   }

   public void start() {
      UnmodifiableIterator var1 = this.initializersOnStart.iterator();

      while(var1.hasNext()) {
         Initable initable = (Initable)var1.next();
         this.handle(initable);
      }

      this.started = true;
   }

   public void stop() {
      UnmodifiableIterator var1 = this.initializersOnStop.iterator();

      while(var1.hasNext()) {
         Initable initable = (Initable)var1.next();
         this.handle(initable);
      }

      this.stopped = true;
   }

   private void handle(Initable initable) {
      try {
         initable.start();
      } catch (Exception var3) {
         var3.printStackTrace();
      }

   }

   public boolean isLoaded() {
      return this.loaded;
   }

   public boolean isStarted() {
      return this.started;
   }

   public boolean isStopped() {
      return this.stopped;
   }
}
