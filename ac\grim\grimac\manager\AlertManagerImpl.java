package ac.grim.grimac.manager;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.api.alerts.AlertManager;
import ac.grim.grimac.utils.anticheat.MessageUtil;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;
import org.bukkit.entity.Player;

public class AlertManagerImpl implements AlertManager {
   private final Set<Player> enabledAlerts = new CopyOnWriteArraySet(new HashSet());
   private final Set<Player> enabledVerbose = new CopyOnWriteArraySet(new HashSet());
   private final Set<Player> enabledBrands = new CopyOnWriteArraySet(new HashSet());

   public boolean hasAlertsEnabled(Player player) {
      return this.enabledAlerts.contains(player);
   }

   public void toggleAlerts(Player player) {
      String alertString;
      if (!this.enabledAlerts.remove(player)) {
         alertString = GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("alerts-enabled", "%prefix% &fAlerts enabled");
         alertString = MessageUtil.replacePlaceholders((Object)player, (String)alertString);
         MessageUtil.sendMessage(player, MessageUtil.miniMessage(alertString));
         this.enabledAlerts.add(player);
      } else {
         alertString = GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("alerts-disabled", "%prefix% &fAlerts disabled");
         alertString = MessageUtil.replacePlaceholders((Object)player, (String)alertString);
         MessageUtil.sendMessage(player, MessageUtil.miniMessage(alertString));
      }

   }

   public boolean hasVerboseEnabled(Player player) {
      return this.enabledVerbose.contains(player);
   }

   public boolean hasBrandsEnabled(Player player) {
      return this.enabledBrands.contains(player) && player.hasPermission("grim.brand");
   }

   public void toggleVerbose(Player player) {
      String alertString;
      if (!this.enabledVerbose.remove(player)) {
         alertString = GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("verbose-enabled", "%prefix% &fVerbose enabled");
         alertString = MessageUtil.replacePlaceholders((Object)player, (String)alertString);
         MessageUtil.sendMessage(player, MessageUtil.miniMessage(alertString));
         this.enabledVerbose.add(player);
      } else {
         alertString = GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("verbose-disabled", "%prefix% &fVerbose disabled");
         alertString = MessageUtil.replacePlaceholders((Object)player, (String)alertString);
         MessageUtil.sendMessage(player, MessageUtil.miniMessage(alertString));
      }

   }

   public void toggleBrands(Player player) {
      String alertString;
      if (!this.enabledBrands.remove(player)) {
         alertString = GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("brands-enabled", "%prefix% &fBrands enabled");
         alertString = MessageUtil.replacePlaceholders((Object)player, (String)alertString);
         MessageUtil.sendMessage(player, MessageUtil.miniMessage(alertString));
         this.enabledBrands.add(player);
      } else {
         alertString = GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("brands-disabled", "%prefix% &fBrands disabled");
         alertString = MessageUtil.replacePlaceholders((Object)player, (String)alertString);
         MessageUtil.sendMessage(player, MessageUtil.miniMessage(alertString));
      }

   }

   public void handlePlayerQuit(Player player) {
      this.enabledAlerts.remove(player);
      this.enabledVerbose.remove(player);
      this.enabledBrands.remove(player);
   }

   public Set<Player> getEnabledAlerts() {
      return this.enabledAlerts;
   }

   public Set<Player> getEnabledVerbose() {
      return this.enabledVerbose;
   }

   public Set<Player> getEnabledBrands() {
      return this.enabledBrands;
   }
}
