package ac.grim.grimac.manager.init.start;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.commands.GrimAlerts;
import ac.grim.grimac.commands.GrimBrands;
import ac.grim.grimac.commands.GrimDebug;
import ac.grim.grimac.commands.GrimDump;
import ac.grim.grimac.commands.GrimHelp;
import ac.grim.grimac.commands.GrimLog;
import ac.grim.grimac.commands.GrimPerf;
import ac.grim.grimac.commands.GrimProfile;
import ac.grim.grimac.commands.GrimReload;
import ac.grim.grimac.commands.GrimSendAlert;
import ac.grim.grimac.commands.GrimSpectate;
import ac.grim.grimac.commands.GrimStopSpectating;
import ac.grim.grimac.commands.GrimVerbose;
import ac.grim.grimac.commands.GrimVersion;
import ac.grim.grimac.manager.init.Initable;
import ac.grim.grimac.shaded.acf.PaperCommandManager;
import org.bukkit.Bukkit;

public class CommandRegister implements Initable {
   public void start() {
      PaperCommandManager commandManager = new PaperCommandManager(GrimAPI.INSTANCE.getPlugin());
      commandManager.registerCommand(new GrimPerf());
      commandManager.registerCommand(new GrimDebug());
      commandManager.registerCommand(new GrimAlerts());
      commandManager.registerCommand(new GrimProfile());
      commandManager.registerCommand(new GrimSendAlert());
      commandManager.registerCommand(new GrimHelp());
      commandManager.registerCommand(new GrimReload());
      commandManager.registerCommand(new GrimSpectate());
      commandManager.registerCommand(new GrimStopSpectating());
      commandManager.registerCommand(new GrimLog());
      commandManager.registerCommand(new GrimVerbose());
      commandManager.registerCommand(new GrimVersion());
      commandManager.registerCommand(new GrimDump());
      commandManager.registerCommand(new GrimBrands());
      commandManager.getCommandCompletions().registerCompletion("stopspectating", GrimStopSpectating.completionHandler);
      if (GrimAPI.INSTANCE.getConfigManager().getConfig().getBooleanElse("check-for-updates", true)) {
         GrimVersion.checkForUpdatesAsync(Bukkit.getConsoleSender());
      }

   }
}
