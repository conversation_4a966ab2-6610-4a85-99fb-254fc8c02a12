package ac.grim.grimac.checks.impl.exploit;

import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.CheckData;
import ac.grim.grimac.checks.type.PacketCheck;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.PacketEvents;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketReceiveEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.manager.server.ServerVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.packettype.PacketType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientTabComplete;

@CheckData(
   name = "ExploitA",
   experimental = true
)
public class ExploitA extends Check implements PacketCheck {
   public ExploitA(GrimPlayer playerData) {
      super(playerData);
   }

   public void onPacketReceive(PacketReceiveEvent event) {
      if (event.getPacketType() == PacketType.Play.Client.TAB_COMPLETE && PacketEvents.getAPI().getServerManager().getVersion().isNewerThanOrEquals(ServerVersion.V_1_13)) {
         WrapperPlayClientTabComplete wrapper = new WrapperPlayClientTabComplete(event);
         String text = wrapper.getText();
         if ((text.equals("/") || text.trim().isEmpty()) && this.flagAndAlert("")) {
            event.setCancelled(true);
            this.player.onPacketCancel();
         }
      }

   }
}
