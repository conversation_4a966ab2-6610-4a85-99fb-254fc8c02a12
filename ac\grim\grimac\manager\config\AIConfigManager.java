package ac.grim.grimac.manager.config;

import ac.grim.grimac.api.config.ConfigManager;
import ac.grim.grimac.utils.anticheat.LogUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * AI Configuration Manager for GrimAC
 * Handles AI-specific configuration loading and validation
 */
public class AIConfigManager {
    private static final Logger LOGGER = Logger.getLogger("GrimAC-AI-Config");
    
    private ConfigManager config;
    
    // AI System Configuration
    private boolean aiEnabled;
    private boolean trainingMode;
    private int threadPoolSize;
    private int cacheExpirationMinutes;
    private int maxCacheSize;
    
    // OpenAI Configuration
    private String openAIApiKey;
    private String openAIModel;
    private int maxTokens;
    private double temperature;
    
    // Detection Thresholds
    private final Map<String, Double> detectionThresholds;
    private final Map<String, Double> setbackThresholds;
    private final Map<String, Double> aiWeights;
    
    // Training Configuration
    private int minTrainingDataSize;
    private int maxTrainingDataSize;
    private int trainingBatchSize;
    private boolean autoTraining;
    private int autoTrainingIntervalHours;
    
    // Data Collection Configuration
    private final Map<String, Boolean> dataCollectionSettings;
    private int dataRetentionDays;
    
    // Performance Settings
    private int maxAnalysisTime;
    private double minTpsThreshold;
    private int maxConcurrentAnalyses;
    
    // Alert Settings
    private boolean aiAlertsEnabled;
    private double minConfidenceForAlerts;
    private boolean includeReasoningInAlerts;
    private boolean logToConsole;
    private boolean logTraining;
    
    public AIConfigManager() {
        this.detectionThresholds = new HashMap<>();
        this.setbackThresholds = new HashMap<>();
        this.aiWeights = new HashMap<>();
        this.dataCollectionSettings = new HashMap<>();
        
        // Set default values
        setDefaults();
    }
    
    public void load(ConfigManager config) {
        this.config = config;
        
        try {
            loadAISystemConfig();
            loadOpenAIConfig();
            loadThresholds();
            loadTrainingConfig();
            loadDataCollectionConfig();
            loadPerformanceConfig();
            loadAlertConfig();
            
            validateConfiguration();
            
            LOGGER.info("AI configuration loaded successfully");
            LOGGER.info("AI System Status: " + (aiEnabled ? "ENABLED" : "DISABLED"));
            if (aiEnabled) {
                LOGGER.info("Training Mode: " + (trainingMode ? "ENABLED" : "DISABLED"));
                LOGGER.info("OpenAI Model: " + openAIModel);
            }
            
        } catch (Exception e) {
            LOGGER.severe("Failed to load AI configuration: " + e.getMessage());
            LogUtil.warn("AI configuration loading failed. AI features will be disabled.");
            aiEnabled = false;
        }
    }
    
    private void setDefaults() {
        // AI System defaults
        aiEnabled = false;
        trainingMode = false;
        threadPoolSize = 4;
        cacheExpirationMinutes = 30;
        maxCacheSize = 1000;
        
        // OpenAI defaults
        openAIApiKey = "";
        openAIModel = "gpt-4";
        maxTokens = 1000;
        temperature = 0.3;
        
        // Detection threshold defaults
        detectionThresholds.put("movement", 0.7);
        detectionThresholds.put("combat", 0.8);
        detectionThresholds.put("exploit", 0.9);
        detectionThresholds.put("building", 0.6);
        
        // Setback threshold defaults
        setbackThresholds.put("movement", 0.8);
        setbackThresholds.put("combat", 0.9);
        setbackThresholds.put("exploit", 0.95);
        setbackThresholds.put("building", 0.8);
        
        // AI weight defaults
        aiWeights.put("movement", 0.5);
        aiWeights.put("combat", 0.6);
        aiWeights.put("exploit", 0.7);
        aiWeights.put("building", 0.4);
        
        // Training defaults
        minTrainingDataSize = 100;
        maxTrainingDataSize = 10000;
        trainingBatchSize = 50;
        autoTraining = true;
        autoTrainingIntervalHours = 24;
        
        // Data collection defaults
        dataCollectionSettings.put("movement", true);
        dataCollectionSettings.put("combat", true);
        dataCollectionSettings.put("exploit", true);
        dataCollectionSettings.put("building", true);
        dataRetentionDays = 30;
        
        // Performance defaults
        maxAnalysisTime = 5000;
        minTpsThreshold = 18.0;
        maxConcurrentAnalyses = 10;
        
        // Alert defaults
        aiAlertsEnabled = true;
        minConfidenceForAlerts = 0.7;
        includeReasoningInAlerts = true;
        logToConsole = false;
        logTraining = true;
    }
    
    private void loadAISystemConfig() {
        aiEnabled = config.getBooleanElse("ai.enabled", false);
        trainingMode = config.getBooleanElse("ai.training-mode", false);
        threadPoolSize = config.getIntElse("ai.thread-pool-size", 4);
        cacheExpirationMinutes = config.getIntElse("ai.cache.expiration-minutes", 30);
        maxCacheSize = config.getIntElse("ai.cache.max-size", 1000);
    }
    
    private void loadOpenAIConfig() {
        openAIApiKey = config.getStringElse("ai.openai.api-key", "");
        openAIModel = config.getStringElse("ai.openai.model", "gpt-4");
        maxTokens = config.getIntElse("ai.openai.max-tokens", 1000);
        temperature = config.getDoubleElse("ai.openai.temperature", 0.3);
    }
    
    private void loadThresholds() {
        // Detection thresholds
        detectionThresholds.put("movement", config.getDoubleElse("ai.thresholds.movement", 0.7));
        detectionThresholds.put("combat", config.getDoubleElse("ai.thresholds.combat", 0.8));
        detectionThresholds.put("exploit", config.getDoubleElse("ai.thresholds.exploit", 0.9));
        detectionThresholds.put("building", config.getDoubleElse("ai.thresholds.building", 0.6));
        
        // Setback thresholds
        setbackThresholds.put("movement", config.getDoubleElse("ai-checks.movement.setback-threshold", 0.8));
        setbackThresholds.put("combat", config.getDoubleElse("ai-checks.combat.setback-threshold", 0.9));
        setbackThresholds.put("exploit", config.getDoubleElse("ai-checks.exploit.setback-threshold", 0.95));
        setbackThresholds.put("building", config.getDoubleElse("ai-checks.building.setback-threshold", 0.8));
        
        // AI weights
        aiWeights.put("movement", config.getDoubleElse("ai-checks.movement.ai-weight", 0.5));
        aiWeights.put("combat", config.getDoubleElse("ai-checks.combat.ai-weight", 0.6));
        aiWeights.put("exploit", config.getDoubleElse("ai-checks.exploit.ai-weight", 0.7));
        aiWeights.put("building", config.getDoubleElse("ai-checks.building.ai-weight", 0.4));
    }
    
    private void loadTrainingConfig() {
        minTrainingDataSize = config.getIntElse("ai.training.min-data-size", 100);
        maxTrainingDataSize = config.getIntElse("ai.training.max-data-size", 10000);
        trainingBatchSize = config.getIntElse("ai.training.batch-size", 50);
        autoTraining = config.getBooleanElse("ai.training.auto-training", true);
        autoTrainingIntervalHours = config.getIntElse("ai.training.interval-hours", 24);
    }
    
    private void loadDataCollectionConfig() {
        dataCollectionSettings.put("movement", config.getBooleanElse("ai.data-collection.movement", true));
        dataCollectionSettings.put("combat", config.getBooleanElse("ai.data-collection.combat", true));
        dataCollectionSettings.put("exploit", config.getBooleanElse("ai.data-collection.exploit", true));
        dataCollectionSettings.put("building", config.getBooleanElse("ai.data-collection.building", true));
        dataRetentionDays = config.getIntElse("ai.data-collection.retention-days", 30);
    }
    
    private void loadPerformanceConfig() {
        maxAnalysisTime = config.getIntElse("performance.max-analysis-time", 5000);
        minTpsThreshold = config.getDoubleElse("performance.min-tps-threshold", 18.0);
        maxConcurrentAnalyses = config.getIntElse("performance.max-concurrent-analyses", 10);
    }
    
    private void loadAlertConfig() {
        aiAlertsEnabled = config.getBooleanElse("ai-alerts.enabled", true);
        minConfidenceForAlerts = config.getDoubleElse("ai-alerts.min-confidence", 0.7);
        includeReasoningInAlerts = config.getBooleanElse("ai-alerts.include-reasoning", true);
        logToConsole = config.getBooleanElse("ai-alerts.log-to-console", false);
        logTraining = config.getBooleanElse("ai-alerts.log-training", true);
    }
    
    private void validateConfiguration() {
        // Validate AI system settings
        if (threadPoolSize < 1 || threadPoolSize > 20) {
            throw new IllegalArgumentException("Thread pool size must be between 1 and 20");
        }
        
        if (cacheExpirationMinutes < 1 || cacheExpirationMinutes > 1440) {
            throw new IllegalArgumentException("Cache expiration must be between 1 and 1440 minutes");
        }
        
        if (maxCacheSize < 10 || maxCacheSize > 100000) {
            throw new IllegalArgumentException("Max cache size must be between 10 and 100000");
        }
        
        // Validate OpenAI settings
        if (aiEnabled && openAIApiKey.isEmpty()) {
            throw new IllegalArgumentException("OpenAI API key is required when AI is enabled");
        }
        
        if (temperature < 0.0 || temperature > 2.0) {
            throw new IllegalArgumentException("Temperature must be between 0.0 and 2.0");
        }
        
        // Validate thresholds
        for (Double threshold : detectionThresholds.values()) {
            if (threshold < 0.0 || threshold > 1.0) {
                throw new IllegalArgumentException("Detection thresholds must be between 0.0 and 1.0");
            }
        }
        
        // Validate training settings
        if (minTrainingDataSize < 10 || minTrainingDataSize > 10000) {
            throw new IllegalArgumentException("Min training data size must be between 10 and 10000");
        }
        
        if (maxTrainingDataSize < minTrainingDataSize) {
            throw new IllegalArgumentException("Max training data size must be >= min training data size");
        }
    }
    
    // Getters
    public boolean isAIEnabled() { return aiEnabled && !openAIApiKey.isEmpty(); }
    public boolean isTrainingMode() { return trainingMode; }
    public int getThreadPoolSize() { return threadPoolSize; }
    public int getCacheExpirationMinutes() { return cacheExpirationMinutes; }
    public int getMaxCacheSize() { return maxCacheSize; }
    
    public String getOpenAIApiKey() { return openAIApiKey; }
    public String getOpenAIModel() { return openAIModel; }
    public int getMaxTokens() { return maxTokens; }
    public double getTemperature() { return temperature; }
    
    public double getDetectionThreshold(String behaviorType) {
        return detectionThresholds.getOrDefault(behaviorType, 0.7);
    }
    
    public double getSetbackThreshold(String behaviorType) {
        return setbackThresholds.getOrDefault(behaviorType, 0.8);
    }
    
    public double getAIWeight(String behaviorType) {
        return aiWeights.getOrDefault(behaviorType, 0.5);
    }
    
    public int getMinTrainingDataSize() { return minTrainingDataSize; }
    public int getMaxTrainingDataSize() { return maxTrainingDataSize; }
    public int getTrainingBatchSize() { return trainingBatchSize; }
    public boolean isAutoTraining() { return autoTraining; }
    public int getAutoTrainingIntervalHours() { return autoTrainingIntervalHours; }
    
    public boolean isDataCollectionEnabled(String behaviorType) {
        return dataCollectionSettings.getOrDefault(behaviorType, true);
    }
    
    public int getDataRetentionDays() { return dataRetentionDays; }
    
    public int getMaxAnalysisTime() { return maxAnalysisTime; }
    public double getMinTpsThreshold() { return minTpsThreshold; }
    public int getMaxConcurrentAnalyses() { return maxConcurrentAnalyses; }
    
    public boolean isAIAlertsEnabled() { return aiAlertsEnabled; }
    public double getMinConfidenceForAlerts() { return minConfidenceForAlerts; }
    public boolean isIncludeReasoningInAlerts() { return includeReasoningInAlerts; }
    public boolean isLogToConsole() { return logToConsole; }
    public boolean isLogTraining() { return logTraining; }
    
    public String getConfigSummary() {
        return String.format(
            "AI Config - Enabled: %s, Training: %s, Model: %s, Threads: %d, Cache: %d entries/%d min",
            isAIEnabled(), isTrainingMode(), getOpenAIModel(), 
            getThreadPoolSize(), getMaxCacheSize(), getCacheExpirationMinutes()
        );
    }
}
