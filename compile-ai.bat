@echo off
echo ========================================
echo GrimAC AI Enhancement Compilation
echo ========================================
echo.

REM Check if Java is available
javac -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java compiler (javac) is not installed or not in PATH
    pause
    exit /b 1
)

echo Java compiler found.

REM Check for lib directory
if not exist "lib" (
    echo ERROR: lib directory not found!
    echo Please create a 'lib' directory and add the required dependencies:
    echo - bukkit-api.jar or spigot-api.jar
    echo - gson.jar
    echo - packetevents.jar
    echo - Any other GrimAC dependencies
    pause
    exit /b 1
)

REM Build classpath from lib directory
set CLASSPATH=.
for %%f in (lib\*.jar) do (
    set CLASSPATH=!CLASSPATH!;%%f
)

echo Building classpath from lib directory...
echo Classpath: %CLASSPATH%
echo.

REM Create output directory
if not exist "build" mkdir build

echo Compiling AI enhancement classes...
echo.

REM Compile AI core classes
echo Compiling AI Manager...
javac -cp "%CLASSPATH%" -d build ac/grim/grimac/manager/AIManager.java
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile AIManager
    pause
    exit /b 1
)

echo Compiling AI Configuration...
javac -cp "%CLASSPATH%" -d build ac/grim/grimac/utils/ai/AIConfig.java
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile AIConfig
    pause
    exit /b 1
)

echo Compiling OpenAI Client...
javac -cp "%CLASSPATH%" -d build ac/grim/grimac/utils/ai/OpenAIClient.java
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile OpenAIClient
    pause
    exit /b 1
)

echo Compiling AI Data Collector...
javac -cp "%CLASSPATH%" -d build ac/grim/grimac/utils/ai/AIDataCollector.java
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile AIDataCollector
    pause
    exit /b 1
)

echo Compiling AI Training Manager...
javac -cp "%CLASSPATH%" -d build ac/grim/grimac/utils/ai/AITrainingManager.java
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile AITrainingManager
    pause
    exit /b 1
)

REM Compile AI model classes
echo Compiling AI Models...
javac -cp "%CLASSPATH%" -d build ac/grim/grimac/utils/ai/models/*.java
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile AI models
    pause
    exit /b 1
)

REM Compile AI checks
echo Compiling AI Checks...
javac -cp "%CLASSPATH%" -d build ac/grim/grimac/checks/impl/ai/*.java
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile AI checks
    pause
    exit /b 1
)

REM Compile AI training system
echo Compiling AI Training System...
javac -cp "%CLASSPATH%" -d build ac/grim/grimac/utils/ai/training/*.java
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile AI training system
    pause
    exit /b 1
)

REM Compile AI learning system
echo Compiling AI Learning System...
javac -cp "%CLASSPATH%" -d build ac/grim/grimac/utils/ai/learning/*.java
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile AI learning system
    pause
    exit /b 1
)

REM Compile AI integration
echo Compiling AI Integration...
javac -cp "%CLASSPATH%" -d build ac/grim/grimac/utils/ai/integration/*.java
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile AI integration
    pause
    exit /b 1
)

REM Compile AI configuration manager
echo Compiling AI Configuration Manager...
javac -cp "%CLASSPATH%" -d build ac/grim/grimac/manager/config/AIConfigManager.java
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile AI configuration manager
    pause
    exit /b 1
)

REM Compile AI commands
echo Compiling AI Commands...
javac -cp "%CLASSPATH%" -d build ac/grim/grimac/commands/GrimAI.java
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile AI commands
    pause
    exit /b 1
)

echo.
echo ========================================
echo COMPILATION SUCCESSFUL!
echo ========================================
echo.
echo All AI enhancement classes have been compiled successfully.
echo Compiled classes are in the 'build' directory.
echo.
echo Next steps:
echo 1. Copy the config/ai.yml to your GrimAC config directory
echo 2. Add your OpenAI API key to the configuration
echo 3. Package the compiled classes with the original GrimAC JAR
echo 4. Test the AI features in a development environment
echo.
echo To create a JAR with the AI enhancements:
echo jar -cf grimac-ai-enhanced.jar -C build . -C . config/ai.yml
echo.
pause
