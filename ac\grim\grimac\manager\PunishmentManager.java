package ac.grim.grimac.manager;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.api.AbstractCheck;
import ac.grim.grimac.api.config.ConfigManager;
import ac.grim.grimac.api.config.ConfigReloadable;
import ac.grim.grimac.api.events.CommandExecuteEvent;
import ac.grim.grimac.checks.Check;
import ac.grim.grimac.events.packets.ProxyAlertMessenger;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.io.github.retrooper.packetevents.util.folia.FoliaScheduler;
import ac.grim.grimac.shaded.kyori.adventure.text.Component;
import ac.grim.grimac.utils.anticheat.LogUtil;
import ac.grim.grimac.utils.anticheat.MessageUtil;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

public class PunishmentManager implements ConfigReloadable {
   GrimPlayer player;
   List<PunishGroup> groups = new ArrayList();
   String experimentalSymbol = "*";
   private String alertString;
   private boolean testMode;
   private boolean printToConsole;
   private String proxyAlertString = "";

   public PunishmentManager(GrimPlayer player) {
      this.player = player;
   }

   public void reload(ConfigManager config) {
      List<String> punish = config.getStringListElse("Punishments", new ArrayList());
      this.experimentalSymbol = config.getStringElse("experimental-symbol", "*");
      this.alertString = config.getStringElse("alerts-format", "%prefix% &f%player% &bfailed &f%check_name% &f(x&c%vl%&f) &7%verbose%");
      this.testMode = config.getBooleanElse("test-mode", false);
      this.printToConsole = config.getBooleanElse("verbose.print-to-console", false);
      this.proxyAlertString = config.getStringElse("alerts-format-proxy", "%prefix% &f[&cproxy&f] &f%player% &bfailed &f%check_name% &f(x&c%vl%&f) &7%verbose%");

      try {
         this.groups.clear();
         Iterator var3 = this.player.checkManager.allChecks.values().iterator();

         while(var3.hasNext()) {
            AbstractCheck check = (AbstractCheck)var3.next();
            check.setEnabled(false);
         }

         var3 = punish.iterator();

         while(var3.hasNext()) {
            Object s = var3.next();
            LinkedHashMap<String, Object> map = (LinkedHashMap)s;
            List<String> checks = (List)map.getOrDefault("checks", new ArrayList());
            List<String> commands = (List)map.getOrDefault("commands", new ArrayList());
            int removeViolationsAfter = (Integer)map.getOrDefault("remove-violations-after", 300);
            List<ParsedCommand> parsed = new ArrayList();
            List<AbstractCheck> checksList = new ArrayList();
            List<AbstractCheck> excluded = new ArrayList();
            Iterator var12 = checks.iterator();

            String command;
            label66:
            while(var12.hasNext()) {
               command = (String)var12.next();
               command = command.toLowerCase(Locale.ROOT);
               boolean exclude = false;
               if (command.startsWith("!")) {
                  exclude = true;
                  command = command.substring(1);
               }

               Iterator var15 = this.player.checkManager.allChecks.values().iterator();

               while(true) {
                  AbstractCheck check;
                  do {
                     do {
                        if (!var15.hasNext()) {
                           var15 = excluded.iterator();

                           while(var15.hasNext()) {
                              check = (AbstractCheck)var15.next();
                              checksList.remove(check);
                           }
                           continue label66;
                        }

                        check = (AbstractCheck)var15.next();
                     } while(check.getCheckName() == null);
                  } while(!check.getCheckName().toLowerCase(Locale.ROOT).contains(command) && !check.getAlternativeName().toLowerCase(Locale.ROOT).contains(command));

                  if (exclude) {
                     excluded.add(check);
                  } else {
                     checksList.add(check);
                     check.setEnabled(true);
                  }
               }
            }

            var12 = commands.iterator();

            while(var12.hasNext()) {
               command = (String)var12.next();
               String firstNum = command.substring(0, command.indexOf(":"));
               String secondNum = command.substring(command.indexOf(":"), command.indexOf(" "));
               int threshold = Integer.parseInt(firstNum);
               int interval = Integer.parseInt(secondNum.substring(1));
               String commandString = command.substring(command.indexOf(" ") + 1);
               parsed.add(new ParsedCommand(threshold, interval, commandString));
            }

            this.groups.add(new PunishGroup(checksList, parsed, removeViolationsAfter));
         }
      } catch (Exception var19) {
         LogUtil.error("Error while loading punishments.yml! This is likely your fault!");
         var19.printStackTrace();
      }

   }

   private String replaceAlertPlaceholders(String original, int vl, Check check, String alertString, String verbose) {
      return MessageUtil.replacePlaceholders(this.player, original.replace("[alert]", alertString).replace("[proxy]", alertString).replace("%check_name%", check.getDisplayName()).replace("%experimental%", check.isExperimental() ? this.experimentalSymbol : "").replace("%vl%", Integer.toString(vl)).replace("%verbose%", verbose).replace("%description%", check.getDescription()));
   }

   public boolean handleAlert(GrimPlayer player, String verbose, Check check) {
      boolean sentDebug = false;
      Iterator var5 = this.groups.iterator();

      label85:
      while(true) {
         PunishGroup group;
         do {
            if (!var5.hasNext()) {
               return sentDebug;
            }

            group = (PunishGroup)var5.next();
         } while(!group.checks.contains(check));

         int vl = this.getViolations(group, check);
         int violationCount = group.violations.size();
         Iterator var9 = group.commands.iterator();

         while(true) {
            while(true) {
               ParsedCommand command;
               String cmd;
               do {
                  if (!var9.hasNext()) {
                     continue label85;
                  }

                  command = (ParsedCommand)var9.next();
                  cmd = this.replaceAlertPlaceholders(command.command, vl, check, this.alertString, verbose);
                  if (!GrimAPI.INSTANCE.getAlertManager().getEnabledVerbose().isEmpty() && command.command.equals("[alert]")) {
                     sentDebug = true;
                     Component component = MessageUtil.miniMessage(cmd);
                     Iterator var13 = GrimAPI.INSTANCE.getAlertManager().getEnabledVerbose().iterator();

                     while(var13.hasNext()) {
                        Player bukkitPlayer = (Player)var13.next();
                        MessageUtil.sendMessage(bukkitPlayer, component);
                     }

                     if (this.printToConsole) {
                        LogUtil.console(component);
                     }
                  }
               } while(violationCount < command.threshold);

               boolean inInterval = command.interval == 0 ? command.executeCount == 0 : violationCount % command.interval == 0;
               if (inInterval) {
                  CommandExecuteEvent executeEvent = new CommandExecuteEvent(player, check, verbose, cmd);
                  Bukkit.getPluginManager().callEvent(executeEvent);
                  if (executeEvent.isCancelled()) {
                     continue;
                  }

                  if (command.command.equals("[webhook]")) {
                     GrimAPI.INSTANCE.getDiscordManager().sendAlert(player, verbose, check.getDisplayName(), vl);
                  } else if (command.command.equals("[proxy]")) {
                     ProxyAlertMessenger.sendPluginMessage(this.replaceAlertPlaceholders(command.command, vl, check, this.proxyAlertString, verbose));
                  } else {
                     if (command.command.equals("[alert]")) {
                        sentDebug = true;
                        if (this.testMode) {
                           player.user.sendMessage(MessageUtil.miniMessage(cmd));
                           continue;
                        }

                        cmd = "grim sendalert " + cmd;
                     }

                     FoliaScheduler.getGlobalRegionScheduler().run(GrimAPI.INSTANCE.getPlugin(), (dummy) -> {
                        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), cmd);
                     });
                  }
               }

               ++command.executeCount;
            }
         }
      }
   }

   public void handleViolation(Check check) {
      Iterator var2 = this.groups.iterator();

      while(var2.hasNext()) {
         PunishGroup group = (PunishGroup)var2.next();
         if (group.checks.contains(check)) {
            long currentTime = System.currentTimeMillis();
            group.violations.put(currentTime, check);
            group.violations.entrySet().removeIf((time) -> {
               return currentTime - (Long)time.getKey() > (long)group.removeViolationsAfter;
            });
         }
      }

   }

   private int getViolations(PunishGroup group, Check check) {
      int vl = 0;
      Iterator var4 = group.violations.values().iterator();

      while(var4.hasNext()) {
         Check value = (Check)var4.next();
         if (value == check) {
            ++vl;
         }
      }

      return vl;
   }
}
