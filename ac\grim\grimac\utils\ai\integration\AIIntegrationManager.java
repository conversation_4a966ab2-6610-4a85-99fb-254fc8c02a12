package ac.grim.grimac.utils.ai.integration;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.checks.impl.ai.*;
import ac.grim.grimac.manager.AIManager;
import ac.grim.grimac.manager.CheckManager;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.utils.ai.models.AIDetectionResult;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Manages integration between AI checks and traditional GrimAC checks
 * Provides seamless blending of AI and rule-based detection
 */
public class AIIntegrationManager {
    private static final Logger LOGGER = Logger.getLogger("GrimAC-AI-Integration");
    
    private final AIManager aiManager;
    private final Map<String, AICheckIntegration> integrations;
    
    public AIIntegrationManager(AIManager aiManager) {
        this.aiManager = aiManager;
        this.integrations = new HashMap<>();
        
        initializeIntegrations();
        LOGGER.info("AI Integration Manager initialized");
    }
    
    /**
     * Initialize AI check integrations
     */
    private void initializeIntegrations() {
        // Movement integration
        integrations.put("movement", new AICheckIntegration(
            "movement",
            AIMovementCheck.class,
            0.5, // AI weight
            true // Enabled by default
        ));
        
        // Combat integration
        integrations.put("combat", new AICheckIntegration(
            "combat",
            AICombatCheck.class,
            0.6, // AI weight
            true
        ));
        
        // Exploit integration
        integrations.put("exploit", new AICheckIntegration(
            "exploit",
            AIExploitCheck.class,
            0.7, // AI weight
            true
        ));
        
        // Building integration
        integrations.put("building", new AICheckIntegration(
            "building",
            AIBuildingCheck.class,
            0.4, // AI weight
            true
        ));
    }
    
    /**
     * Integrate AI result with traditional check result
     */
    public IntegratedResult integrateResults(String checkType, 
                                           AIDetectionResult aiResult, 
                                           TraditionalCheckResult traditionalResult,
                                           GrimPlayer player) {
        
        AICheckIntegration integration = integrations.get(checkType);
        if (integration == null || !integration.enabled) {
            // Fall back to traditional result
            return IntegratedResult.fromTraditional(traditionalResult);
        }
        
        try {
            // Get AI weight for this check type
            double aiWeight = integration.aiWeight;
            double traditionalWeight = 1.0 - aiWeight;
            
            // Calculate integrated confidence
            double aiConfidence = aiResult != null && !aiResult.isError() ? aiResult.getConfidence() : 0.0;
            double traditionalConfidence = traditionalResult.confidence;
            
            double integratedConfidence = (aiConfidence * aiWeight) + (traditionalConfidence * traditionalWeight);
            
            // Determine if result is suspicious
            boolean aiSuspicious = aiResult != null && aiResult.isSuspicious();
            boolean traditionalSuspicious = traditionalResult.suspicious;
            
            // Use weighted voting for final decision
            boolean integratedSuspicious = (aiSuspicious && aiWeight >= 0.5) || 
                                         (traditionalSuspicious && traditionalWeight >= 0.5) ||
                                         (aiSuspicious && traditionalSuspicious);
            
            // Calculate integrated violation level
            double aiViolation = aiSuspicious ? aiConfidence * 2.0 : 0.0;
            double traditionalViolation = traditionalResult.violationLevel;
            double integratedViolation = (aiViolation * aiWeight) + (traditionalViolation * traditionalWeight);
            
            // Create reasoning
            String reasoning = buildIntegratedReasoning(aiResult, traditionalResult, aiWeight);
            
            // Determine severity
            String severity = determineIntegratedSeverity(aiResult, traditionalResult, integratedConfidence);
            
            return new IntegratedResult(
                integratedSuspicious,
                integratedConfidence,
                integratedViolation,
                reasoning,
                severity,
                checkType,
                aiResult,
                traditionalResult
            );
            
        } catch (Exception e) {
            LOGGER.warning("Error integrating AI and traditional results: " + e.getMessage());
            // Fall back to traditional result on error
            return IntegratedResult.fromTraditional(traditionalResult);
        }
    }
    
    /**
     * Build reasoning that combines AI and traditional check insights
     */
    private String buildIntegratedReasoning(AIDetectionResult aiResult, 
                                          TraditionalCheckResult traditionalResult,
                                          double aiWeight) {
        StringBuilder reasoning = new StringBuilder();
        
        if (aiResult != null && !aiResult.isError()) {
            reasoning.append("AI Analysis (").append(String.format("%.0f%%", aiWeight * 100)).append("): ");
            reasoning.append(aiResult.getReasoning());
        }
        
        if (traditionalResult.reasoning != null && !traditionalResult.reasoning.isEmpty()) {
            if (reasoning.length() > 0) {
                reasoning.append(" | ");
            }
            reasoning.append("Traditional Check (").append(String.format("%.0f%%", (1.0 - aiWeight) * 100)).append("): ");
            reasoning.append(traditionalResult.reasoning);
        }
        
        return reasoning.toString();
    }
    
    /**
     * Determine integrated severity level
     */
    private String determineIntegratedSeverity(AIDetectionResult aiResult, 
                                             TraditionalCheckResult traditionalResult,
                                             double integratedConfidence) {
        
        String aiSeverity = aiResult != null ? aiResult.getSeverity() : "none";
        String traditionalSeverity = traditionalResult.severity;
        
        // Use the higher severity level
        if ("critical".equals(aiSeverity) || "critical".equals(traditionalSeverity)) {
            return "critical";
        } else if ("high".equals(aiSeverity) || "high".equals(traditionalSeverity)) {
            return "high";
        } else if ("medium".equals(aiSeverity) || "medium".equals(traditionalSeverity)) {
            return "medium";
        } else if (integratedConfidence >= 0.8) {
            return "high";
        } else if (integratedConfidence >= 0.6) {
            return "medium";
        } else {
            return "low";
        }
    }
    
    /**
     * Get AI check instance for a player
     */
    public <T> T getAICheck(GrimPlayer player, Class<T> checkClass) {
        CheckManager checkManager = player.checkManager;
        
        // This would need to be implemented based on how GrimAC's check system works
        // For now, return null as placeholder
        return null;
    }
    
    /**
     * Enable/disable AI integration for a specific check type
     */
    public void setIntegrationEnabled(String checkType, boolean enabled) {
        AICheckIntegration integration = integrations.get(checkType);
        if (integration != null) {
            integration.enabled = enabled;
            LOGGER.info("AI integration for " + checkType + " " + (enabled ? "enabled" : "disabled"));
        }
    }
    
    /**
     * Update AI weight for a specific check type
     */
    public void updateAIWeight(String checkType, double weight) {
        AICheckIntegration integration = integrations.get(checkType);
        if (integration != null && weight >= 0.0 && weight <= 1.0) {
            integration.aiWeight = weight;
            LOGGER.info("AI weight for " + checkType + " updated to " + weight);
        }
    }
    
    /**
     * Get integration statistics
     */
    public Map<String, IntegrationStats> getIntegrationStats() {
        Map<String, IntegrationStats> stats = new HashMap<>();
        
        for (Map.Entry<String, AICheckIntegration> entry : integrations.entrySet()) {
            AICheckIntegration integration = entry.getValue();
            stats.put(entry.getKey(), new IntegrationStats(
                integration.enabled,
                integration.aiWeight,
                integration.checkClass.getSimpleName()
            ));
        }
        
        return stats;
    }
    
    // Data classes
    public static class AICheckIntegration {
        public final String checkType;
        public final Class<?> checkClass;
        public double aiWeight;
        public boolean enabled;
        
        public AICheckIntegration(String checkType, Class<?> checkClass, double aiWeight, boolean enabled) {
            this.checkType = checkType;
            this.checkClass = checkClass;
            this.aiWeight = aiWeight;
            this.enabled = enabled;
        }
    }
    
    public static class TraditionalCheckResult {
        public final boolean suspicious;
        public final double confidence;
        public final double violationLevel;
        public final String reasoning;
        public final String severity;
        
        public TraditionalCheckResult(boolean suspicious, double confidence, double violationLevel, 
                                    String reasoning, String severity) {
            this.suspicious = suspicious;
            this.confidence = confidence;
            this.violationLevel = violationLevel;
            this.reasoning = reasoning;
            this.severity = severity;
        }
        
        public static TraditionalCheckResult createNormal() {
            return new TraditionalCheckResult(false, 0.1, 0.0, "Normal behavior", "none");
        }
        
        public static TraditionalCheckResult createSuspicious(double confidence, double violationLevel, String reasoning) {
            return new TraditionalCheckResult(true, confidence, violationLevel, reasoning, "medium");
        }
    }
    
    public static class IntegratedResult {
        public final boolean suspicious;
        public final double confidence;
        public final double violationLevel;
        public final String reasoning;
        public final String severity;
        public final String checkType;
        public final AIDetectionResult aiResult;
        public final TraditionalCheckResult traditionalResult;
        
        public IntegratedResult(boolean suspicious, double confidence, double violationLevel,
                              String reasoning, String severity, String checkType,
                              AIDetectionResult aiResult, TraditionalCheckResult traditionalResult) {
            this.suspicious = suspicious;
            this.confidence = confidence;
            this.violationLevel = violationLevel;
            this.reasoning = reasoning;
            this.severity = severity;
            this.checkType = checkType;
            this.aiResult = aiResult;
            this.traditionalResult = traditionalResult;
        }
        
        public static IntegratedResult fromTraditional(TraditionalCheckResult traditionalResult) {
            return new IntegratedResult(
                traditionalResult.suspicious,
                traditionalResult.confidence,
                traditionalResult.violationLevel,
                traditionalResult.reasoning,
                traditionalResult.severity,
                "traditional",
                null,
                traditionalResult
            );
        }
        
        public boolean shouldFlag() {
            return suspicious && confidence >= 0.5;
        }
        
        public boolean shouldSetback() {
            return suspicious && confidence >= 0.7;
        }
    }
    
    public static class IntegrationStats {
        public final boolean enabled;
        public final double aiWeight;
        public final String checkClassName;
        
        public IntegrationStats(boolean enabled, double aiWeight, String checkClassName) {
            this.enabled = enabled;
            this.aiWeight = aiWeight;
            this.checkClassName = checkClassName;
        }
    }
}
