package ac.grim.grimac.manager;

import ac.grim.grimac.api.AbstractCheck;
import ac.grim.grimac.checks.Check;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

class PunishGroup {
   public final List<AbstractCheck> checks;
   public final List<ParsedCommand> commands;
   public final Map<Long, Check> violations = new HashMap();
   public final int removeViolationsAfter;

   public PunishGroup(List<AbstractCheck> checks, List<ParsedCommand> commands, int removeViolationsAfter) {
      this.checks = checks;
      this.commands = commands;
      this.removeViolationsAfter = removeViolationsAfter * 1000;
   }
}
