package ac.grim.grimac.manager;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.utils.ai.AIConfig;
import ac.grim.grimac.utils.ai.AIDataCollector;
import ac.grim.grimac.utils.ai.AITrainingManager;
import ac.grim.grimac.utils.ai.OpenAIClient;
import ac.grim.grimac.utils.ai.models.PlayerBehaviorData;
import ac.grim.grimac.utils.ai.models.AIDetectionResult;
import ac.grim.grimac.utils.ai.models.AITrainingData;
import org.bukkit.Bukkit;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.logging.Logger;

/**
 * Central AI Manager for GrimAC
 * Handles AI-powered anti-cheat detection, training, and analysis
 */
public class AIManager {
    private static final Logger LOGGER = Logger.getLogger("GrimAC-AI");
    
    private final AIConfig config;
    private final OpenAIClient openAIClient;
    private final AIDataCollector dataCollector;
    private final AITrainingManager trainingManager;
    private final ExecutorService aiExecutor;
    
    // Player behavior tracking
    private final Map<UUID, PlayerBehaviorData> playerBehaviorMap;
    
    // AI analysis cache
    private final Map<String, AIDetectionResult> analysisCache;
    
    private boolean enabled;
    private boolean trainingMode;
    
    public AIManager() {
        this.config = new AIConfig();
        this.openAIClient = new OpenAIClient(config);
        this.dataCollector = new AIDataCollector();
        this.trainingManager = new AITrainingManager(openAIClient);
        this.aiExecutor = Executors.newFixedThreadPool(config.getThreadPoolSize());
        
        this.playerBehaviorMap = new ConcurrentHashMap<>();
        this.analysisCache = new ConcurrentHashMap<>();
        
        this.enabled = config.isAIEnabled();
        this.trainingMode = config.isTrainingMode();
        
        LOGGER.info("AI Manager initialized - Enabled: " + enabled + ", Training Mode: " + trainingMode);
        
        // Start periodic tasks
        startPeriodicTasks();
    }
    
    /**
     * Analyze player behavior using AI
     */
    public CompletableFuture<AIDetectionResult> analyzePlayerBehavior(GrimPlayer player, String behaviorType, Object behaviorData) {
        if (!enabled) {
            return CompletableFuture.completedFuture(AIDetectionResult.createNeutral());
        }
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Get or create player behavior data
                PlayerBehaviorData playerData = getOrCreatePlayerData(player);
                
                // Update behavior data
                dataCollector.updatePlayerBehavior(playerData, behaviorType, behaviorData);
                
                // Check cache first
                String cacheKey = generateCacheKey(player.uuid, behaviorType, behaviorData);
                AIDetectionResult cachedResult = analysisCache.get(cacheKey);
                if (cachedResult != null && !cachedResult.isExpired()) {
                    return cachedResult;
                }
                
                // Perform AI analysis
                AIDetectionResult result = performAIAnalysis(playerData, behaviorType, behaviorData);
                
                // Cache result
                analysisCache.put(cacheKey, result);
                
                // If training mode, collect this data for training
                if (trainingMode) {
                    trainingManager.addTrainingData(new AITrainingData(playerData, behaviorType, behaviorData, result));
                }
                
                return result;
                
            } catch (Exception e) {
                LOGGER.severe("Error during AI analysis: " + e.getMessage());
                return AIDetectionResult.createError("AI analysis failed: " + e.getMessage());
            }
        }, aiExecutor);
    }
    
    /**
     * Train the AI with collected data
     */
    public CompletableFuture<Boolean> trainAI() {
        if (!enabled || !trainingMode) {
            return CompletableFuture.completedFuture(false);
        }
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                LOGGER.info("Starting AI training session...");
                boolean success = trainingManager.performTraining();
                LOGGER.info("AI training completed. Success: " + success);
                return success;
            } catch (Exception e) {
                LOGGER.severe("AI training failed: " + e.getMessage());
                return false;
            }
        }, aiExecutor);
    }
    
    private PlayerBehaviorData getOrCreatePlayerData(GrimPlayer player) {
        return playerBehaviorMap.computeIfAbsent(player.uuid, uuid -> new PlayerBehaviorData(uuid, player.user.getName()));
    }
    
    private AIDetectionResult performAIAnalysis(PlayerBehaviorData playerData, String behaviorType, Object behaviorData) {
        // This will be implemented with specific AI analysis logic
        return openAIClient.analyzePlayerBehavior(playerData, behaviorType, behaviorData);
    }
    
    private String generateCacheKey(UUID playerId, String behaviorType, Object behaviorData) {
        return playerId.toString() + ":" + behaviorType + ":" + behaviorData.hashCode();
    }
    
    private void startPeriodicTasks() {
        // Cleanup expired cache entries every 5 minutes
        new BukkitRunnable() {
            @Override
            public void run() {
                cleanupCache();
            }
        }.runTaskTimerAsynchronously(GrimAPI.INSTANCE.getPlugin(), 6000L, 6000L); // 5 minutes
        
        // Auto-training every hour if in training mode
        new BukkitRunnable() {
            @Override
            public void run() {
                if (trainingMode && trainingManager.hasEnoughDataForTraining()) {
                    trainAI();
                }
            }
        }.runTaskTimerAsynchronously(GrimAPI.INSTANCE.getPlugin(), 72000L, 72000L); // 1 hour
    }
    
    private void cleanupCache() {
        analysisCache.entrySet().removeIf(entry -> entry.getValue().isExpired());
        LOGGER.fine("AI cache cleanup completed. Remaining entries: " + analysisCache.size());
    }
    
    // Getters and configuration methods
    public boolean isEnabled() { return enabled; }
    public boolean isTrainingMode() { return trainingMode; }
    public AIConfig getConfig() { return config; }
    public AIDataCollector getDataCollector() { return dataCollector; }
    public AITrainingManager getTrainingManager() { return trainingManager; }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        LOGGER.info("AI Manager enabled state changed to: " + enabled);
    }
    
    public void setTrainingMode(boolean trainingMode) {
        this.trainingMode = trainingMode;
        LOGGER.info("AI Training mode changed to: " + trainingMode);
    }
    
    public void shutdown() {
        LOGGER.info("Shutting down AI Manager...");
        aiExecutor.shutdown();
        openAIClient.shutdown();
        LOGGER.info("AI Manager shutdown complete.");
    }
}
