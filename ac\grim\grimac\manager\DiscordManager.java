package ac.grim.grimac.manager;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.manager.init.Initable;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.discord-webhooks.discord.webhook.WebhookClient;
import ac.grim.grimac.shaded.discord-webhooks.discord.webhook.send.WebhookEmbed;
import ac.grim.grimac.shaded.discord-webhooks.discord.webhook.send.WebhookEmbedBuilder;
import ac.grim.grimac.utils.anticheat.LogUtil;
import ac.grim.grimac.utils.anticheat.MessageUtil;
import java.awt.Color;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DiscordManager implements Initable {
   private static WebhookClient client;
   private int embedColor;
   private String staticContent = "";
   private String embedTitle = "";
   public static final Pattern WEBHOOK_PATTERN = Pattern.compile("(?:https?://)?(?:\\w+\\.)?\\w+\\.\\w+/api(?:/v\\d+)?/webhooks/(\\d+)/([\\w-]+)(?:/(?:\\w+)?)?");

   public void start() {
      try {
         if (!GrimAPI.INSTANCE.getConfigManager().getConfig().getBooleanElse("enabled", false)) {
            return;
         }

         String webhook = GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("webhook", "");
         if (webhook.isEmpty()) {
            LogUtil.warn("Discord webhook is empty, disabling Discord alerts");
            client = null;
            return;
         }

         Matcher matcher = WEBHOOK_PATTERN.matcher(webhook);
         if (!matcher.matches()) {
            throw new IllegalArgumentException("Failed to parse webhook URL");
         }

         client = WebhookClient.withId(Long.parseUnsignedLong(matcher.group(1)), matcher.group(2));
         client.setTimeout(15000L);
         this.embedTitle = GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("embed-title", "**Grim Alert**");

         try {
            this.embedColor = Color.decode(GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("embed-color", "#00FFFF")).getRGB();
         } catch (NumberFormatException var6) {
            LogUtil.warn("Discord embed color is invalid");
         }

         StringBuilder sb = new StringBuilder();
         Iterator var4 = GrimAPI.INSTANCE.getConfigManager().getConfig().getStringListElse("violation-content", this.getDefaultContents()).iterator();

         while(var4.hasNext()) {
            String string = (String)var4.next();
            sb.append(string).append("\n");
         }

         this.staticContent = sb.toString();
      } catch (Exception var7) {
         var7.printStackTrace();
      }

   }

   private List<String> getDefaultContents() {
      List<String> list = new ArrayList();
      list.add("**Player**: %player%");
      list.add("**Check**: %check%");
      list.add("**Violations**: %violations%");
      list.add("**Client Version**: %version%");
      list.add("**Brand**: %brand%");
      list.add("**Ping**: %ping%");
      list.add("**TPS**: %tps%");
      return list;
   }

   public void sendAlert(GrimPlayer player, String verbose, String checkName, int violations) {
      if (client != null) {
         String content = this.staticContent;
         content = content.replace("%check%", checkName);
         content = content.replace("%violations%", Integer.toString(violations));
         content = MessageUtil.replacePlaceholders(player, content);
         content = content.replace("_", "\\_");
         WebhookEmbedBuilder embed = (new WebhookEmbedBuilder()).setImageUrl("https://i.stack.imgur.com/Fzh0w.png").setThumbnailUrl("https://crafthead.net/helm/" + String.valueOf(player.user.getProfile().getUUID())).setColor(this.embedColor).setTitle(new WebhookEmbed.EmbedTitle(this.embedTitle, (String)null)).setDescription(content).setTimestamp(Instant.now()).setFooter(new WebhookEmbed.EmbedFooter("", "https://grim.ac/images/grim.png"));
         if (!verbose.isEmpty()) {
            embed.addField(new WebhookEmbed.EmbedField(true, "Verbose", verbose));
         }

         this.sendWebhookEmbed(embed);
      }

   }

   public void sendWebhookEmbed(WebhookEmbedBuilder embed) {
      try {
         client.send(embed.build());
      } catch (Exception var3) {
      }

   }
}
