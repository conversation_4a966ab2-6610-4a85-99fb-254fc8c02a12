package ac.grim.grimac.checks.impl.exploit;

import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.CheckData;
import ac.grim.grimac.checks.type.PacketCheck;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketReceiveEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.packettype.PacketType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientEditBook;

@CheckData(
   name = "ExploitB",
   description = "Too long book title"
)
public class ExploitB extends Check implements PacketCheck {
   public ExploitB(GrimPlayer playerData) {
      super(playerData);
   }

   public void onPacketReceive(PacketReceiveEvent event) {
      if (event.getPacketType() == PacketType.Play.Client.EDIT_BOOK) {
         String title = (new WrapperPlayClientEditBook(event)).getTitle();
         if (title == null) {
            return;
         }

         if (title.length() > 15 && this.flagAndAlert("title=" + title) && this.shouldModifyPackets()) {
            event.setCancelled(true);
            this.player.onPacketCancel();
         }
      }

   }
}
