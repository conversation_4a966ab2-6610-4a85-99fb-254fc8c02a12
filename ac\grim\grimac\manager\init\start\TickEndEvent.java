package ac.grim.grimac.manager.init.start;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.manager.init.Initable;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.PacketEvents;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.manager.server.ServerVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.util.reflection.Reflection;
import ac.grim.grimac.shaded.io.github.retrooper.packetevents.util.SpigotReflectionUtil;
import ac.grim.grimac.shaded.io.github.retrooper.packetevents.util.folia.FoliaScheduler;
import ac.grim.grimac.utils.anticheat.LogUtil;
import ac.grim.grimac.utils.lists.HookedListWrapper;
import ac.grim.grimac.utils.reflection.PaperUtils;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;
import sun.misc.Unsafe;

public class TickEndEvent implements Initable, Listener {
   public void start() {
      if (GrimAPI.INSTANCE.getConfigManager().getConfig().getBooleanElse("Reach.enable-post-packet", false)) {
         if (PacketEvents.getAPI().getServerManager().getVersion().isNewerThan(ServerVersion.V_1_11_2) && !Boolean.getBoolean("paper.explicit-flush")) {
            LogUtil.warn("Reach.enable-post-packet=true but paper.explicit-flush=false, add \"-Dpaper.explicit-flush=true\" to your server's startup flags for fully functional extra reach accuracy.");
         }

         if (FoliaScheduler.isFolia()) {
            PaperUtils.registerTickEndEvent(this, this::tickAllFoliaPlayers);
         } else {
            if (!this.injectWithReflection() && !PaperUtils.registerTickEndEvent(this, this::tickAllPlayers)) {
               LogUtil.error("Failed to inject into the end of tick event!");
            }

         }
      }
   }

   private void onEndOfTick(GrimPlayer player) {
      player.checkManager.getEntityReplication().onEndOfTickEvent();
   }

   private void tickAllPlayers() {
      Iterator var1 = GrimAPI.INSTANCE.getPlayerDataManager().getEntries().iterator();

      while(var1.hasNext()) {
         GrimPlayer player = (GrimPlayer)var1.next();
         if (!player.disableGrim) {
            this.onEndOfTick(player);
         }
      }

   }

   private void tickAllFoliaPlayers() {
      Iterator var1 = GrimAPI.INSTANCE.getPlayerDataManager().getEntries().iterator();

      while(var1.hasNext()) {
         GrimPlayer player = (GrimPlayer)var1.next();
         if (!player.disableGrim) {
            Player p = player.bukkitPlayer;
            if (p != null && Bukkit.isOwnedByCurrentRegion(p)) {
               this.onEndOfTick(player);
            }
         }
      }

   }

   private boolean injectWithReflection() {
      try {
         Object connection = SpigotReflectionUtil.getMinecraftServerConnectionInstance();
         Field connectionsList = Reflection.getField(connection.getClass(), List.class, 1);
         List<Object> endOfTickObject = (List)connectionsList.get(connection);
         List<?> wrapper = Collections.synchronizedList(new HookedListWrapper<Object>(endOfTickObject) {
            public void onIterator() {
               TickEndEvent.this.tickAllPlayers();
            }
         });
         Field unsafeField = Unsafe.class.getDeclaredField("theUnsafe");
         unsafeField.setAccessible(true);
         Unsafe unsafe = (Unsafe)unsafeField.get((Object)null);
         unsafe.putObject(connection, unsafe.objectFieldOffset(connectionsList), wrapper);
         return true;
      } catch (IllegalAccessException | NoSuchFieldException var7) {
         LogUtil.exception("Failed to inject into the end of tick event via reflection", var7);
         return false;
      }
   }
}
