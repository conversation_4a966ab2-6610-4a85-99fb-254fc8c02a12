package ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.attribute;

import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.ClientVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.resources.ResourceLocation;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.util.mappings.VersionedRegistry;
import ac.grim.grimac.shaded.jetbrains.annotations.ApiStatus;
import ac.grim.grimac.shaded.jetbrains.annotations.Nullable;

public final class Attributes {
   private static final VersionedRegistry<Attribute> REGISTRY = new VersionedRegistry("attribute");
   public static final Attribute ARMOR = define("armor", "generic", 0.0D, 0.0D, 30.0D);
   public static final Attribute ARMOR_TOUGHNESS = define("armor_toughness", "generic", 0.0D, 0.0D, 20.0D);
   public static final Attribute ATTACK_DAMAGE = define("attack_damage", "generic", 2.0D, 0.0D, 2048.0D);
   public static final Attribute ATTACK_KNOCKBACK = define("attack_knockback", "generic", 0.0D, 0.0D, 5.0D);
   public static final Attribute ATTACK_SPEED = define("attack_speed", "generic", 4.0D, 0.0D, 1024.0D);
   public static final Attribute FLYING_SPEED = define("flying_speed", "generic", 0.4D, 0.0D, 1024.0D);
   public static final Attribute FOLLOW_RANGE = define("follow_range", "generic", 32.0D, 0.0D, 2048.0D);
   public static final Attribute KNOCKBACK_RESISTANCE = define("knockback_resistance", "generic", 0.0D, 0.0D, 1.0D);
   public static final Attribute LUCK = define("luck", "generic", 0.0D, -1024.0D, 1024.0D);
   public static final Attribute MAX_HEALTH = define("max_health", "generic", 20.0D, 1.0D, 1024.0D);
   public static final Attribute MOVEMENT_SPEED = define("movement_speed", "generic", 0.7D, 0.0D, 1024.0D);
   public static final Attribute SPAWN_REINFORCEMENTS = define("spawn_reinforcements", "zombie", 0.0D, 0.0D, 1.0D);
   public static final Attribute MAX_ABSORPTION = define("max_absorption", "generic", 0.0D, 0.0D, 2048.0D);
   public static final Attribute BLOCK_BREAK_SPEED = define("block_break_speed", "player", 1.0D, 0.0D, 1024.0D);
   public static final Attribute BLOCK_INTERACTION_RANGE = define("block_interaction_range", "player", 4.5D, 0.0D, 64.0D);
   public static final Attribute ENTITY_INTERACTION_RANGE = define("entity_interaction_range", "player", 3.0D, 0.0D, 64.0D);
   public static final Attribute FALL_DAMAGE_MULTIPLIER = define("fall_damage_multiplier", "generic", 1.0D, 0.0D, 100.0D);
   public static final Attribute GRAVITY = define("gravity", "generic", 0.08D, -1.0D, 1.0D);
   public static final Attribute JUMP_STRENGTH = define("jump_strength", "generic", 0.42D, 0.0D, 32.0D);
   public static final Attribute SAFE_FALL_DISTANCE = define("safe_fall_distance", "generic", 3.0D, 0.0D, 1024.0D);
   public static final Attribute SCALE = define("scale", "generic", 1.0D, 0.0625D, 16.0D);
   public static final Attribute STEP_HEIGHT = define("step_height", "generic", 0.6D, 0.0D, 10.0D);
   public static final Attribute BURNING_TIME = define("burning_time", "generic", 0.0D, 1.0D, 1024.0D);
   public static final Attribute EXPLOSION_KNOCKBACK_RESISTANCE = define("explosion_knockback_resistance", "generic", 0.0D, 0.0D, 1.0D);
   public static final Attribute MINING_EFFICIENCY = define("mining_efficiency", "player", 0.0D, 0.0D, 1024.0D);
   public static final Attribute MOVEMENT_EFFICIENCY = define("movement_efficiency", "generic", 0.0D, 0.0D, 1.0D);
   public static final Attribute OXYGEN_BONUS = define("oxygen_bonus", "generic", 0.0D, 0.0D, 1024.0D);
   public static final Attribute SNEAKING_SPEED = define("sneaking_speed", "player", 0.3D, 0.0D, 1.0D);
   public static final Attribute SUBMERGED_MINING_SPEED = define("submerged_mining_speed", "player", 0.2D, 0.0D, 20.0D);
   public static final Attribute SWEEPING_DAMAGE_RATIO = define("sweeping_damage_ratio", "player", 0.0D, 0.0D, 1.0D);
   public static final Attribute WATER_MOVEMENT_EFFICIENCY = define("water_movement_efficiency", "generic", 0.0D, 0.0D, 1.0D);
   public static final Attribute TEMPT_RANGE = define("tempt_range", (String)null, 10.0D, 0.0D, 2048.0D);
   @ApiStatus.Obsolete
   public static final Attribute HORSE_JUMP_STRENGTH = define("horse.jump_strength", (String)null, 0.7D, 0.0D, 2.0D);
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_ARMOR;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_ARMOR_TOUGHNESS;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_ATTACK_DAMAGE;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_ATTACK_KNOCKBACK;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_ATTACK_SPEED;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_FLYING_SPEED;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_FOLLOW_RANGE;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_KNOCKBACK_RESISTANCE;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_LUCK;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_MAX_HEALTH;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_MOVEMENT_SPEED;
   /** @deprecated */
   @Deprecated
   public static final Attribute ZOMBIE_SPAWN_REINFORCEMENTS;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_MAX_ABSORPTION;
   /** @deprecated */
   @Deprecated
   public static final Attribute PLAYER_BLOCK_BREAK_SPEED;
   /** @deprecated */
   @Deprecated
   public static final Attribute PLAYER_BLOCK_INTERACTION_RANGE;
   /** @deprecated */
   @Deprecated
   public static final Attribute PLAYER_ENTITY_INTERACTION_RANGE;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_FALL_DAMAGE_MULTIPLIER;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_GRAVITY;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_JUMP_STRENGTH;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_SAFE_FALL_DISTANCE;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_SCALE;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_STEP_HEIGHT;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_BURNING_TIME;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_EXPLOSION_KNOCKBACK_RESISTANCE;
   /** @deprecated */
   @Deprecated
   public static final Attribute PLAYER_MINING_EFFICIENCY;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_MOVEMENT_EFFICIENCY;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_OXYGEN_BONUS;
   /** @deprecated */
   @Deprecated
   public static final Attribute PLAYER_SNEAKING_SPEED;
   /** @deprecated */
   @Deprecated
   public static final Attribute PLAYER_SUBMERGED_MINING_SPEED;
   /** @deprecated */
   @Deprecated
   public static final Attribute PLAYER_SWEEPING_DAMAGE_RATIO;
   /** @deprecated */
   @Deprecated
   public static final Attribute GENERIC_WATER_MOVEMENT_EFFICIENCY;

   private Attributes() {
   }

   private static Attribute define(String key, @Nullable String legacyPrefix, double def, double min, double max) {
      return (Attribute)REGISTRY.define(key, (data) -> {
         return new StaticAttribute(data, legacyPrefix, def, min, max);
      });
   }

   public static VersionedRegistry<Attribute> getRegistry() {
      return REGISTRY;
   }

   public static Attribute getByName(String name) {
      String normedName = ResourceLocation.normString(name);
      if (normedName.startsWith("minecraft:generic.") || normedName.startsWith("minecraft:player.") || normedName.startsWith("minecraft:zombie.")) {
         normedName = normedName.substring(normedName.indexOf(46) + 1);
      }

      return (Attribute)REGISTRY.getByName(normedName);
   }

   public static Attribute getById(ClientVersion version, int id) {
      return (Attribute)REGISTRY.getById(version, id);
   }

   static {
      GENERIC_ARMOR = ARMOR;
      GENERIC_ARMOR_TOUGHNESS = ARMOR_TOUGHNESS;
      GENERIC_ATTACK_DAMAGE = ATTACK_DAMAGE;
      GENERIC_ATTACK_KNOCKBACK = ATTACK_KNOCKBACK;
      GENERIC_ATTACK_SPEED = ATTACK_SPEED;
      GENERIC_FLYING_SPEED = FLYING_SPEED;
      GENERIC_FOLLOW_RANGE = FOLLOW_RANGE;
      GENERIC_KNOCKBACK_RESISTANCE = KNOCKBACK_RESISTANCE;
      GENERIC_LUCK = LUCK;
      GENERIC_MAX_HEALTH = MAX_HEALTH;
      GENERIC_MOVEMENT_SPEED = MOVEMENT_SPEED;
      ZOMBIE_SPAWN_REINFORCEMENTS = SPAWN_REINFORCEMENTS;
      GENERIC_MAX_ABSORPTION = MAX_ABSORPTION;
      PLAYER_BLOCK_BREAK_SPEED = BLOCK_BREAK_SPEED;
      PLAYER_BLOCK_INTERACTION_RANGE = BLOCK_INTERACTION_RANGE;
      PLAYER_ENTITY_INTERACTION_RANGE = ENTITY_INTERACTION_RANGE;
      GENERIC_FALL_DAMAGE_MULTIPLIER = FALL_DAMAGE_MULTIPLIER;
      GENERIC_GRAVITY = GRAVITY;
      GENERIC_JUMP_STRENGTH = JUMP_STRENGTH;
      GENERIC_SAFE_FALL_DISTANCE = SAFE_FALL_DISTANCE;
      GENERIC_SCALE = SCALE;
      GENERIC_STEP_HEIGHT = STEP_HEIGHT;
      GENERIC_BURNING_TIME = BURNING_TIME;
      GENERIC_EXPLOSION_KNOCKBACK_RESISTANCE = EXPLOSION_KNOCKBACK_RESISTANCE;
      PLAYER_MINING_EFFICIENCY = MINING_EFFICIENCY;
      GENERIC_MOVEMENT_EFFICIENCY = MOVEMENT_EFFICIENCY;
      GENERIC_OXYGEN_BONUS = OXYGEN_BONUS;
      PLAYER_SNEAKING_SPEED = SNEAKING_SPEED;
      PLAYER_SUBMERGED_MINING_SPEED = SUBMERGED_MINING_SPEED;
      PLAYER_SWEEPING_DAMAGE_RATIO = SWEEPING_DAMAGE_RATIO;
      GENERIC_WATER_MOVEMENT_EFFICIENCY = WATER_MOVEMENT_EFFICIENCY;
      REGISTRY.unloadMappings();
   }
}
