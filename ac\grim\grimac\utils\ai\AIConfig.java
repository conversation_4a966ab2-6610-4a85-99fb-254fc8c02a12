package ac.grim.grimac.utils.ai;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.manager.config.AIConfigManager;

/**
 * Configuration class for AI-powered anti-cheat features
 * Now delegates to AIConfigManager for better organization
 */
public class AIConfig {
    private final AIConfigManager configManager;
    
    // OpenAI Configuration
    private final String openAIApiKey;
    private final String openAIModel;
    private final int maxTokens;
    private final double temperature;
    
    // AI System Configuration
    private final boolean aiEnabled;
    private final boolean trainingMode;
    private final int threadPoolSize;
    private final int cacheExpirationMinutes;
    private final int maxCacheSize;
    
    // Detection Thresholds
    private final double movementSuspicionThreshold;
    private final double combatSuspicionThreshold;
    private final double exploitSuspicionThreshold;
    private final double buildingSuspicionThreshold;
    
    // Training Configuration
    private final int minTrainingDataSize;
    private final int maxTrainingDataSize;
    private final int trainingBatchSize;
    private final boolean autoTraining;
    private final int autoTrainingIntervalHours;
    
    // Data Collection
    private final boolean collectMovementData;
    private final boolean collectCombatData;
    private final boolean collectExploitData;
    private final boolean collectBuildingData;
    private final int dataRetentionDays;
    
    public AIConfig() {
        this.configManager = new AIConfigManager();

        // Load configuration through the AI config manager
        this.configManager.load(GrimAPI.INSTANCE.getConfigManager().getConfig());

        // Delegate to config manager
        this.openAIApiKey = configManager.getOpenAIApiKey();
        this.openAIModel = configManager.getOpenAIModel();
        this.maxTokens = configManager.getMaxTokens();
        this.temperature = configManager.getTemperature();

        this.aiEnabled = configManager.isAIEnabled();
        this.trainingMode = configManager.isTrainingMode();
        this.threadPoolSize = configManager.getThreadPoolSize();
        this.cacheExpirationMinutes = configManager.getCacheExpirationMinutes();
        this.maxCacheSize = configManager.getMaxCacheSize();

        this.movementSuspicionThreshold = configManager.getDetectionThreshold("movement");
        this.combatSuspicionThreshold = configManager.getDetectionThreshold("combat");
        this.exploitSuspicionThreshold = configManager.getDetectionThreshold("exploit");
        this.buildingSuspicionThreshold = configManager.getDetectionThreshold("building");

        this.minTrainingDataSize = configManager.getMinTrainingDataSize();
        this.maxTrainingDataSize = configManager.getMaxTrainingDataSize();
        this.trainingBatchSize = configManager.getTrainingBatchSize();
        this.autoTraining = configManager.isAutoTraining();
        this.autoTrainingIntervalHours = configManager.getAutoTrainingIntervalHours();

        this.collectMovementData = configManager.isDataCollectionEnabled("movement");
        this.collectCombatData = configManager.isDataCollectionEnabled("combat");
        this.collectExploitData = configManager.isDataCollectionEnabled("exploit");
        this.collectBuildingData = configManager.isDataCollectionEnabled("building");
        this.dataRetentionDays = configManager.getDataRetentionDays();
    }
    
    // OpenAI Configuration Getters
    public String getOpenAIApiKey() { return openAIApiKey; }
    public String getOpenAIModel() { return openAIModel; }
    public int getMaxTokens() { return maxTokens; }
    public double getTemperature() { return temperature; }
    
    // AI System Configuration Getters
    public boolean isAIEnabled() { return aiEnabled && !openAIApiKey.isEmpty(); }
    public boolean isTrainingMode() { return trainingMode; }
    public int getThreadPoolSize() { return threadPoolSize; }
    public int getCacheExpirationMinutes() { return cacheExpirationMinutes; }
    public int getMaxCacheSize() { return maxCacheSize; }
    
    // Detection Thresholds Getters
    public double getMovementSuspicionThreshold() { return movementSuspicionThreshold; }
    public double getCombatSuspicionThreshold() { return combatSuspicionThreshold; }
    public double getExploitSuspicionThreshold() { return exploitSuspicionThreshold; }
    public double getBuildingSuspicionThreshold() { return buildingSuspicionThreshold; }
    
    // Training Configuration Getters
    public int getMinTrainingDataSize() { return minTrainingDataSize; }
    public int getMaxTrainingDataSize() { return maxTrainingDataSize; }
    public int getTrainingBatchSize() { return trainingBatchSize; }
    public boolean isAutoTraining() { return autoTraining; }
    public int getAutoTrainingIntervalHours() { return autoTrainingIntervalHours; }
    
    // Data Collection Getters
    public boolean isCollectMovementData() { return collectMovementData; }
    public boolean isCollectCombatData() { return collectCombatData; }
    public boolean isCollectExploitData() { return collectExploitData; }
    public boolean isCollectBuildingData() { return collectBuildingData; }
    public int getDataRetentionDays() { return dataRetentionDays; }
    
    /**
     * Validate configuration
     */
    public boolean isValid() {
        if (!aiEnabled) return true; // If AI is disabled, config is valid
        
        if (openAIApiKey.isEmpty()) {
            return false;
        }
        
        if (threadPoolSize < 1 || threadPoolSize > 20) {
            return false;
        }
        
        if (cacheExpirationMinutes < 1 || cacheExpirationMinutes > 1440) {
            return false;
        }
        
        if (maxCacheSize < 10 || maxCacheSize > 100000) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Get the underlying AI config manager
     */
    public AIConfigManager getConfigManager() {
        return configManager;
    }

    /**
     * Get configuration summary for logging
     */
    public String getConfigSummary() {
        return configManager.getConfigSummary();
    }
}
