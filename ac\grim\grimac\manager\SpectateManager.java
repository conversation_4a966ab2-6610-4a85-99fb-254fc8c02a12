package ac.grim.grimac.manager;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.manager.init.Initable;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerPlayerInfo;
import ac.grim.grimac.shaded.kyori.adventure.text.Component;
import ac.grim.grimac.shaded.kyori.adventure.text.format.NamedTextColor;
import ac.grim.grimac.shaded.kyori.adventure.text.format.TextColor;
import ac.grim.grimac.utils.anticheat.MessageUtil;
import ac.grim.grimac.utils.reflection.PaperUtils;
import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.entity.Player;

public class SpectateManager implements Initable {
   private final Map<UUID, SpectateManager.PreviousState> spectatingPlayers = new ConcurrentHashMap();
   private final Set<UUID> hiddenPlayers = ConcurrentHashMap.newKeySet();
   private final Set<String> allowedWorlds = ConcurrentHashMap.newKeySet();
   private boolean checkWorld = false;

   public void start() {
      this.allowedWorlds.clear();
      this.allowedWorlds.addAll(GrimAPI.INSTANCE.getConfigManager().getConfig().getStringListElse("spectators.allowed-worlds", new ArrayList()));
      this.checkWorld = !this.allowedWorlds.isEmpty() && !((String)(new ArrayList(this.allowedWorlds)).get(0)).isEmpty();
   }

   public boolean isSpectating(UUID uuid) {
      return this.spectatingPlayers.containsKey(uuid);
   }

   public boolean shouldHidePlayer(GrimPlayer receiver, WrapperPlayServerPlayerInfo.PlayerData playerData) {
      return playerData.getUser() != null && playerData.getUser().getUUID() != null && this.shouldHidePlayer(receiver, playerData.getUser().getUUID());
   }

   public boolean shouldHidePlayer(GrimPlayer receiver, UUID uuid) {
      return !Objects.equals(uuid, receiver.uuid) && (this.spectatingPlayers.containsKey(uuid) || this.hiddenPlayers.contains(uuid)) && (receiver.uuid == null || !this.spectatingPlayers.containsKey(receiver.uuid) && !this.hiddenPlayers.contains(receiver.uuid)) && (!this.checkWorld || receiver.bukkitPlayer != null && this.allowedWorlds.contains(receiver.bukkitPlayer.getWorld().getName()));
   }

   public boolean enable(Player player) {
      if (this.spectatingPlayers.containsKey(player.getUniqueId())) {
         return false;
      } else {
         this.spectatingPlayers.put(player.getUniqueId(), new SpectateManager.PreviousState(player.getGameMode(), player.getLocation()));
         return true;
      }
   }

   public void onLogin(Player player) {
      this.hiddenPlayers.add(player.getUniqueId());
   }

   public void onQuit(Player player) {
      this.hiddenPlayers.remove(player.getUniqueId());
      this.handlePlayerStopSpectating(player.getUniqueId());
   }

   public void disable(Player player, boolean teleportBack) {
      SpectateManager.PreviousState previousState = (SpectateManager.PreviousState)this.spectatingPlayers.get(player.getUniqueId());
      if (previousState != null) {
         if (teleportBack && previousState.location.isWorldLoaded()) {
            PaperUtils.teleportAsync(player, previousState.location).thenAccept((bool) -> {
               if (bool) {
                  this.onDisable(previousState, player);
               } else {
                  MessageUtil.sendMessage(player, Component.text("Teleport failed, please try again.", (TextColor)NamedTextColor.RED));
               }

            });
         } else {
            this.onDisable(previousState, player);
         }
      }

   }

   private void onDisable(SpectateManager.PreviousState previousState, Player player) {
      player.setGameMode(previousState.gameMode);
      this.handlePlayerStopSpectating(player.getUniqueId());
   }

   public void handlePlayerStopSpectating(UUID uuid) {
      this.spectatingPlayers.remove(uuid);
   }

   private static record PreviousState(GameMode gameMode, Location location) {
      private PreviousState(GameMode gameMode, Location location) {
         this.gameMode = gameMode;
         this.location = location;
      }

      public GameMode gameMode() {
         return this.gameMode;
      }

      public Location location() {
         return this.location;
      }
   }
}
