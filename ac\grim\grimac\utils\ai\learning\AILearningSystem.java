package ac.grim.grimac.utils.ai.learning;

import ac.grim.grimac.utils.ai.models.AIDetectionResult;
import ac.grim.grimac.utils.ai.models.PlayerBehaviorData;
import ac.grim.grimac.utils.ai.training.AITrainingProcessor;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.Logger;

/**
 * AI Learning System that continuously improves detection accuracy
 * through feedback loops and pattern recognition
 */
public class AILearningSystem {
    private static final Logger LOGGER = Logger.getLogger("GrimAC-AI-Learning");
    
    // Learning metrics
    private final Map<String, LearningMetrics> behaviorMetrics;
    private final Map<String, AdaptiveThreshold> adaptiveThresholds;
    private final FeedbackProcessor feedbackProcessor;
    private final PatternLearner patternLearner;
    
    // Performance tracking
    private final AtomicInteger totalPredictions;
    private final AtomicInteger correctPredictions;
    private final AtomicInteger falsePositives;
    private final AtomicInteger falseNegatives;
    
    public AILearningSystem() {
        this.behaviorMetrics = new ConcurrentHashMap<>();
        this.adaptiveThresholds = new ConcurrentHashMap<>();
        this.feedbackProcessor = new FeedbackProcessor();
        this.patternLearner = new PatternLearner();
        
        this.totalPredictions = new AtomicInteger(0);
        this.correctPredictions = new AtomicInteger(0);
        this.falsePositives = new AtomicInteger(0);
        this.falseNegatives = new AtomicInteger(0);
        
        initializeAdaptiveThresholds();
        
        LOGGER.info("AI Learning System initialized");
    }
    
    /**
     * Process AI prediction and learn from the result
     */
    public void processPrediction(String behaviorType, AIDetectionResult prediction, 
                                PlayerBehaviorData playerData, boolean actualResult) {
        totalPredictions.incrementAndGet();
        
        // Update metrics
        updateLearningMetrics(behaviorType, prediction, actualResult);
        
        // Process feedback
        FeedbackData feedback = new FeedbackData(
            behaviorType, prediction, playerData, actualResult, System.currentTimeMillis()
        );
        feedbackProcessor.processFeedback(feedback);
        
        // Learn patterns
        patternLearner.learnFromPrediction(behaviorType, prediction, playerData, actualResult);
        
        // Adapt thresholds
        adaptThresholds(behaviorType, prediction, actualResult);
        
        // Log learning progress
        if (totalPredictions.get() % 100 == 0) {
            logLearningProgress();
        }
    }
    
    /**
     * Get adaptive threshold for a behavior type
     */
    public double getAdaptiveThreshold(String behaviorType) {
        AdaptiveThreshold threshold = adaptiveThresholds.get(behaviorType);
        return threshold != null ? threshold.getCurrentThreshold() : 0.7; // Default
    }
    
    /**
     * Get confidence adjustment based on learned patterns
     */
    public double getConfidenceAdjustment(String behaviorType, PlayerBehaviorData playerData) {
        return patternLearner.getConfidenceAdjustment(behaviorType, playerData);
    }
    
    /**
     * Get learning insights for a specific behavior type
     */
    public LearningInsights getLearningInsights(String behaviorType) {
        LearningMetrics metrics = behaviorMetrics.get(behaviorType);
        AdaptiveThreshold threshold = adaptiveThresholds.get(behaviorType);
        List<String> learnedPatterns = patternLearner.getLearnedPatterns(behaviorType);
        
        return new LearningInsights(metrics, threshold, learnedPatterns);
    }
    
    private void initializeAdaptiveThresholds() {
        // Initialize thresholds for different behavior types
        adaptiveThresholds.put("movement", new AdaptiveThreshold(0.7, 0.5, 0.9));
        adaptiveThresholds.put("combat", new AdaptiveThreshold(0.8, 0.6, 0.95));
        adaptiveThresholds.put("exploit", new AdaptiveThreshold(0.9, 0.7, 0.98));
        adaptiveThresholds.put("building", new AdaptiveThreshold(0.6, 0.4, 0.8));
    }
    
    private void updateLearningMetrics(String behaviorType, AIDetectionResult prediction, boolean actualResult) {
        LearningMetrics metrics = behaviorMetrics.computeIfAbsent(behaviorType, k -> new LearningMetrics());
        
        boolean predictedSuspicious = prediction.isSuspicious();
        
        if (predictedSuspicious == actualResult) {
            correctPredictions.incrementAndGet();
            metrics.correctPredictions++;
        } else if (predictedSuspicious && !actualResult) {
            falsePositives.incrementAndGet();
            metrics.falsePositives++;
        } else if (!predictedSuspicious && actualResult) {
            falseNegatives.incrementAndGet();
            metrics.falseNegatives++;
        }
        
        metrics.totalPredictions++;
        metrics.updateAccuracy();
        
        // Track confidence calibration
        metrics.addConfidenceCalibration(prediction.getConfidence(), actualResult);
    }
    
    private void adaptThresholds(String behaviorType, AIDetectionResult prediction, boolean actualResult) {
        AdaptiveThreshold threshold = adaptiveThresholds.get(behaviorType);
        if (threshold == null) return;
        
        boolean predictedSuspicious = prediction.isSuspicious();
        double confidence = prediction.getConfidence();
        
        // Adjust threshold based on prediction accuracy
        if (predictedSuspicious && !actualResult) {
            // False positive - increase threshold
            threshold.adjustThreshold(0.01);
        } else if (!predictedSuspicious && actualResult && confidence > 0.5) {
            // False negative with reasonable confidence - decrease threshold
            threshold.adjustThreshold(-0.005);
        }
    }
    
    private void logLearningProgress() {
        double overallAccuracy = totalPredictions.get() > 0 ? 
            (double) correctPredictions.get() / totalPredictions.get() : 0.0;
        
        LOGGER.info(String.format("AI Learning Progress - Total: %d, Accuracy: %.3f, FP: %d, FN: %d",
            totalPredictions.get(), overallAccuracy, falsePositives.get(), falseNegatives.get()));
        
        // Log per-behavior metrics
        for (Map.Entry<String, LearningMetrics> entry : behaviorMetrics.entrySet()) {
            LearningMetrics metrics = entry.getValue();
            LOGGER.info(String.format("  %s - Accuracy: %.3f, Predictions: %d", 
                entry.getKey(), metrics.accuracy, metrics.totalPredictions));
        }
    }
    
    // Inner classes
    public static class LearningMetrics {
        public int totalPredictions = 0;
        public int correctPredictions = 0;
        public int falsePositives = 0;
        public int falseNegatives = 0;
        public double accuracy = 0.0;
        
        private final List<ConfidenceCalibration> calibrationData = new ArrayList<>();
        
        public void updateAccuracy() {
            accuracy = totalPredictions > 0 ? (double) correctPredictions / totalPredictions : 0.0;
        }
        
        public void addConfidenceCalibration(double confidence, boolean actualResult) {
            calibrationData.add(new ConfidenceCalibration(confidence, actualResult));
            
            // Keep only recent calibration data
            if (calibrationData.size() > 1000) {
                calibrationData.remove(0);
            }
        }
        
        public double getCalibrationError() {
            if (calibrationData.isEmpty()) return 0.0;
            
            // Calculate Expected Calibration Error (ECE)
            Map<Integer, List<ConfidenceCalibration>> bins = new HashMap<>();
            
            for (ConfidenceCalibration cal : calibrationData) {
                int bin = (int) (cal.confidence * 10); // 10 bins
                bins.computeIfAbsent(bin, k -> new ArrayList<>()).add(cal);
            }
            
            double ece = 0.0;
            for (List<ConfidenceCalibration> bin : bins.values()) {
                if (bin.isEmpty()) continue;
                
                double avgConfidence = bin.stream().mapToDouble(c -> c.confidence).average().orElse(0.0);
                double accuracy = bin.stream().mapToDouble(c -> c.actualResult ? 1.0 : 0.0).average().orElse(0.0);
                double weight = (double) bin.size() / calibrationData.size();
                
                ece += weight * Math.abs(avgConfidence - accuracy);
            }
            
            return ece;
        }
    }
    
    public static class AdaptiveThreshold {
        private double currentThreshold;
        private final double minThreshold;
        private final double maxThreshold;
        private final double learningRate = 0.01;
        
        public AdaptiveThreshold(double initial, double min, double max) {
            this.currentThreshold = initial;
            this.minThreshold = min;
            this.maxThreshold = max;
        }
        
        public void adjustThreshold(double adjustment) {
            currentThreshold += adjustment * learningRate;
            currentThreshold = Math.max(minThreshold, Math.min(maxThreshold, currentThreshold));
        }
        
        public double getCurrentThreshold() {
            return currentThreshold;
        }
    }
    
    public static class ConfidenceCalibration {
        public final double confidence;
        public final boolean actualResult;
        
        public ConfidenceCalibration(double confidence, boolean actualResult) {
            this.confidence = confidence;
            this.actualResult = actualResult;
        }
    }
    
    public static class FeedbackData {
        public final String behaviorType;
        public final AIDetectionResult prediction;
        public final PlayerBehaviorData playerData;
        public final boolean actualResult;
        public final long timestamp;
        
        public FeedbackData(String behaviorType, AIDetectionResult prediction, 
                          PlayerBehaviorData playerData, boolean actualResult, long timestamp) {
            this.behaviorType = behaviorType;
            this.prediction = prediction;
            this.playerData = playerData;
            this.actualResult = actualResult;
            this.timestamp = timestamp;
        }
    }
    
    public static class LearningInsights {
        public final LearningMetrics metrics;
        public final AdaptiveThreshold threshold;
        public final List<String> learnedPatterns;
        
        public LearningInsights(LearningMetrics metrics, AdaptiveThreshold threshold, List<String> learnedPatterns) {
            this.metrics = metrics;
            this.threshold = threshold;
            this.learnedPatterns = learnedPatterns;
        }
    }
    
    // Placeholder classes for feedback processing and pattern learning
    private static class FeedbackProcessor {
        public void processFeedback(FeedbackData feedback) {
            // Process feedback to improve future predictions
        }
    }
    
    private static class PatternLearner {
        private final Map<String, List<String>> learnedPatterns = new ConcurrentHashMap<>();
        
        public void learnFromPrediction(String behaviorType, AIDetectionResult prediction, 
                                      PlayerBehaviorData playerData, boolean actualResult) {
            // Learn patterns from prediction results
        }
        
        public double getConfidenceAdjustment(String behaviorType, PlayerBehaviorData playerData) {
            // Return confidence adjustment based on learned patterns
            return 0.0;
        }
        
        public List<String> getLearnedPatterns(String behaviorType) {
            return learnedPatterns.getOrDefault(behaviorType, new ArrayList<>());
        }
    }
}
