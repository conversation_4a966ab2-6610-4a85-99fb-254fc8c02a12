package ac.grim.grimac.shaded.acf;

import ac.grim.grimac.shaded.acf.bukkit.contexts.OnlinePlayer;
import ac.grim.grimac.shaded.jetbrains.annotations.Contract;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.OfflinePlayer;
import org.bukkit.World;
import org.bukkit.command.BlockCommandSender;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.inventory.PlayerInventory;

public class BukkitCommandContexts extends CommandContexts<BukkitCommandExecutionContext> {
   public BukkitCommandContexts(BukkitCommandManager manager) {
      super(manager);
      this.registerContext(OnlinePlayer.class, (c) -> {
         return this.getOnlinePlayer((BukkitCommandIssuer)c.getIssuer(), c.popFirstArg(), false);
      });
      this.registerContext(ac.grim.grimac.shaded.acf.contexts.OnlinePlayer.class, (c) -> {
         OnlinePlayer onlinePlayer = this.getOnlinePlayer((BukkitCommandIssuer)c.getIssuer(), c.popFirstArg(), false);
         return new ac.grim.grimac.shaded.acf.contexts.OnlinePlayer(onlinePlayer.getPlayer());
      });
      this.registerContext(OnlinePlayer[].class, (c) -> {
         BukkitCommandIssuer issuer = (BukkitCommandIssuer)c.getIssuer();
         String search = c.popFirstArg();
         boolean allowMissing = c.hasFlag("allowmissing");
         Set<OnlinePlayer> players = new HashSet();
         Pattern split = ACFPatterns.COMMA;
         String splitter = c.getFlagValue("splitter", (String)null);
         if (splitter != null) {
            split = Pattern.compile(Pattern.quote(splitter));
         }

         String[] var8 = split.split(search);
         int var9 = var8.length;

         for(int var10 = 0; var10 < var9; ++var10) {
            String lookup = var8[var10];
            OnlinePlayer player = this.getOnlinePlayer(issuer, lookup, allowMissing);
            if (player != null) {
               players.add(player);
            }
         }

         if (players.isEmpty() && !c.hasFlag("allowempty")) {
            issuer.sendError(MinecraftMessageKeys.NO_PLAYER_FOUND_SERVER, new String[]{"{search}", search});
            throw new InvalidCommandArgument(false);
         } else {
            return (OnlinePlayer[])players.toArray(new OnlinePlayer[players.size()]);
         }
      });
      this.registerIssuerAwareContext(World.class, (c) -> {
         String firstArg = c.getFirstArg();
         World world = firstArg != null ? Bukkit.getWorld(firstArg) : null;
         if (world != null) {
            c.popFirstArg();
         }

         if (world == null && c.getSender() instanceof Player) {
            world = ((Entity)c.getSender()).getWorld();
         }

         if (world == null) {
            throw new InvalidCommandArgument(MinecraftMessageKeys.INVALID_WORLD, new String[0]);
         } else {
            return world;
         }
      });
      this.registerIssuerAwareContext(CommandSender.class, BukkitCommandExecutionContext::getSender);
      this.registerIssuerAwareContext(Player.class, (c) -> {
         boolean isOptional = c.isOptional();
         CommandSender sender = c.getSender();
         boolean isPlayerSender = sender instanceof Player;
         if (!c.hasFlag("other")) {
            Player player = isPlayerSender ? (Player)sender : null;
            if (player == null && !isOptional) {
               throw new InvalidCommandArgument(MessageKeys.NOT_ALLOWED_ON_CONSOLE, false, new String[0]);
            } else {
               PlayerInventory inventory = player != null ? player.getInventory() : null;
               if (inventory != null && c.hasFlag("itemheld") && !ACFBukkitUtil.isValidItem(inventory.getItem(inventory.getHeldItemSlot()))) {
                  throw new InvalidCommandArgument(MinecraftMessageKeys.YOU_MUST_BE_HOLDING_ITEM, false, new String[0]);
               } else {
                  return player;
               }
            }
         } else {
            String arg = c.popFirstArg();
            if (arg == null && isOptional) {
               if (c.hasFlag("defaultself")) {
                  if (isPlayerSender) {
                     return (Player)sender;
                  } else {
                     throw new InvalidCommandArgument(MessageKeys.NOT_ALLOWED_ON_CONSOLE, false, new String[0]);
                  }
               } else {
                  return null;
               }
            } else if (arg == null) {
               throw new InvalidCommandArgument();
            } else {
               OnlinePlayer onlinePlayer = this.getOnlinePlayer((BukkitCommandIssuer)c.getIssuer(), arg, false);
               return onlinePlayer.getPlayer();
            }
         }
      });
      this.registerContext(OfflinePlayer.class, (c) -> {
         String name = c.popFirstArg();
         OfflinePlayer offlinePlayer;
         if (c.hasFlag("uuid")) {
            UUID uuid;
            try {
               uuid = UUID.fromString(name);
            } catch (IllegalArgumentException var6) {
               throw new InvalidCommandArgument(MinecraftMessageKeys.NO_PLAYER_FOUND_OFFLINE, new String[]{"{search}", name});
            }

            offlinePlayer = Bukkit.getOfflinePlayer(uuid);
         } else {
            offlinePlayer = Bukkit.getOfflinePlayer(name);
         }

         if (offlinePlayer != null && (offlinePlayer.hasPlayedBefore() || offlinePlayer.isOnline())) {
            return offlinePlayer;
         } else if (!c.hasFlag("uuid") && !manager.isValidName(name)) {
            throw new InvalidCommandArgument(MinecraftMessageKeys.IS_NOT_A_VALID_NAME, new String[]{"{name}", name});
         } else {
            throw new InvalidCommandArgument(MinecraftMessageKeys.NO_PLAYER_FOUND_OFFLINE, new String[]{"{search}", name});
         }
      });
      this.registerContext(ChatColor.class, (c) -> {
         String first = c.popFirstArg();
         Stream<ChatColor> colors = Stream.of(ChatColor.values());
         if (c.hasFlag("colorsonly")) {
            colors = colors.filter((color) -> {
               return color.ordinal() <= 15;
            });
         }

         String filter = c.getFlagValue("filter", (String)null);
         if (filter != null) {
            filter = ACFUtil.simplifyString(filter);
            colors = colors.filter((color) -> {
               return filter.equals(ACFUtil.simplifyString(color.name()));
            });
         }

         ChatColor match = (ChatColor)ACFUtil.simpleMatch(ChatColor.class, first);
         if (match == null) {
            String valid = (String)colors.map((color) -> {
               return "<c2>" + ACFUtil.simplifyString(color.name()) + "</c2>";
            }).collect(Collectors.joining("<c1>,</c1> "));
            throw new InvalidCommandArgument(MessageKeys.PLEASE_SPECIFY_ONE_OF, new String[]{"{valid}", valid});
         } else {
            return match;
         }
      });
      this.registerContext(Location.class, (c) -> {
         String input = c.popFirstArg();
         CommandSender sender = c.getSender();
         String[] split = ACFPatterns.COLON.split(input, 2);
         if (split.length == 0) {
            throw new InvalidCommandArgument(true);
         } else if (split.length < 2 && !(sender instanceof Player) && !(sender instanceof BlockCommandSender)) {
            throw new InvalidCommandArgument(MinecraftMessageKeys.LOCATION_PLEASE_SPECIFY_WORLD, new String[0]);
         } else {
            Location sourceLoc = null;
            String world;
            String rest;
            if (split.length == 2) {
               world = split[0];
               rest = split[1];
            } else if (sender instanceof Player) {
               sourceLoc = ((Player)sender).getLocation();
               world = sourceLoc.getWorld().getName();
               rest = split[0];
            } else {
               if (!(sender instanceof BlockCommandSender)) {
                  throw new InvalidCommandArgument(true);
               }

               sourceLoc = ((BlockCommandSender)sender).getBlock().getLocation();
               world = sourceLoc.getWorld().getName();
               rest = split[0];
            }

            boolean rel = rest.startsWith("~");
            split = ACFPatterns.COMMA.split(rel ? rest.substring(1) : rest);
            if (split.length < 3) {
               throw new InvalidCommandArgument(MinecraftMessageKeys.LOCATION_PLEASE_SPECIFY_XYZ, new String[0]);
            } else {
               Double x = ACFUtil.parseDouble(split[0], rel ? 0.0D : null);
               Double y = ACFUtil.parseDouble(split[1], rel ? 0.0D : null);
               Double z = ACFUtil.parseDouble(split[2], rel ? 0.0D : null);
               if (sourceLoc != null && rel) {
                  x = x + sourceLoc.getX();
                  y = y + sourceLoc.getY();
                  z = z + sourceLoc.getZ();
               } else if (rel) {
                  throw new InvalidCommandArgument(MinecraftMessageKeys.LOCATION_CONSOLE_NOT_RELATIVE, new String[0]);
               }

               if (x != null && y != null && z != null) {
                  World worldObj = Bukkit.getWorld(world);
                  if (worldObj == null) {
                     throw new InvalidCommandArgument(MinecraftMessageKeys.INVALID_WORLD, new String[0]);
                  } else if (split.length >= 5) {
                     Float yaw = ACFUtil.parseFloat(split[3]);
                     Float pitch = ACFUtil.parseFloat(split[4]);
                     if (pitch != null && yaw != null) {
                        return new Location(worldObj, x, y, z, yaw, pitch);
                     } else {
                        throw new InvalidCommandArgument(MinecraftMessageKeys.LOCATION_PLEASE_SPECIFY_XYZ, new String[0]);
                     }
                  } else {
                     return new Location(worldObj, x, y, z);
                  }
               } else {
                  throw new InvalidCommandArgument(MinecraftMessageKeys.LOCATION_PLEASE_SPECIFY_XYZ, new String[0]);
               }
            }
         }
      });
      if (manager.mcMinorVersion >= 12) {
         BukkitCommandContexts_1_12.register(this);
      }

   }

   @Contract("_,_,false -> !null")
   OnlinePlayer getOnlinePlayer(BukkitCommandIssuer issuer, String lookup, boolean allowMissing) throws InvalidCommandArgument {
      Player player = ACFBukkitUtil.findPlayerSmart((CommandIssuer)issuer, lookup);
      if (player == null) {
         if (allowMissing) {
            return null;
         } else {
            throw new InvalidCommandArgument(false);
         }
      } else {
         return new OnlinePlayer(player);
      }
   }
}
