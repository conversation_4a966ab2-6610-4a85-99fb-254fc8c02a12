package ac.grim.grimac.shaded.acf;

import ac.grim.grimac.shaded.acf.bukkit.contexts.OnlinePlayer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Iterator;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang.Validate;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.DyeColor;
import org.bukkit.World;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.generator.WorldInfo;
import org.bukkit.util.StringUtil;

public class BukkitCommandCompletions extends CommandCompletions<BukkitCommandCompletionContext> {
   public BukkitCommandCompletions(BukkitCommandManager manager) {
      super(manager);
      this.registerAsyncCompletion("mobs", (c) -> {
         Stream<String> normal = Stream.of(EntityType.values()).map((entityType) -> {
            return ACFUtil.simplifyString(entityType.getName());
         });
         return (Collection)normal.collect(Collectors.toList());
      });
      this.registerAsyncCompletion("chatcolors", (c) -> {
         Stream<ChatColor> colors = Stream.of(ChatColor.values());
         if (c.hasConfig("colorsonly")) {
            colors = colors.filter((color) -> {
               return color.ordinal() <= 15;
            });
         }

         String filter = c.getConfig("filter");
         if (filter != null) {
            Set<String> filters = (Set)Arrays.stream(ACFPatterns.COLON.split(filter)).map(ACFUtil::simplifyString).collect(Collectors.toSet());
            colors = colors.filter((color) -> {
               return filters.contains(ACFUtil.simplifyString(color.name()));
            });
         }

         return (Collection)colors.map((color) -> {
            return ACFUtil.simplifyString(color.name());
         }).collect(Collectors.toList());
      });
      this.registerAsyncCompletion("dyecolors", (c) -> {
         return ACFUtil.enumNames((Enum[])DyeColor.values());
      });
      this.registerCompletion("worlds", (c) -> {
         return (Collection)Bukkit.getWorlds().stream().map(WorldInfo::getName).collect(Collectors.toList());
      });
      this.registerCompletion("players", (c) -> {
         CommandSender sender = c.getSender();
         Validate.notNull(sender, "Sender cannot be null");
         Player senderPlayer = sender instanceof Player ? (Player)sender : null;
         ArrayList<String> matchedPlayers = new ArrayList();
         Iterator var4 = Bukkit.getOnlinePlayers().iterator();

         while(true) {
            Player player;
            String name;
            do {
               if (!var4.hasNext()) {
                  matchedPlayers.sort(String.CASE_INSENSITIVE_ORDER);
                  return matchedPlayers;
               }

               player = (Player)var4.next();
               name = player.getName();
            } while(senderPlayer != null && !senderPlayer.canSee(player));

            if (StringUtil.startsWithIgnoreCase(name, c.getInput())) {
               matchedPlayers.add(name);
            }
         }
      });
      this.setDefaultCompletion("players", new Class[]{OnlinePlayer.class, ac.grim.grimac.shaded.acf.contexts.OnlinePlayer.class, Player.class});
      this.setDefaultCompletion("worlds", new Class[]{World.class});
   }
}
