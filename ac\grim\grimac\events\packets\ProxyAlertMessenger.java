package ac.grim.grimac.events.packets;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.PacketEvents;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketListenerAbstract;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketReceiveEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.manager.server.ServerVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.packettype.PacketType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientPluginMessage;
import ac.grim.grimac.utils.anticheat.LogUtil;
import ac.grim.grimac.utils.anticheat.MessageUtil;
import com.google.common.collect.Iterables;
import com.google.common.io.ByteArrayDataInput;
import com.google.common.io.ByteArrayDataOutput;
import com.google.common.io.ByteStreams;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.Iterator;
import org.bukkit.Bukkit;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

public class ProxyAlertMessenger extends PacketListenerAbstract {
   private static boolean usingProxy;

   public ProxyAlertMessenger() {
      usingProxy = getBooleanFromFile("spigot.yml", "settings.bungeecord") || getBooleanFromFile("paper.yml", "settings.velocity-support.enabled") || PacketEvents.getAPI().getServerManager().getVersion().isNewerThanOrEquals(ServerVersion.V_1_19) && getBooleanFromFile("config/paper-global.yml", "proxies.velocity.enabled");
      if (usingProxy) {
         LogUtil.info("Registering an outgoing plugin channel...");
         GrimAPI.INSTANCE.getPlugin().getServer().getMessenger().registerOutgoingPluginChannel(GrimAPI.INSTANCE.getPlugin(), "BungeeCord");
      }

   }

   public void onPacketReceive(PacketReceiveEvent event) {
      if (event.getPacketType() == PacketType.Play.Client.PLUGIN_MESSAGE && canReceiveAlerts()) {
         WrapperPlayClientPluginMessage wrapper = new WrapperPlayClientPluginMessage(event);
         if (wrapper.getChannelName().equals("BungeeCord") || wrapper.getChannelName().equals("bungeecord:main")) {
            ByteArrayDataInput in = ByteStreams.newDataInput(wrapper.getData());
            if (in.readUTF().equals("GRIMAC")) {
               byte[] messageBytes = new byte[in.readShort()];
               in.readFully(messageBytes);

               String alert;
               try {
                  alert = (new DataInputStream(new ByteArrayInputStream(messageBytes))).readUTF();
               } catch (IOException var8) {
                  LogUtil.error("Something went wrong whilst reading an alert forwarded from another server!");
                  var8.printStackTrace();
                  return;
               }

               Iterator var6 = GrimAPI.INSTANCE.getAlertManager().getEnabledAlerts().iterator();

               while(var6.hasNext()) {
                  Player bukkitPlayer = (Player)var6.next();
                  MessageUtil.sendMessage(bukkitPlayer, MessageUtil.miniMessage(alert));
               }

            }
         }
      }
   }

   public static void sendPluginMessage(String message) {
      if (canSendAlerts()) {
         ByteArrayOutputStream messageBytes = new ByteArrayOutputStream();
         ByteArrayDataOutput out = ByteStreams.newDataOutput();
         out.writeUTF("Forward");
         out.writeUTF("ONLINE");
         out.writeUTF("GRIMAC");

         try {
            (new DataOutputStream(messageBytes)).writeUTF(message);
         } catch (IOException var4) {
            LogUtil.error("Something went wrong whilst forwarding an alert to other servers!");
            var4.printStackTrace();
            return;
         }

         out.writeShort(messageBytes.toByteArray().length);
         out.write(messageBytes.toByteArray());
         ((Player)Iterables.getFirst(Bukkit.getOnlinePlayers(), (Object)null)).sendPluginMessage(GrimAPI.INSTANCE.getPlugin(), "BungeeCord", out.toByteArray());
      }
   }

   public static boolean canSendAlerts() {
      return usingProxy && GrimAPI.INSTANCE.getConfigManager().getConfig().getBooleanElse("alerts.proxy.send", false) && !Bukkit.getOnlinePlayers().isEmpty();
   }

   public static boolean canReceiveAlerts() {
      return usingProxy && GrimAPI.INSTANCE.getConfigManager().getConfig().getBooleanElse("alerts.proxy.receive", false) && !GrimAPI.INSTANCE.getAlertManager().getEnabledAlerts().isEmpty();
   }

   private static boolean getBooleanFromFile(String pathToFile, String pathToValue) {
      File file = new File(pathToFile);
      return !file.exists() ? false : YamlConfiguration.loadConfiguration(file).getBoolean(pathToValue);
   }
}
