package ac.grim.grimac.checks;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.api.AbstractCheck;
import ac.grim.grimac.api.config.ConfigManager;
import ac.grim.grimac.api.events.FlagEvent;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.packettype.PacketType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.packettype.PacketTypeCommon;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.ClientVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientPlayerFlying;
import ac.grim.grimac.shaded.io.github.retrooper.packetevents.util.folia.FoliaScheduler;
import org.bukkit.Bukkit;

public class Check extends GrimProcessor implements AbstractCheck {
   protected final GrimPlayer player;
   public double violations;
   private double decay;
   private double setbackVL;
   private String checkName;
   private String configName;
   private String alternativeName;
   private String displayName;
   private String description;
   private boolean experimental;
   private boolean isEnabled;
   private boolean exemptPermission;
   private boolean noSetbackPermission;
   private boolean noModifyPacketPermission;
   private long lastViolationTime;

   public Check(GrimPlayer player) {
      this.player = player;
      CheckData checkData = (CheckData)this.getClass().getAnnotation(CheckData.class);
      if (checkData != null) {
         this.checkName = checkData.name();
         this.configName = checkData.configName();
         if (this.configName.equals("DEFAULT")) {
            this.configName = this.checkName;
         }

         this.decay = checkData.decay();
         this.setbackVL = checkData.setback();
         this.alternativeName = checkData.alternativeName();
         this.experimental = checkData.experimental();
         this.description = checkData.description();
         this.displayName = this.checkName;
      }

      this.reload();
   }

   public boolean shouldModifyPackets() {
      return this.isEnabled && !this.player.disableGrim && !this.player.noModifyPacketPermission && !this.exemptPermission;
   }

   public void updatePermissions() {
      if (this.player.bukkitPlayer != null && this.checkName != null) {
         FoliaScheduler.getEntityScheduler().run(this.player.bukkitPlayer, GrimAPI.INSTANCE.getPlugin(), (t) -> {
            String id = this.checkName.toLowerCase();
            this.exemptPermission = this.player.bukkitPlayer.hasPermission("grim.exempt." + id);
            this.noSetbackPermission = this.player.bukkitPlayer.hasPermission("grim.nosetback." + id);
            this.noModifyPacketPermission = this.player.bukkitPlayer.hasPermission("grim.nomodifypacket." + id);
         }, () -> {
         });
      }
   }

   public final boolean flagAndAlert(String verbose) {
      if (this.flag(verbose)) {
         this.alert(verbose);
         return true;
      } else {
         return false;
      }
   }

   public final boolean flagAndAlert() {
      return this.flagAndAlert("");
   }

   public final boolean flag() {
      return this.flag("");
   }

   public final boolean flag(String verbose) {
      if (!this.player.disableGrim && (!this.experimental || this.player.isExperimentalChecks()) && !this.exemptPermission) {
         FlagEvent event = new FlagEvent(this.player, this, verbose);
         Bukkit.getPluginManager().callEvent(event);
         if (event.isCancelled()) {
            return false;
         } else {
            this.player.punishmentManager.handleViolation(this);
            this.lastViolationTime = System.currentTimeMillis();
            ++this.violations;
            return true;
         }
      } else {
         return false;
      }
   }

   public final boolean flagWithSetback() {
      if (this.flag()) {
         this.setbackIfAboveSetbackVL();
         return true;
      } else {
         return false;
      }
   }

   public final boolean flagAndAlertWithSetback() {
      return this.flagAndAlertWithSetback("");
   }

   public final boolean flagAndAlertWithSetback(String verbose) {
      if (this.flagAndAlert(verbose)) {
         this.setbackIfAboveSetbackVL();
         return true;
      } else {
         return false;
      }
   }

   public final void reward() {
      this.violations = Math.max(0.0D, this.violations - this.decay);
   }

   public void reload(ConfigManager configuration) {
      this.decay = configuration.getDoubleElse(this.configName + ".decay", this.decay);
      this.setbackVL = configuration.getDoubleElse(this.configName + ".setbackvl", this.setbackVL);
      this.displayName = configuration.getStringElse(this.configName + ".displayname", this.checkName);
      this.description = configuration.getStringElse(this.configName + ".description", this.description);
      if (this.setbackVL == -1.0D) {
         this.setbackVL = Double.MAX_VALUE;
      }

      this.updatePermissions();
      this.onReload(configuration);
   }

   public void onReload(ConfigManager config) {
   }

   public boolean alert(String verbose) {
      return this.player.punishmentManager.handleAlert(this.player, verbose, this);
   }

   public boolean setbackIfAboveSetbackVL() {
      return this.shouldSetback() ? this.player.getSetbackTeleportUtil().executeViolationSetback() : false;
   }

   public boolean shouldSetback() {
      return !this.noSetbackPermission && this.violations > this.setbackVL;
   }

   public String formatOffset(double offset) {
      return offset > 0.001D ? String.format("%.5f", offset) : String.format("%.2E", offset);
   }

   public boolean isTransaction(PacketTypeCommon packetType) {
      return packetType == PacketType.Play.Client.PONG || packetType == PacketType.Play.Client.WINDOW_CONFIRMATION;
   }

   public boolean isFlying(PacketTypeCommon packetType) {
      return WrapperPlayClientPlayerFlying.isFlying(packetType);
   }

   public boolean isUpdate(PacketTypeCommon packetType) {
      return this.isFlying(packetType) || packetType == PacketType.Play.Client.CLIENT_TICK_END || this.isTransaction(packetType);
   }

   public boolean isTickPacket(PacketTypeCommon packetType) {
      if (this.isTickPacketIncludingNonMovement(packetType)) {
         if (!this.isFlying(packetType)) {
            return true;
         } else {
            return !this.player.packetStateData.lastPacketWasTeleport && !this.player.packetStateData.lastPacketWasOnePointSeventeenDuplicate;
         }
      } else {
         return false;
      }
   }

   public boolean isTickPacketIncludingNonMovement(PacketTypeCommon packetType) {
      return this.player.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_21_2) && !this.player.packetStateData.didSendMovementBeforeTickEnd && packetType == PacketType.Play.Client.CLIENT_TICK_END ? true : this.isFlying(packetType);
   }

   public GrimPlayer getPlayer() {
      return this.player;
   }

   public double getViolations() {
      return this.violations;
   }

   public double getDecay() {
      return this.decay;
   }

   public double getSetbackVL() {
      return this.setbackVL;
   }

   public String getCheckName() {
      return this.checkName;
   }

   public String getConfigName() {
      return this.configName;
   }

   public String getAlternativeName() {
      return this.alternativeName;
   }

   public String getDisplayName() {
      return this.displayName;
   }

   public String getDescription() {
      return this.description;
   }

   public boolean isExperimental() {
      return this.experimental;
   }

   public boolean isEnabled() {
      return this.isEnabled;
   }

   public boolean isExemptPermission() {
      return this.exemptPermission;
   }

   public boolean isNoSetbackPermission() {
      return this.noSetbackPermission;
   }

   public boolean isNoModifyPacketPermission() {
      return this.noModifyPacketPermission;
   }

   public long getLastViolationTime() {
      return this.lastViolationTime;
   }

   public void setEnabled(boolean isEnabled) {
      this.isEnabled = isEnabled;
   }
}
