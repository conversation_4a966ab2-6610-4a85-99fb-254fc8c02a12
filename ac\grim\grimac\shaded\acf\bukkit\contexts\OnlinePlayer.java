package ac.grim.grimac.shaded.acf.bukkit.contexts;

import java.util.Objects;
import org.bukkit.entity.Player;

public class OnlinePlayer {
   public final Player player;

   public OnlinePlayer(Player player) {
      this.player = player;
   }

   public Player getPlayer() {
      return this.player;
   }

   public boolean equals(Object o) {
      if (this == o) {
         return true;
      } else if (o != null && this.getClass() == o.getClass()) {
         OnlinePlayer that = (OnlinePlayer)o;
         return Objects.equals(this.player, that.player);
      } else {
         return false;
      }
   }

   public int hashCode() {
      return Objects.hash(new Object[]{this.player});
   }

   public String toString() {
      return "OnlinePlayer{player=" + this.player + '}';
   }
}
