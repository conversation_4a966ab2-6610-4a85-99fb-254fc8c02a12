package ac.grim.grimac.checks.impl.breaking;

import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.CheckData;
import ac.grim.grimac.checks.type.BlockBreakCheck;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketReceiveEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.packettype.PacketType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.ClientVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.DiggingAction;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.world.states.WrappedBlockState;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.world.states.type.StateType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.world.states.type.StateTypes;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.util.Vector3i;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientPlayerFlying;
import ac.grim.grimac.utils.anticheat.update.BlockBreak;
import ac.grim.grimac.utils.math.GrimMath;
import ac.grim.grimac.utils.nmsutil.BlockBreakSpeed;
import java.util.Set;

@CheckData(
   name = "FastBreak",
   description = "Breaking blocks too quickly"
)
public class FastBreak extends Check implements BlockBreakCheck {
   private static final Set<StateType> EXEMPT_STATES = Set.of();
   Vector3i targetBlock = null;
   double maximumBlockDamage = 0.0D;
   long lastFinishBreak = 0L;
   long startBreak = 0L;
   double blockBreakBalance = 0.0D;
   double blockDelayBalance = 0.0D;

   public FastBreak(GrimPlayer playerData) {
      super(playerData);
   }

   public void onBlockBreak(BlockBreak blockBreak) {
      double breakDelay;
      if (blockBreak.action == DiggingAction.START_DIGGING) {
         WrappedBlockState block = blockBreak.block;
         WrappedBlockState defaultState = WrappedBlockState.getDefaultState(this.player.getClientVersion(), block.getType());
         if (defaultState.getType() == StateTypes.AIR || EXEMPT_STATES.contains(defaultState.getType())) {
            return;
         }

         this.startBreak = System.currentTimeMillis() - (long)(this.targetBlock == null ? 50 : 0);
         this.targetBlock = blockBreak.position;
         this.maximumBlockDamage = BlockBreakSpeed.getBlockDamage(this.player, this.targetBlock);
         breakDelay = (double)(System.currentTimeMillis() - this.lastFinishBreak);
         if (breakDelay >= 275.0D) {
            this.blockDelayBalance *= 0.9D;
         } else {
            this.blockDelayBalance += 300.0D - breakDelay;
         }

         if (this.blockDelayBalance > 1000.0D && this.flagAndAlert("delay=" + breakDelay + "ms, type=" + String.valueOf(blockBreak.block.getType())) && this.shouldModifyPackets()) {
            blockBreak.cancel();
         }

         this.clampBalance();
      }

      if (blockBreak.action == DiggingAction.FINISHED_DIGGING && this.targetBlock != null) {
         double predictedTime = Math.ceil(1.0D / this.maximumBlockDamage) * 50.0D;
         breakDelay = (double)(System.currentTimeMillis() - this.startBreak);
         double diff = predictedTime - breakDelay;
         this.clampBalance();
         if (diff < 25.0D) {
            this.blockBreakBalance *= 0.9D;
         } else {
            this.blockBreakBalance += diff;
         }

         if (this.blockBreakBalance > 1000.0D && this.flagAndAlert("diff=" + diff + "ms, balance=" + this.blockBreakBalance + "ms, type=" + String.valueOf(blockBreak.block.getType())) && this.shouldModifyPackets()) {
            blockBreak.cancel();
         }

         this.lastFinishBreak = this.startBreak = System.currentTimeMillis();
      }

   }

   public void onPacketReceive(PacketReceiveEvent event) {
      if (this.player.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_9)) {
         if (event.getPacketType() != PacketType.Play.Client.ANIMATION) {
            return;
         }
      } else if (!WrapperPlayClientPlayerFlying.isFlying(event.getPacketType())) {
         return;
      }

      if (this.targetBlock != null) {
         this.maximumBlockDamage = Math.max(this.maximumBlockDamage, BlockBreakSpeed.getBlockDamage(this.player, this.targetBlock));
      }

   }

   private void clampBalance() {
      double balance = (double)Math.max(1000, this.player.getTransactionPing());
      this.blockBreakBalance = GrimMath.clamp(this.blockBreakBalance, -balance, balance);
      this.blockDelayBalance = GrimMath.clamp(this.blockDelayBalance, -balance, balance);
   }
}
