package ac.grim.grimac.events.packets;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.api.events.GrimQuitEvent;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketListenerAbstract;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketSendEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.UserConnectEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.UserDisconnectEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.UserLoginEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.netty.channel.ChannelHelper;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.ConnectionState;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.packettype.PacketType;
import ac.grim.grimac.utils.anticheat.LogUtil;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

public class PacketPlayerJoinQuit extends PacketListenerAbstract {
   public void onPacketSend(PacketSendEvent event) {
      if (event.getPacketType() == PacketType.Login.Server.LOGIN_SUCCESS) {
         event.getTasksAfterSend().add(() -> {
            GrimAPI.INSTANCE.getPlayerDataManager().addUser(event.getUser());
         });
      }

   }

   public void onUserConnect(UserConnectEvent event) {
      if (event.getUser().getConnectionState() == ConnectionState.PLAY && !GrimAPI.INSTANCE.getPlayerDataManager().exemptUsers.contains(event.getUser())) {
         event.setCancelled(true);
      }

   }

   public void onUserLogin(UserLoginEvent event) {
      Player player = (Player)event.getPlayer();
      if (GrimAPI.INSTANCE.getConfigManager().getConfig().getBooleanElse("debug-pipeline-on-join", false)) {
         LogUtil.info("Pipeline: " + ChannelHelper.pipelineHandlerNamesAsString(event.getUser().getChannel()));
      }

      if (player.hasPermission("grim.alerts") && player.hasPermission("grim.alerts.enable-on-join")) {
         GrimAPI.INSTANCE.getAlertManager().toggleAlerts(player);
      }

      if (player.hasPermission("grim.verbose") && player.hasPermission("grim.verbose.enable-on-join")) {
         GrimAPI.INSTANCE.getAlertManager().toggleVerbose(player);
      }

      if (player.hasPermission("grim.brand") && player.hasPermission("grim.brand.enable-on-join")) {
         GrimAPI.INSTANCE.getAlertManager().toggleBrands(player);
      }

      if (player.hasPermission("grim.spectate") && GrimAPI.INSTANCE.getConfigManager().getConfig().getBooleanElse("spectators.hide-regardless", false)) {
         GrimAPI.INSTANCE.getSpectateManager().onLogin(player);
      }

   }

   public void onUserDisconnect(UserDisconnectEvent event) {
      GrimPlayer grimPlayer = GrimAPI.INSTANCE.getPlayerDataManager().remove(event.getUser());
      if (grimPlayer != null) {
         Bukkit.getPluginManager().callEvent(new GrimQuitEvent(grimPlayer));
      }

      GrimAPI.INSTANCE.getPlayerDataManager().exemptUsers.remove(event.getUser());
      if (event.getUser().getProfile().getUUID() != null) {
         Player player = Bukkit.getPlayer(event.getUser().getProfile().getUUID());
         if (player != null) {
            GrimAPI.INSTANCE.getAlertManager().handlePlayerQuit(player);
            GrimAPI.INSTANCE.getSpectateManager().onQuit(player);
         }

      }
   }
}
