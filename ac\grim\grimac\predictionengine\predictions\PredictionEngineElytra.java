package ac.grim.grimac.predictionengine.predictions;

import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.attribute.Attributes;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.ClientVersion;
import ac.grim.grimac.utils.data.VectorData;
import ac.grim.grimac.utils.nmsutil.ReachUtils;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import org.bukkit.util.Vector;

public class PredictionEngineElytra extends PredictionEngine {
   public List<VectorData> applyInputsToVelocityPossibilities(GrimPlayer player, Set<VectorData> possibleVectors, float speed) {
      List<VectorData> results = new ArrayList();
      Vector currentLook = ReachUtils.getLook(player, player.xRot, player.yRot);
      Iterator var6 = possibleVectors.iterator();

      while(var6.hasNext()) {
         VectorData data = (VectorData)var6.next();
         Vector elytraResult = getElytraMovement(player, data.vector.clone(), currentLook).multiply(player.stuckSpeedMultiplier).multiply(new Vector(0.99F, 0.98F, 0.99F));
         results.add(data.returnNewModified(elytraResult, VectorData.VectorType.InputResult));
         player.trigHandler.toggleShitMath();
         elytraResult = getElytraMovement(player, data.vector.clone(), ReachUtils.getLook(player, player.xRot, player.yRot)).multiply(player.stuckSpeedMultiplier).multiply(new Vector(0.99F, 0.98F, 0.99F));
         player.trigHandler.toggleShitMath();
         results.add(data.returnNewModified(elytraResult, VectorData.VectorType.InputResult));
      }

      return results;
   }

   public static Vector getElytraMovement(GrimPlayer player, Vector vector, Vector lookVector) {
      float yRotRadians = player.yRot * 0.017453292F;
      double horizontalSqrt = Math.sqrt(lookVector.getX() * lookVector.getX() + lookVector.getZ() * lookVector.getZ());
      double horizontalLength = vector.clone().setY(0).length();
      double length = lookVector.length();
      double vertCosRotation = player.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_18_2) ? Math.cos((double)yRotRadians) : (double)player.trigHandler.cos(yRotRadians);
      vertCosRotation = (double)((float)(vertCosRotation * vertCosRotation * Math.min(1.0D, length / 0.4D)));
      double recalculatedGravity = player.compensatedEntities.self.getAttributeValue(Attributes.GRAVITY);
      if (player.clientVelocity.getY() <= 0.0D && player.compensatedEntities.getSlowFallingAmplifier().isPresent()) {
         recalculatedGravity = player.getClientVersion().isOlderThan(ClientVersion.V_1_20_5) ? 0.01D : Math.min(recalculatedGravity, 0.01D);
      }

      vector.add(new Vector(0.0D, recalculatedGravity * (-1.0D + vertCosRotation * 0.75D), 0.0D));
      double d5;
      if (vector.getY() < 0.0D && horizontalSqrt > 0.0D) {
         d5 = vector.getY() * -0.1D * vertCosRotation;
         vector.add(new Vector(lookVector.getX() * d5 / horizontalSqrt, d5, lookVector.getZ() * d5 / horizontalSqrt));
      }

      if (yRotRadians < 0.0F && horizontalSqrt > 0.0D) {
         d5 = horizontalLength * (double)(-player.trigHandler.sin(yRotRadians)) * 0.04D;
         vector.add(new Vector(-lookVector.getX() * d5 / horizontalSqrt, d5 * 3.2D, -lookVector.getZ() * d5 / horizontalSqrt));
      }

      if (horizontalSqrt > 0.0D) {
         vector.add(new Vector((lookVector.getX() / horizontalSqrt * horizontalLength - vector.getX()) * 0.1D, 0.0D, (lookVector.getZ() / horizontalSqrt * horizontalLength - vector.getZ()) * 0.1D));
      }

      return vector;
   }

   public void addJumpsToPossibilities(GrimPlayer player, Set<VectorData> existingVelocities) {
      (new PredictionEngineNormal()).addJumpsToPossibilities(player, existingVelocities);
   }
}
