package ac.grim.grimac.events.bukkit;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.world.BlockFace;
import ac.grim.grimac.utils.blockstate.helper.BlockFaceHelper;
import ac.grim.grimac.utils.collisions.datatypes.SimpleCollisionBox;
import ac.grim.grimac.utils.data.PistonData;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockPistonExtendEvent;
import org.bukkit.event.block.BlockPistonRetractEvent;

public class PistonEvent implements Listener {
   Material SLIME_BLOCK = Material.getMaterial("SLIME_BLOCK");
   Material HONEY_BLOCK = Material.getMaterial("HONEY_BLOCK");

   @EventHandler(
      priority = EventPriority.MONITOR,
      ignoreCancelled = true
   )
   public void onPistonPushEvent(BlockPistonExtendEvent event) {
      boolean hasSlimeBlock = false;
      boolean hasHoneyBlock = false;
      List<SimpleCollisionBox> boxes = new ArrayList();
      Iterator var5 = event.getBlocks().iterator();

      while(var5.hasNext()) {
         Block block = (Block)var5.next();
         boxes.add((new SimpleCollisionBox(0.0D, 0.0D, 0.0D, 1.0D, 1.0D, 1.0D, true)).offset((double)block.getX(), (double)block.getY(), (double)block.getZ()));
         boxes.add((new SimpleCollisionBox(0.0D, 0.0D, 0.0D, 1.0D, 1.0D, 1.0D, true)).offset((double)(block.getX() + event.getDirection().getModX()), (double)(block.getY() + event.getDirection().getModY()), (double)(block.getZ() + event.getDirection().getModZ())));
         if (block.getType() == this.SLIME_BLOCK) {
            hasSlimeBlock = true;
         }

         if (block.getType() == this.HONEY_BLOCK) {
            hasHoneyBlock = true;
         }
      }

      Block piston = event.getBlock();
      boxes.add((new SimpleCollisionBox(0.0D, 0.0D, 0.0D, 1.0D, 1.0D, 1.0D, true)).offset((double)(piston.getX() + event.getDirection().getModX()), (double)(piston.getY() + event.getDirection().getModY()), (double)(piston.getZ() + event.getDirection().getModZ())));
      Iterator var10 = GrimAPI.INSTANCE.getPlayerDataManager().getEntries().iterator();

      while(var10.hasNext()) {
         GrimPlayer player = (GrimPlayer)var10.next();
         if (player.compensatedWorld.isChunkLoaded(event.getBlock().getX() >> 4, event.getBlock().getZ() >> 4)) {
            PistonData data = new PistonData(BlockFaceHelper.fromBukkitFace(event.getDirection()), boxes, player.lastTransactionSent.get(), true, hasSlimeBlock, hasHoneyBlock);
            player.latencyUtils.addRealTimeTaskAsync(player.lastTransactionSent.get(), () -> {
               player.compensatedWorld.activePistons.add(data);
            });
         }
      }

   }

   @EventHandler(
      priority = EventPriority.MONITOR,
      ignoreCancelled = true
   )
   public void onPistonRetractEvent(BlockPistonRetractEvent event) {
      boolean hasSlimeBlock = false;
      boolean hasHoneyBlock = false;
      List<SimpleCollisionBox> boxes = new ArrayList();
      BlockFace face = BlockFaceHelper.fromBukkitFace(event.getDirection());
      if (event.getBlocks().isEmpty()) {
         Block piston = event.getBlock();
         boxes.add((new SimpleCollisionBox(0.0D, 0.0D, 0.0D, 1.0D, 1.0D, 1.0D, true)).offset((double)(piston.getX() + face.getModX()), (double)(piston.getY() + face.getModY()), (double)(piston.getZ() + face.getModZ())));
      }

      Iterator var9 = event.getBlocks().iterator();

      while(var9.hasNext()) {
         Block block = (Block)var9.next();
         boxes.add((new SimpleCollisionBox(0.0D, 0.0D, 0.0D, 1.0D, 1.0D, 1.0D, true)).offset((double)block.getX(), (double)block.getY(), (double)block.getZ()));
         boxes.add((new SimpleCollisionBox(0.0D, 0.0D, 0.0D, 1.0D, 1.0D, 1.0D, true)).offset((double)(block.getX() + face.getModX()), (double)(block.getY() + face.getModY()), (double)(block.getZ() + face.getModZ())));
         if (block.getType() == this.SLIME_BLOCK) {
            hasSlimeBlock = true;
         }

         if (block.getType() == this.HONEY_BLOCK) {
            hasHoneyBlock = true;
         }
      }

      var9 = GrimAPI.INSTANCE.getPlayerDataManager().getEntries().iterator();

      while(var9.hasNext()) {
         GrimPlayer player = (GrimPlayer)var9.next();
         if (player.compensatedWorld.isChunkLoaded(event.getBlock().getX() >> 4, event.getBlock().getZ() >> 4)) {
            PistonData data = new PistonData(BlockFaceHelper.fromBukkitFace(event.getDirection()), boxes, player.lastTransactionSent.get(), false, hasSlimeBlock, hasHoneyBlock);
            player.latencyUtils.addRealTimeTaskAsync(player.lastTransactionSent.get(), () -> {
               player.compensatedWorld.activePistons.add(data);
            });
         }
      }

   }
}
