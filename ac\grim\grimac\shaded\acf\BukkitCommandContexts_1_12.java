package ac.grim.grimac.shaded.acf;

import org.bukkit.NamespacedKey;

class BukkitCommandContexts_1_12 {
   static void register(BukkitCommandContexts contexts) {
      contexts.registerContext(NamespacedKey.class, (c) -> {
         String arg = c.popFirstArg();
         String[] split = ACFPatterns.COLON.split(arg, 2);
         if (split.length == 1) {
            String namespace = c.getFlagValue("namespace", (String)null);
            return namespace == null ? NamespacedKey.minecraft(split[0]) : new NamespacedKey(namespace, split[0]);
         } else {
            return new NamespacedKey(split[0], split[1]);
         }
      });
   }
}
