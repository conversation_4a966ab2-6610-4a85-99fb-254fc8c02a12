package ac.grim.grimac.checks.impl.misc;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.type.PacketCheck;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.PacketEvents;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketReceiveEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.manager.server.ServerVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.packettype.PacketType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.configuration.client.WrapperConfigClientPluginMessage;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientPluginMessage;
import ac.grim.grimac.shaded.kyori.adventure.text.Component;
import ac.grim.grimac.utils.anticheat.MessageUtil;
import java.util.Iterator;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;

public class ClientBrand extends Check implements PacketCheck {
   public static final String channel;
   private String brand = "vanilla";
   private boolean hasBrand = false;

   public ClientBrand(GrimPlayer player) {
      super(player);
   }

   public void onPacketReceive(PacketReceiveEvent event) {
      if (event.getPacketType() == PacketType.Play.Client.PLUGIN_MESSAGE) {
         WrapperPlayClientPluginMessage packet = new WrapperPlayClientPluginMessage(event);
         this.handle(packet.getChannelName(), packet.getData());
      } else if (event.getPacketType() == PacketType.Configuration.Client.PLUGIN_MESSAGE) {
         WrapperConfigClientPluginMessage packet = new WrapperConfigClientPluginMessage(event);
         this.handle(packet.getChannelName(), packet.getData());
      }

   }

   private void handle(String channel, byte[] data) {
      if (channel.equals(ClientBrand.channel)) {
         if (data.length <= 64 && data.length != 0) {
            if (!this.hasBrand) {
               byte[] minusLength = new byte[data.length - 1];
               System.arraycopy(data, 1, minusLength, 0, minusLength.length);
               this.brand = (new String(minusLength)).replace(" (Velocity)", "");
               this.brand = ChatColor.stripColor(this.brand);
               if (!GrimAPI.INSTANCE.getConfigManager().isIgnoredClient(this.brand)) {
                  String message = GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("client-brand-format", "%prefix% &f%player% joined using %brand%");
                  Component component = MessageUtil.replacePlaceholders(this.player, MessageUtil.miniMessage(message));
                  Iterator var6 = Bukkit.getOnlinePlayers().iterator();

                  while(var6.hasNext()) {
                     Player player = (Player)var6.next();
                     if (GrimAPI.INSTANCE.getAlertManager().hasBrandsEnabled(player)) {
                        MessageUtil.sendMessage(player, component);
                     }
                  }
               }
            }
         } else {
            this.brand = "sent " + data.length + " bytes as brand";
         }

         this.hasBrand = true;
      }
   }

   public String getBrand() {
      return this.brand;
   }

   static {
      channel = PacketEvents.getAPI().getServerManager().getVersion().isNewerThanOrEquals(ServerVersion.V_1_13) ? "minecraft:brand" : "MC|Brand";
   }
}
