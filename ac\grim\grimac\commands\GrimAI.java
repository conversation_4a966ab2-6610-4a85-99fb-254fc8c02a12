package ac.grim.grimac.commands;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.manager.AIManager;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.utils.ai.AITrainingManager;
import ac.grim.grimac.utils.anticheat.PlayerDataManager;
import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import co.aikar.commands.bukkit.contexts.OnlinePlayer;
import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.concurrent.CompletableFuture;

/**
 * AI management commands for GrimAC
 */
@CommandAlias("grimai|ai")
@CommandPermission("grim.ai")
public class GrimAI extends BaseCommand {
    
    @Subcommand("status")
    @Description("Show AI system status")
    public void onStatus(CommandSender sender) {
        AIManager aiManager = GrimAPI.INSTANCE.getAIManager();
        
        if (aiManager == null) {
            sender.sendMessage(ChatColor.RED + "AI Manager is not initialized!");
            return;
        }
        
        sender.sendMessage(ChatColor.GOLD + "=== GrimAC AI Status ===");
        sender.sendMessage(ChatColor.YELLOW + "Enabled: " + ChatColor.WHITE + aiManager.isEnabled());
        sender.sendMessage(ChatColor.YELLOW + "Training Mode: " + ChatColor.WHITE + aiManager.isTrainingMode());
        sender.sendMessage(ChatColor.YELLOW + "Configuration: " + ChatColor.WHITE + aiManager.getConfig().getConfigSummary());
        
        // Training statistics
        AITrainingManager.TrainingStatistics stats = aiManager.getTrainingManager().getTrainingStatistics();
        sender.sendMessage(ChatColor.YELLOW + "Training Stats: " + ChatColor.WHITE + stats.toString());
    }
    
    @Subcommand("enable")
    @Description("Enable AI system")
    public void onEnable(CommandSender sender) {
        AIManager aiManager = GrimAPI.INSTANCE.getAIManager();
        
        if (aiManager == null) {
            sender.sendMessage(ChatColor.RED + "AI Manager is not initialized!");
            return;
        }
        
        if (!aiManager.getConfig().isValid()) {
            sender.sendMessage(ChatColor.RED + "AI configuration is invalid! Please check your OpenAI API key and other settings.");
            return;
        }
        
        aiManager.setEnabled(true);
        sender.sendMessage(ChatColor.GREEN + "AI system enabled!");
    }
    
    @Subcommand("disable")
    @Description("Disable AI system")
    public void onDisable(CommandSender sender) {
        AIManager aiManager = GrimAPI.INSTANCE.getAIManager();
        
        if (aiManager == null) {
            sender.sendMessage(ChatColor.RED + "AI Manager is not initialized!");
            return;
        }
        
        aiManager.setEnabled(false);
        sender.sendMessage(ChatColor.YELLOW + "AI system disabled!");
    }
    
    @Subcommand("training enable")
    @Description("Enable AI training mode")
    public void onTrainingEnable(CommandSender sender) {
        AIManager aiManager = GrimAPI.INSTANCE.getAIManager();
        
        if (aiManager == null) {
            sender.sendMessage(ChatColor.RED + "AI Manager is not initialized!");
            return;
        }
        
        aiManager.setTrainingMode(true);
        sender.sendMessage(ChatColor.GREEN + "AI training mode enabled! The AI will now collect data to improve detection accuracy.");
    }
    
    @Subcommand("training disable")
    @Description("Disable AI training mode")
    public void onTrainingDisable(CommandSender sender) {
        AIManager aiManager = GrimAPI.INSTANCE.getAIManager();
        
        if (aiManager == null) {
            sender.sendMessage(ChatColor.RED + "AI Manager is not initialized!");
            return;
        }
        
        aiManager.setTrainingMode(false);
        sender.sendMessage(ChatColor.YELLOW + "AI training mode disabled!");
    }
    
    @Subcommand("training start")
    @Description("Start AI training session")
    public void onTrainingStart(CommandSender sender) {
        AIManager aiManager = GrimAPI.INSTANCE.getAIManager();
        
        if (aiManager == null) {
            sender.sendMessage(ChatColor.RED + "AI Manager is not initialized!");
            return;
        }
        
        if (!aiManager.isEnabled()) {
            sender.sendMessage(ChatColor.RED + "AI system is not enabled!");
            return;
        }
        
        if (!aiManager.getTrainingManager().hasEnoughDataForTraining()) {
            sender.sendMessage(ChatColor.RED + "Not enough training data collected yet. Current: " + 
                             aiManager.getTrainingManager().getQueueSize() + 
                             ", Required: " + aiManager.getConfig().getMinTrainingDataSize());
            return;
        }
        
        sender.sendMessage(ChatColor.YELLOW + "Starting AI training session...");
        
        CompletableFuture<Boolean> trainingResult = aiManager.trainAI();
        trainingResult.thenAccept(success -> {
            if (success) {
                sender.sendMessage(ChatColor.GREEN + "AI training completed successfully!");
            } else {
                sender.sendMessage(ChatColor.RED + "AI training failed! Check console for details.");
            }
        });
    }
    
    @Subcommand("training clear")
    @Description("Clear training data")
    public void onTrainingClear(CommandSender sender) {
        AIManager aiManager = GrimAPI.INSTANCE.getAIManager();
        
        if (aiManager == null) {
            sender.sendMessage(ChatColor.RED + "AI Manager is not initialized!");
            return;
        }
        
        int clearedCount = aiManager.getTrainingManager().getQueueSize();
        aiManager.getTrainingManager().clearTrainingData();
        sender.sendMessage(ChatColor.YELLOW + "Cleared " + clearedCount + " training data entries.");
    }
    
    @Subcommand("analyze")
    @Description("Analyze a specific player with AI")
    @CommandCompletion("@players")
    public void onAnalyze(CommandSender sender, OnlinePlayer target) {
        AIManager aiManager = GrimAPI.INSTANCE.getAIManager();
        
        if (aiManager == null) {
            sender.sendMessage(ChatColor.RED + "AI Manager is not initialized!");
            return;
        }
        
        if (!aiManager.isEnabled()) {
            sender.sendMessage(ChatColor.RED + "AI system is not enabled!");
            return;
        }
        
        Player targetPlayer = target.getPlayer();
        PlayerDataManager playerDataManager = GrimAPI.INSTANCE.getPlayerDataManager();
        GrimPlayer grimPlayer = playerDataManager.getPlayer(targetPlayer);
        
        if (grimPlayer == null) {
            sender.sendMessage(ChatColor.RED + "Player data not found for " + targetPlayer.getName());
            return;
        }
        
        sender.sendMessage(ChatColor.YELLOW + "Analyzing player " + targetPlayer.getName() + " with AI...");
        
        // Get player behavior data
        var playerData = aiManager.getDataCollector();
        // This would trigger a comprehensive analysis
        
        sender.sendMessage(ChatColor.GREEN + "AI analysis initiated for " + targetPlayer.getName() + 
                         ". Check alerts for results.");
    }
    
    @Subcommand("stats")
    @Description("Show detailed AI statistics")
    public void onStats(CommandSender sender) {
        AIManager aiManager = GrimAPI.INSTANCE.getAIManager();
        
        if (aiManager == null) {
            sender.sendMessage(ChatColor.RED + "AI Manager is not initialized!");
            return;
        }
        
        sender.sendMessage(ChatColor.GOLD + "=== GrimAC AI Detailed Statistics ===");
        
        // System status
        sender.sendMessage(ChatColor.YELLOW + "System Status:");
        sender.sendMessage("  Enabled: " + ChatColor.WHITE + aiManager.isEnabled());
        sender.sendMessage("  Training Mode: " + ChatColor.WHITE + aiManager.isTrainingMode());
        
        // Configuration
        sender.sendMessage(ChatColor.YELLOW + "Configuration:");
        var config = aiManager.getConfig();
        sender.sendMessage("  Model: " + ChatColor.WHITE + config.getOpenAIModel());
        sender.sendMessage("  Thread Pool Size: " + ChatColor.WHITE + config.getThreadPoolSize());
        sender.sendMessage("  Cache Size: " + ChatColor.WHITE + config.getMaxCacheSize());
        
        // Training statistics
        sender.sendMessage(ChatColor.YELLOW + "Training Statistics:");
        var trainingStats = aiManager.getTrainingManager().getTrainingStatistics();
        sender.sendMessage("  Queue Size: " + ChatColor.WHITE + trainingStats.getCurrentQueueSize());
        sender.sendMessage("  Total Data Collected: " + ChatColor.WHITE + trainingStats.getTotalDataCollected());
        sender.sendMessage("  Successful Sessions: " + ChatColor.WHITE + trainingStats.getSuccessfulSessions());
        sender.sendMessage("  Ready for Training: " + ChatColor.WHITE + trainingStats.isReadyForTraining());
        
        // Thresholds
        sender.sendMessage(ChatColor.YELLOW + "Detection Thresholds:");
        sender.sendMessage("  Movement: " + ChatColor.WHITE + config.getMovementSuspicionThreshold());
        sender.sendMessage("  Combat: " + ChatColor.WHITE + config.getCombatSuspicionThreshold());
        sender.sendMessage("  Exploit: " + ChatColor.WHITE + config.getExploitSuspicionThreshold());
        sender.sendMessage("  Building: " + ChatColor.WHITE + config.getBuildingSuspicionThreshold());
    }
    
    @Subcommand("reload")
    @Description("Reload AI configuration")
    public void onReload(CommandSender sender) {
        sender.sendMessage(ChatColor.YELLOW + "Reloading AI configuration...");
        
        try {
            // This would reload the AI configuration
            GrimAPI.INSTANCE.getConfigManager().reload();
            sender.sendMessage(ChatColor.GREEN + "AI configuration reloaded successfully!");
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "Failed to reload AI configuration: " + e.getMessage());
        }
    }
    
    @Subcommand("help")
    @Description("Show AI command help")
    @Default
    public void onHelp(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "=== GrimAC AI Commands ===");
        sender.sendMessage(ChatColor.YELLOW + "/grimai status" + ChatColor.WHITE + " - Show AI system status");
        sender.sendMessage(ChatColor.YELLOW + "/grimai enable" + ChatColor.WHITE + " - Enable AI system");
        sender.sendMessage(ChatColor.YELLOW + "/grimai disable" + ChatColor.WHITE + " - Disable AI system");
        sender.sendMessage(ChatColor.YELLOW + "/grimai training enable" + ChatColor.WHITE + " - Enable training mode");
        sender.sendMessage(ChatColor.YELLOW + "/grimai training disable" + ChatColor.WHITE + " - Disable training mode");
        sender.sendMessage(ChatColor.YELLOW + "/grimai training start" + ChatColor.WHITE + " - Start training session");
        sender.sendMessage(ChatColor.YELLOW + "/grimai training clear" + ChatColor.WHITE + " - Clear training data");
        sender.sendMessage(ChatColor.YELLOW + "/grimai analyze <player>" + ChatColor.WHITE + " - Analyze specific player");
        sender.sendMessage(ChatColor.YELLOW + "/grimai stats" + ChatColor.WHITE + " - Show detailed statistics");
        sender.sendMessage(ChatColor.YELLOW + "/grimai reload" + ChatColor.WHITE + " - Reload AI configuration");
    }
}
