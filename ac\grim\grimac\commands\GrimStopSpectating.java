package ac.grim.grimac.commands;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.shaded.acf.BaseCommand;
import ac.grim.grimac.shaded.acf.BukkitCommandCompletionContext;
import ac.grim.grimac.shaded.acf.CommandCompletions;
import ac.grim.grimac.shaded.acf.annotation.CommandAlias;
import ac.grim.grimac.shaded.acf.annotation.CommandCompletion;
import ac.grim.grimac.shaded.acf.annotation.CommandPermission;
import ac.grim.grimac.shaded.acf.annotation.Subcommand;
import ac.grim.grimac.utils.anticheat.MessageUtil;
import java.util.List;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

@CommandAlias("grim|grimac")
public class GrimStopSpectating extends BaseCommand {
   public static final CommandCompletions.CommandCompletionHandler<BukkitCommandCompletionContext> completionHandler = (context) -> {
      return context.getSender().hasPermission("grim.spectate.stophere") ? List.of("here") : List.of();
   };

   @Subcommand("stopspectating")
   @CommandPermission("grim.spectate")
   @CommandCompletion("@stopspectating")
   public void onStopSpectate(CommandSender sender, String[] args) {
      if (sender instanceof Player) {
         Player player = (Player)sender;
         String string = args.length > 0 ? args[0] : null;
         if (GrimAPI.INSTANCE.getSpectateManager().isSpectating(player.getUniqueId())) {
            boolean teleportBack = string == null || !string.equalsIgnoreCase("here") || !sender.hasPermission("grim.spectate.stophere");
            GrimAPI.INSTANCE.getSpectateManager().disable(player, teleportBack);
         } else {
            String message = GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("cannot-spectate-return", "%prefix% &cYou can only do this after spectating a player.");
            message = MessageUtil.replacePlaceholders((Object)sender, (String)message);
            MessageUtil.sendMessage(sender, MessageUtil.miniMessage(message));
         }

      }
   }
}
