package ac.grim.grimac.shaded.acf;

import ac.grim.grimac.shaded.locales.MessageKey;
import ac.grim.grimac.shaded.locales.MessageKeyProvider;

public class InvalidCommandArgument extends RuntimeException {
   final boolean showSyntax;
   final MessageKey key;
   final String[] replacements;

   public InvalidCommandArgument() {
      this((String)null, true);
   }

   public InvalidCommandArgument(boolean showSyntax) {
      this((String)null, showSyntax);
   }

   public InvalidCommandArgument(Message<PERSON>eyProvider key, String... replacements) {
      this(key.getMessageKey(), replacements);
   }

   public InvalidCommandArgument(Message<PERSON><PERSON> key, String... replacements) {
      this(key, true, replacements);
   }

   public InvalidCommandArgument(MessageKeyProvider key, boolean showSyntax, String... replacements) {
      this(key.getMessageKey(), showSyntax, replacements);
   }

   public InvalidCommandArgument(<PERSON><PERSON><PERSON> key, boolean showSyntax, String... replacements) {
      super(key.getKey(), (Throwable)null, false, false);
      this.showSyntax = showSyntax;
      this.key = key;
      this.replacements = replacements;
   }

   public InvalidCommandArgument(String message) {
      this(message, true);
   }

   public InvalidCommandArgument(String message, boolean showSyntax) {
      super(message, (Throwable)null, false, false);
      this.showSyntax = showSyntax;
      this.replacements = null;
      this.key = null;
   }
}
