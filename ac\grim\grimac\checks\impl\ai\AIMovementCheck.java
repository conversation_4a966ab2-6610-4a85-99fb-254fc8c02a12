package ac.grim.grimac.checks.impl.ai;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.CheckData;
import ac.grim.grimac.checks.type.PositionCheck;
import ac.grim.grimac.manager.AIManager;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.utils.ai.models.AIDetectionResult;
import ac.grim.grimac.utils.ai.models.MovementData;
import ac.grim.grimac.utils.anticheat.update.PositionUpdate;

import java.util.concurrent.CompletableFuture;
import java.util.logging.Logger;

/**
 * AI-powered movement analysis check
 * Integrates with traditional movement checks to provide enhanced detection
 */
@CheckData(
    name = "AIMovement",
    description = "AI-powered movement pattern analysis",
    experimental = true
)
public class AIMovementCheck extends Check implements PositionCheck {
    private static final Logger LOGGER = Logger.getLogger("GrimAC-AI-Movement");
    
    private final AIManager aiManager;
    private final double flagThreshold;
    private final double setbackThreshold;
    private final double aiWeight;
    
    // Movement tracking
    private MovementData lastMovement;
    private long lastAnalysisTime;
    private int consecutiveSuspiciousMovements;
    
    public AIMovementCheck(GrimPlayer player) {
        super(player);
        this.aiManager = GrimAPI.INSTANCE.getAIManager();
        
        // Load configuration
        this.flagThreshold = GrimAPI.INSTANCE.getConfigManager().getDoubleElse("ai-checks.movement.flag-threshold", 0.7);
        this.setbackThreshold = GrimAPI.INSTANCE.getConfigManager().getDoubleElse("ai-checks.movement.setback-threshold", 0.8);
        this.aiWeight = GrimAPI.INSTANCE.getConfigManager().getDoubleElse("ai-checks.movement.ai-weight", 0.5);
        
        this.lastAnalysisTime = 0;
        this.consecutiveSuspiciousMovements = 0;
    }
    
    @Override
    public void onPositionUpdate(PositionUpdate positionUpdate) {
        // Skip if AI is not available or enabled
        if (aiManager == null || !aiManager.isEnabled()) {
            return;
        }
        
        // Skip if player is exempt or in creative/spectator
        if (player.disableGrim || player.gamemode.name().equals("CREATIVE") || player.gamemode.name().equals("SPECTATOR")) {
            return;
        }
        
        // Rate limiting - don't analyze every movement to avoid performance issues
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastAnalysisTime < 100) { // Minimum 100ms between analyses
            return;
        }
        lastAnalysisTime = currentTime;
        
        try {
            // Create movement data from position update
            MovementData movementData = createMovementData(positionUpdate);
            
            // Perform AI analysis asynchronously
            CompletableFuture<AIDetectionResult> analysisResult = aiManager.analyzePlayerBehavior(
                player, "movement", movementData
            );
            
            // Handle the result when it completes
            analysisResult.thenAccept(this::handleAIResult);
            
            // Store for next analysis
            lastMovement = movementData;
            
        } catch (Exception e) {
            LOGGER.warning("Error in AI movement analysis for player " + player.user.getName() + ": " + e.getMessage());
        }
    }
    
    private MovementData createMovementData(PositionUpdate update) {
        return new MovementData(
            update.getTo().getX(),
            update.getTo().getY(), 
            update.getTo().getZ(),
            update.getTo().getYaw(),
            update.getTo().getPitch(),
            update.isOnGround(),
            player.isSprinting,
            player.isSneaking,
            System.currentTimeMillis()
        );
    }
    
    private void handleAIResult(AIDetectionResult result) {
        if (result == null || result.isError()) {
            return;
        }
        
        // Log detailed analysis if enabled
        if (GrimAPI.INSTANCE.getConfigManager().getBooleanElse("ai-alerts.log-to-console", false)) {
            LOGGER.info("AI Movement Analysis for " + player.user.getName() + ": " + result.toString());
        }
        
        // Check if result is suspicious enough to flag
        if (result.isSuspicious() && result.getConfidence() >= flagThreshold) {
            consecutiveSuspiciousMovements++;
            
            // Create alert message with AI reasoning
            String alertMessage = String.format("AI Movement Detection - Confidence: %.2f, Type: %s", 
                result.getConfidence(), result.getCheatType() != null ? result.getCheatType() : "Unknown");
            
            if (GrimAPI.INSTANCE.getConfigManager().getBooleanElse("ai-alerts.include-reasoning", true)) {
                alertMessage += " | Reasoning: " + result.getReasoning();
            }
            
            // Flag with traditional check system
            if (flagAndAlert(alertMessage)) {
                // Check if we should setback
                if (result.getConfidence() >= setbackThreshold || consecutiveSuspiciousMovements >= 3) {
                    setbackIfAboveSetbackVL();
                    consecutiveSuspiciousMovements = 0; // Reset after setback
                }
            }
            
        } else {
            // Reset consecutive count if movement is normal
            consecutiveSuspiciousMovements = Math.max(0, consecutiveSuspiciousMovements - 1);
        }
        
        // Update violation level based on AI confidence and traditional checks
        updateViolationLevel(result);
    }
    
    private void updateViolationLevel(AIDetectionResult result) {
        if (!result.isSuspicious()) {
            // Decrease violation level for normal behavior
            violations = Math.max(0, violations - 0.1);
            return;
        }
        
        // Calculate violation increase based on AI confidence and weight
        double aiViolationIncrease = result.getConfidence() * aiWeight;
        
        // Combine with traditional check results (if any)
        double traditionalWeight = 1.0 - aiWeight;
        double traditionalViolation = getTraditionalMovementViolation();
        
        double totalViolationIncrease = (aiViolationIncrease * aiWeight) + (traditionalViolation * traditionalWeight);
        
        // Apply violation increase
        violations += totalViolationIncrease;
        
        // Cap violations to prevent excessive punishment
        violations = Math.min(violations, 50.0);
    }
    
    private double getTraditionalMovementViolation() {
        // This would integrate with existing movement checks
        // For now, return a base value
        return 0.0;
    }
    
    @Override
    public void reload() {
        super.reload();
        // Reset state on reload
        consecutiveSuspiciousMovements = 0;
        lastMovement = null;
    }
    
    // Utility methods for integration with existing checks
    public boolean isAISuspicious() {
        return consecutiveSuspiciousMovements > 0;
    }
    
    public int getConsecutiveSuspiciousCount() {
        return consecutiveSuspiciousMovements;
    }
    
    public void resetSuspiciousCount() {
        consecutiveSuspiciousMovements = 0;
    }
}
