package ac.grim.grimac.utils.ai;

import ac.grim.grimac.utils.ai.models.PlayerBehaviorData;
import ac.grim.grimac.utils.ai.models.AIDetectionResult;
import ac.grim.grimac.utils.ai.models.AITrainingData;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonArray;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.List;
import java.util.logging.Logger;

/**
 * OpenAI API client for AI-powered anti-cheat analysis
 */
public class OpenAIClient {
    private static final Logger LOGGER = Logger.getLogger("GrimAC-AI-OpenAI");
    private static final String OPENAI_API_URL = "https://api.openai.com/v1/chat/completions";
    
    private final AIConfig config;
    private final HttpClient httpClient;
    private final Gson gson;
    
    public OpenAIClient(AIConfig config) {
        this.config = config;
        this.httpClient = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(30))
            .build();
        this.gson = new Gson();
    }
    
    /**
     * Analyze player behavior using OpenAI
     */
    public AIDetectionResult analyzePlayerBehavior(PlayerBehaviorData playerData, String behaviorType, Object behaviorData) {
        try {
            String prompt = buildAnalysisPrompt(playerData, behaviorType, behaviorData);
            String response = sendOpenAIRequest(prompt);
            return parseAnalysisResponse(response, behaviorType);
        } catch (Exception e) {
            LOGGER.severe("OpenAI analysis failed: " + e.getMessage());
            return AIDetectionResult.createError("OpenAI analysis failed: " + e.getMessage());
        }
    }
    
    /**
     * Train AI model with collected data
     */
    public boolean trainWithData(List<AITrainingData> trainingData) {
        try {
            String trainingPrompt = buildTrainingPrompt(trainingData);
            String response = sendOpenAIRequest(trainingPrompt);
            return response.contains("training") || response.contains("learned");
        } catch (Exception e) {
            LOGGER.severe("OpenAI training failed: " + e.getMessage());
            return false;
        }
    }
    
    private String buildAnalysisPrompt(PlayerBehaviorData playerData, String behaviorType, Object behaviorData) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("You are an advanced anti-cheat AI for Minecraft. ");
        prompt.append("Analyze the following player behavior and determine if it's suspicious.\n\n");
        
        prompt.append("Player: ").append(playerData.getPlayerName()).append("\n");
        prompt.append("Behavior Type: ").append(behaviorType).append("\n");
        prompt.append("Current Behavior Data: ").append(gson.toJson(behaviorData)).append("\n");
        
        // Add historical data context
        prompt.append("Historical Data:\n");
        prompt.append("- Movement Patterns: ").append(gson.toJson(playerData.getMovementHistory())).append("\n");
        prompt.append("- Combat Statistics: ").append(gson.toJson(playerData.getCombatStats())).append("\n");
        prompt.append("- Building Patterns: ").append(gson.toJson(playerData.getBuildingPatterns())).append("\n");
        prompt.append("- Previous Violations: ").append(playerData.getViolationHistory().size()).append("\n\n");
        
        prompt.append("Based on this data, provide analysis in JSON format:\n");
        prompt.append("{\n");
        prompt.append("  \"suspicious\": boolean,\n");
        prompt.append("  \"confidence\": number (0.0-1.0),\n");
        prompt.append("  \"reasoning\": \"detailed explanation\",\n");
        prompt.append("  \"cheat_type\": \"suspected cheat type or null\",\n");
        prompt.append("  \"severity\": \"low/medium/high\"\n");
        prompt.append("}\n\n");
        
        prompt.append("Consider factors like:\n");
        prompt.append("- Consistency with normal human behavior\n");
        prompt.append("- Statistical anomalies\n");
        prompt.append("- Pattern recognition\n");
        prompt.append("- Context and game situation\n");
        prompt.append("- Player's historical behavior\n");
        
        return prompt.toString();
    }
    
    private String buildTrainingPrompt(List<AITrainingData> trainingData) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("You are learning to detect cheats in Minecraft. ");
        prompt.append("Here are examples of player behaviors with their outcomes:\n\n");
        
        for (AITrainingData data : trainingData) {
            prompt.append("Example:\n");
            prompt.append("Behavior: ").append(data.getBehaviorType()).append("\n");
            prompt.append("Data: ").append(gson.toJson(data.getBehaviorData())).append("\n");
            prompt.append("Result: ").append(data.getResult().isSuspicious() ? "SUSPICIOUS" : "NORMAL").append("\n");
            prompt.append("Confidence: ").append(data.getResult().getConfidence()).append("\n\n");
        }
        
        prompt.append("Learn from these patterns to improve future detection accuracy. ");
        prompt.append("Respond with 'Training data processed and learned' if successful.");
        
        return prompt.toString();
    }
    
    private String sendOpenAIRequest(String prompt) throws IOException, InterruptedException {
        JsonObject requestBody = new JsonObject();
        requestBody.addProperty("model", config.getOpenAIModel());
        requestBody.addProperty("max_tokens", config.getMaxTokens());
        requestBody.addProperty("temperature", config.getTemperature());
        
        JsonArray messages = new JsonArray();
        JsonObject message = new JsonObject();
        message.addProperty("role", "user");
        message.addProperty("content", prompt);
        messages.add(message);
        requestBody.add("messages", messages);
        
        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(OPENAI_API_URL))
            .header("Authorization", "Bearer " + config.getOpenAIApiKey())
            .header("Content-Type", "application/json")
            .POST(HttpRequest.BodyPublishers.ofString(gson.toJson(requestBody)))
            .build();
        
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        
        if (response.statusCode() != 200) {
            throw new IOException("OpenAI API request failed with status: " + response.statusCode() + 
                                ", body: " + response.body());
        }
        
        JsonObject responseJson = gson.fromJson(response.body(), JsonObject.class);
        return responseJson.getAsJsonArray("choices")
            .get(0).getAsJsonObject()
            .getAsJsonObject("message")
            .get("content").getAsString();
    }
    
    private AIDetectionResult parseAnalysisResponse(String response, String behaviorType) {
        try {
            // Try to extract JSON from response
            String jsonStr = response;
            if (response.contains("{")) {
                int start = response.indexOf("{");
                int end = response.lastIndexOf("}") + 1;
                jsonStr = response.substring(start, end);
            }
            
            JsonObject json = gson.fromJson(jsonStr, JsonObject.class);
            
            boolean suspicious = json.get("suspicious").getAsBoolean();
            double confidence = json.get("confidence").getAsDouble();
            String reasoning = json.get("reasoning").getAsString();
            String cheatType = json.has("cheat_type") && !json.get("cheat_type").isJsonNull() 
                ? json.get("cheat_type").getAsString() : null;
            String severity = json.has("severity") ? json.get("severity").getAsString() : "medium";
            
            return new AIDetectionResult(suspicious, confidence, reasoning, cheatType, severity, behaviorType);
            
        } catch (Exception e) {
            LOGGER.warning("Failed to parse OpenAI response: " + e.getMessage() + ", Response: " + response);
            // Fallback: try to determine from text content
            boolean suspicious = response.toLowerCase().contains("suspicious") || 
                               response.toLowerCase().contains("cheat") ||
                               response.toLowerCase().contains("hack");
            double confidence = suspicious ? 0.5 : 0.1;
            return new AIDetectionResult(suspicious, confidence, "Parsed from text response", null, "medium", behaviorType);
        }
    }
    
    public void shutdown() {
        // HttpClient doesn't need explicit shutdown in Java 11+
        LOGGER.info("OpenAI client shutdown complete.");
    }
}
