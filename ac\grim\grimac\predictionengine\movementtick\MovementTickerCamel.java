package ac.grim.grimac.predictionengine.movementtick;

import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.utils.data.packetentity.PacketEntityCamel;

public class MovementTickerCamel extends MovementTickerHorse {
   public MovementTickerCamel(GrimPlayer player) {
      super(player);
   }

   public float getExtraSpeed() {
      PacketEntityCamel camel = (PacketEntityCamel)this.player.compensatedEntities.self.getRiding();
      boolean wantsToJump = this.player.vehicleData.horseJump > 0.0F && !this.player.vehicleData.horseJumping && this.player.lastOnGround;
      if (wantsToJump) {
         return 0.0F;
      } else {
         return this.player.isSprinting && this.player.vehicleData.camelDashCooldown <= 0 && !camel.dashing ? 0.1F : 0.0F;
      }
   }
}
