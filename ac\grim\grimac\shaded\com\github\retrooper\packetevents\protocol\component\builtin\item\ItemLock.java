package ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.component.builtin.item;

import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.nbt.NBTString;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.PacketWrapper;
import java.util.Objects;

public class ItemLock {
   private String code;

   public ItemLock(String code) {
      this.code = code;
   }

   public static ItemLock read(PacketWrapper<?> wrapper) {
      NBTString codeTag = (NBTString)wrapper.readNBTRaw();
      return new ItemLock(codeTag.getValue());
   }

   public static void write(PacketWrapper<?> wrapper, ItemLock lock) {
      wrapper.writeNBTRaw(new NBTString(lock.code));
   }

   public String getCode() {
      return this.code;
   }

   public void setCode(String code) {
      this.code = code;
   }

   public boolean equals(Object obj) {
      if (this == obj) {
         return true;
      } else if (!(obj instanceof ItemLock)) {
         return false;
      } else {
         ItemLock itemLock = (ItemLock)obj;
         return this.code.equals(itemLock.code);
      }
   }

   public int hashCode() {
      return Objects.hashCode(this.code);
   }
}
