package ac.grim.grimac.checks.impl.crash;

import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.CheckData;
import ac.grim.grimac.checks.type.PacketCheck;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketReceiveEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.packettype.PacketType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientClickWindow;

@CheckData(
   name = "CrashF"
)
public class CrashF extends Check implements PacketCheck {
   public CrashF(GrimPlayer playerData) {
      super(playerData);
   }

   public void onPacketReceive(PacketReceiveEvent event) {
      if (event.getPacketType() == PacketType.Play.Client.CLICK_WINDOW) {
         WrapperPlayClientClickWindow click = new WrapperPlayClientClickWindow(event);
         int clickType = click.getWindowClickType().ordinal();
         int button = click.getButton();
         int windowId = click.getWindowId();
         int slot = click.getSlot();
         if ((clickType == 1 || clickType == 2) && windowId >= 0 && button < 0) {
            if (this.flagAndAlert("clickType=" + clickType + " button=" + button)) {
               event.setCancelled(true);
               this.player.onPacketCancel();
            }
         } else if (windowId >= 0 && clickType == 2 && slot < 0 && this.flagAndAlert("clickType=" + clickType + " button=" + button + " slot=" + slot)) {
            event.setCancelled(true);
            this.player.onPacketCancel();
         }
      }

   }
}
