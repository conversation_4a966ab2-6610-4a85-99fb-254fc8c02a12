package ac.grim.grimac.shaded.acf;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.bukkit.Bukkit;
import org.bukkit.help.GenericCommandHelpTopic;

public class ACFBukkitHelpTopic extends GenericCommandHelpTopic {
   public ACFBukkitHelpTopic(BukkitCommandManager manager, BukkitRootCommand command) {
      super(command);
      final List<String> messages = new ArrayList();
      BukkitCommandIssuer captureIssuer = new BukkitCommandIssuer(manager, Bukkit.getConsoleSender()) {
         public void sendMessageInternal(String message) {
            messages.add(message);
         }
      };
      manager.generateCommandHelp(captureIssuer, command).showHelp(captureIssuer);
      this.fullText = ACFUtil.join((Collection)messages, "\n");
   }
}
