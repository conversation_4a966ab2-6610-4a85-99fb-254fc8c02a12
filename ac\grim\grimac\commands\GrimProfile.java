package ac.grim.grimac.commands;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.acf.BaseCommand;
import ac.grim.grimac.shaded.acf.annotation.CommandAlias;
import ac.grim.grimac.shaded.acf.annotation.CommandCompletion;
import ac.grim.grimac.shaded.acf.annotation.CommandPermission;
import ac.grim.grimac.shaded.acf.annotation.Subcommand;
import ac.grim.grimac.shaded.acf.bukkit.contexts.OnlinePlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.PacketEvents;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.manager.server.ServerVersion;
import ac.grim.grimac.shaded.kyori.adventure.text.Component;
import ac.grim.grimac.utils.anticheat.MessageUtil;
import ac.grim.grimac.utils.anticheat.MultiLibUtil;
import java.util.Iterator;
import org.bukkit.command.CommandSender;

@CommandAlias("grim|grimac")
public class GrimProfile extends BaseCommand {
   @Subcommand("profile")
   @CommandPermission("grim.profile")
   @CommandCompletion("@players")
   public void onConsoleDebug(CommandSender sender, OnlinePlayer target) {
      if (PacketEvents.getAPI().getServerManager().getVersion().isNewerThanOrEquals(ServerVersion.V_1_18) && MultiLibUtil.isExternalPlayer(target.getPlayer())) {
         String alertString = GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("player-not-this-server", "%prefix% &cThis player isn't on this server!");
         alertString = MessageUtil.replacePlaceholders((Object)sender, (String)alertString);
         MessageUtil.sendMessage(sender, MessageUtil.miniMessage(alertString));
      } else {
         GrimPlayer grimPlayer = GrimAPI.INSTANCE.getPlayerDataManager().getPlayer(target.getPlayer());
         if (grimPlayer == null) {
            String message = GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("player-not-found", "%prefix% &cPlayer is exempt or offline!");
            message = MessageUtil.replacePlaceholders((Object)sender, (String)message);
            MessageUtil.sendMessage(sender, MessageUtil.miniMessage(message));
         } else {
            Iterator var4 = GrimAPI.INSTANCE.getConfigManager().getConfig().getStringList("profile").iterator();

            while(var4.hasNext()) {
               String message = (String)var4.next();
               Component component = MessageUtil.miniMessage(message);
               MessageUtil.sendMessage(sender, MessageUtil.replacePlaceholders(grimPlayer, component));
            }

         }
      }
   }
}
