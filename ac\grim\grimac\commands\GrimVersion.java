package ac.grim.grimac.commands;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.shaded.acf.BaseCommand;
import ac.grim.grimac.shaded.acf.annotation.CommandAlias;
import ac.grim.grimac.shaded.acf.annotation.CommandPermission;
import ac.grim.grimac.shaded.acf.annotation.Subcommand;
import ac.grim.grimac.shaded.io.github.retrooper.packetevents.util.folia.FoliaScheduler;
import ac.grim.grimac.shaded.kyori.adventure.text.Component;
import ac.grim.grimac.shaded.kyori.adventure.text.TextComponent;
import ac.grim.grimac.shaded.kyori.adventure.text.event.ClickEvent;
import ac.grim.grimac.shaded.kyori.adventure.text.format.NamedTextColor;
import ac.grim.grimac.shaded.kyori.adventure.text.format.TextDecoration;
import ac.grim.grimac.utils.anticheat.LogUtil;
import ac.grim.grimac.utils.anticheat.MessageUtil;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.http.HttpResponse.BodyHandlers;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import org.bukkit.command.CommandSender;

@CommandAlias("grim|grimac")
public class GrimVersion extends BaseCommand {
   private static long lastCheck;
   private static final AtomicReference<Component> updateMessage = new AtomicReference();
   private static final HttpClient HTTP_CLIENT = HttpClient.newHttpClient();

   @Subcommand("version")
   @CommandPermission("grim.version")
   public void onCommand(CommandSender sender) {
      checkForUpdatesAsync(sender);
   }

   public static void checkForUpdatesAsync(CommandSender sender) {
      String current = GrimAPI.INSTANCE.getExternalAPI().getGrimVersion();
      MessageUtil.sendMessage(sender, ((TextComponent.Builder)((TextComponent.Builder)Component.text().append(Component.text("Grim Version: ").color(NamedTextColor.GRAY))).append(Component.text(current).color(NamedTextColor.AQUA))).build());
      long now = System.currentTimeMillis();
      if (now - lastCheck < 60000L) {
         Component message = (Component)updateMessage.get();
         if (message != null) {
            MessageUtil.sendMessage(sender, message);
         }

      } else {
         lastCheck = now;
         FoliaScheduler.getAsyncScheduler().runNow(GrimAPI.INSTANCE.getPlugin(), (dummy) -> {
            checkForUpdates(sender);
         });
      }
   }

   private static void checkForUpdates(CommandSender sender) {
      String current = GrimAPI.INSTANCE.getExternalAPI().getGrimVersion();

      try {
         HttpRequest request = HttpRequest.newBuilder().uri(URI.create("https://api.modrinth.com/v2/project/LJNGWSvH/version")).GET().header("User-Agent", "GrimAnticheat/Grim/" + GrimAPI.INSTANCE.getExternalAPI().getGrimVersion()).header("Content-Type", "application/json").timeout(Duration.of(5L, ChronoUnit.SECONDS)).build();
         HttpResponse<String> response = HTTP_CLIENT.send(request, BodyHandlers.ofString());
         if (response.statusCode() != 200) {
            Component msg = (Component)updateMessage.get();
            MessageUtil.sendMessage(sender, (Component)Objects.requireNonNullElseGet(msg, () -> {
               return ((TextComponent.Builder)Component.text().append(Component.text("Failed to check latest version.").color(NamedTextColor.RED))).build();
            }));
            LogUtil.error("Failed to check latest GrimAC version. Response code: " + response.statusCode());
            return;
         }

         JsonObject object = (new JsonParser()).parse((String)response.body()).getAsJsonArray().get(0).getAsJsonObject();
         String latest = object.get("version_number").getAsString();
         GrimVersion.Status status = compareVersions(current, latest);
         TextComponent var10000;
         switch(status.ordinal()) {
         case 0:
            var10000 = (TextComponent)Component.text("You are using a development version of GrimAC").color(NamedTextColor.LIGHT_PURPLE);
            break;
         case 1:
            var10000 = (TextComponent)Component.text("You are using the latest version of GrimAC").color(NamedTextColor.GREEN);
            break;
         case 2:
            var10000 = (TextComponent)((TextComponent.Builder)((TextComponent.Builder)((TextComponent.Builder)((TextComponent.Builder)((TextComponent.Builder)Component.text().append(Component.text("New GrimAC version found!").color(NamedTextColor.AQUA))).append(Component.text(" Version ").color(NamedTextColor.GRAY))).append(((TextComponent)Component.text(latest).color(NamedTextColor.GRAY)).decorate(TextDecoration.ITALIC))).append(Component.text(" is available to be downloaded here: ").color(NamedTextColor.GRAY))).append(((TextComponent)((TextComponent)Component.text("https://modrinth.com/plugin/grimac").color(NamedTextColor.GRAY)).decorate(TextDecoration.UNDERLINED)).clickEvent(ClickEvent.openUrl("https://modrinth.com/plugin/grimac")))).build();
            break;
         default:
            throw new IncompatibleClassChangeError();
         }

         Component msg = var10000;
         updateMessage.set(msg);
         MessageUtil.sendMessage(sender, msg);
      } catch (Exception var8) {
         MessageUtil.sendMessage(sender, Component.text("Failed to check latest version.").color(NamedTextColor.RED));
         LogUtil.error("Failed to check latest GrimAC version.", var8);
      }

   }

   private static GrimVersion.Status compareVersions(String local, String latest) {
      if (local.equals(latest)) {
         return GrimVersion.Status.UPDATED;
      } else {
         String[] localParts = local.split("\\.");
         String[] latestParts = latest.split("\\.");
         int length = Math.max(localParts.length, latestParts.length);

         for(int i = 0; i < length; ++i) {
            int localPart = i < localParts.length ? Integer.parseInt(localParts[i]) : 0;
            int latestPart = i < latestParts.length ? Integer.parseInt(latestParts[i]) : 0;
            if (localPart < latestPart) {
               return GrimVersion.Status.OUTDATED;
            }

            if (localPart > latestPart) {
               return GrimVersion.Status.AHEAD;
            }
         }

         return GrimVersion.Status.UPDATED;
      }
   }

   private static enum Status {
      AHEAD,
      UPDATED,
      OUTDATED;

      // $FF: synthetic method
      private static GrimVersion.Status[] $values() {
         return new GrimVersion.Status[]{AHEAD, UPDATED, OUTDATED};
      }
   }
}
