package ac.grim.grimac.commands;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.shaded.acf.BaseCommand;
import ac.grim.grimac.shaded.acf.annotation.CommandAlias;
import ac.grim.grimac.shaded.acf.annotation.CommandPermission;
import ac.grim.grimac.shaded.acf.annotation.Default;
import ac.grim.grimac.shaded.acf.annotation.Subcommand;
import ac.grim.grimac.utils.anticheat.MessageUtil;
import java.util.Iterator;
import org.bukkit.command.CommandSender;

@CommandAlias("grim|grimac")
public class GrimHelp extends BaseCommand {
   @Default
   @Subcommand("help")
   @CommandPermission("grim.help")
   public void onHelp(CommandSender sender) {
      Iterator var2 = GrimAPI.INSTANCE.getConfigManager().getConfig().getStringList("help").iterator();

      while(var2.hasNext()) {
         String string = (String)var2.next();
         string = MessageUtil.replacePlaceholders((Object)sender, (String)string);
         MessageUtil.sendMessage(sender, MessageUtil.miniMessage(string));
      }

   }
}
