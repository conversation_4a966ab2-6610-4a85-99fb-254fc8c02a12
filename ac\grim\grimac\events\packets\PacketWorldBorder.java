package ac.grim.grimac.events.packets;

import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.type.PacketCheck;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketSendEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.packettype.PacketType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerInitializeWorldBorder;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerWorldBorder;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerWorldBorderCenter;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerWorldBorderSize;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayWorldBorderLerpSize;
import ac.grim.grimac.utils.math.GrimMath;

public class PacketWorldBorder extends Check implements PacketCheck {
   double centerX;
   double centerZ;
   double oldDiameter;
   double newDiameter;
   double absoluteMaxSize;
   long startTime = 1L;
   long endTime = 1L;

   public PacketWorldBorder(GrimPlayer playerData) {
      super(playerData);
   }

   public double getCenterX() {
      return this.centerX;
   }

   public double getCenterZ() {
      return this.centerZ;
   }

   public double getCurrentDiameter() {
      double d0 = (double)(System.currentTimeMillis() - this.startTime) / ((double)this.endTime - (double)this.startTime);
      return d0 < 1.0D ? GrimMath.lerp(d0, this.oldDiameter, this.newDiameter) : this.newDiameter;
   }

   public void onPacketSend(PacketSendEvent event) {
      if (event.getPacketType() == PacketType.Play.Server.WORLD_BORDER) {
         WrapperPlayServerWorldBorder packet = new WrapperPlayServerWorldBorder(event);
         this.player.sendTransaction();
         if (packet.getAction() == WrapperPlayServerWorldBorder.WorldBorderAction.SET_SIZE) {
            this.setSize(packet.getRadius());
         } else if (packet.getAction() == WrapperPlayServerWorldBorder.WorldBorderAction.LERP_SIZE) {
            this.setLerp(packet.getOldRadius(), packet.getNewRadius(), packet.getSpeed());
         } else if (packet.getAction() == WrapperPlayServerWorldBorder.WorldBorderAction.SET_CENTER) {
            this.setCenter(packet.getCenterX(), packet.getCenterZ());
         } else if (packet.getAction() == WrapperPlayServerWorldBorder.WorldBorderAction.INITIALIZE) {
            this.setCenter(packet.getCenterX(), packet.getCenterZ());
            this.setLerp(packet.getOldRadius(), packet.getNewRadius(), packet.getSpeed());
            this.setAbsoluteMaxSize((double)packet.getPortalTeleportBoundary());
         }
      }

      if (event.getPacketType() == PacketType.Play.Server.INITIALIZE_WORLD_BORDER) {
         this.player.sendTransaction();
         WrapperPlayServerInitializeWorldBorder border = new WrapperPlayServerInitializeWorldBorder(event);
         this.setCenter(border.getX(), border.getZ());
         this.setLerp(border.getOldDiameter(), border.getNewDiameter(), border.getSpeed());
         this.setAbsoluteMaxSize((double)border.getPortalTeleportBoundary());
      }

      if (event.getPacketType() == PacketType.Play.Server.WORLD_BORDER_CENTER) {
         this.player.sendTransaction();
         WrapperPlayServerWorldBorderCenter center = new WrapperPlayServerWorldBorderCenter(event);
         this.setCenter(center.getX(), center.getZ());
      }

      if (event.getPacketType() == PacketType.Play.Server.WORLD_BORDER_SIZE) {
         this.player.sendTransaction();
         WrapperPlayServerWorldBorderSize size = new WrapperPlayServerWorldBorderSize(event);
         this.setSize(size.getDiameter());
      }

      if (event.getPacketType() == PacketType.Play.Server.WORLD_BORDER_LERP_SIZE) {
         this.player.sendTransaction();
         WrapperPlayWorldBorderLerpSize size = new WrapperPlayWorldBorderLerpSize(event);
         this.setLerp(size.getOldDiameter(), size.getNewDiameter(), size.getSpeed());
      }

   }

   private void setCenter(double x, double z) {
      this.player.latencyUtils.addRealTimeTask(this.player.lastTransactionSent.get(), () -> {
         this.centerX = x;
         this.centerZ = z;
      });
   }

   private void setSize(double size) {
      this.player.latencyUtils.addRealTimeTask(this.player.lastTransactionSent.get(), () -> {
         this.oldDiameter = size;
         this.newDiameter = size;
      });
   }

   private void setLerp(double oldDiameter, double newDiameter, long length) {
      this.player.latencyUtils.addRealTimeTask(this.player.lastTransactionSent.get(), () -> {
         this.oldDiameter = oldDiameter;
         this.newDiameter = newDiameter;
         this.startTime = System.currentTimeMillis();
         this.endTime = this.startTime + length;
      });
   }

   private void setAbsoluteMaxSize(double absoluteMaxSize) {
      this.player.latencyUtils.addRealTimeTask(this.player.lastTransactionSent.get(), () -> {
         this.absoluteMaxSize = absoluteMaxSize;
      });
   }

   public double getAbsoluteMaxSize() {
      return this.absoluteMaxSize;
   }
}
