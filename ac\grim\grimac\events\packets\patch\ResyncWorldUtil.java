package ac.grim.grimac.events.packets.patch;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.PacketEvents;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.manager.server.ServerVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.netty.channel.ChannelHelper;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.world.states.WrappedBlockState;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.util.Vector3i;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.PacketWrapper;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerAcknowledgeBlockChanges;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerBlockChange;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerMultiBlockChange;
import ac.grim.grimac.shaded.io.github.retrooper.packetevents.util.folia.FoliaScheduler;
import ac.grim.grimac.utils.collisions.datatypes.SimpleCollisionBox;
import ac.grim.grimac.utils.math.GrimMath;
import java.util.HashMap;
import org.bukkit.Chunk;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.block.data.BlockData;

public class ResyncWorldUtil {
   static HashMap<BlockData, Integer> blockDataToId = new HashMap();

   public static void resyncPosition(GrimPlayer player, Vector3i pos) {
      player.getResyncHandler().resync(pos.getX(), pos.getY(), pos.getZ(), pos.getX(), pos.getY(), pos.getZ());
   }

   public static void resyncPositions(GrimPlayer player, SimpleCollisionBox box) {
      player.getResyncHandler().resync(GrimMath.floor(box.minX), GrimMath.floor(box.minY), GrimMath.floor(box.minZ), GrimMath.ceil(box.maxX), GrimMath.ceil(box.maxY), GrimMath.ceil(box.maxZ));
   }

   public static void resyncPositions(GrimPlayer player, int minBlockX, int mY, int minBlockZ, int maxBlockX, int mxY, int maxBlockZ) {
      if (player.compensatedWorld.isChunkLoaded(minBlockX >> 4, minBlockZ >> 4) && player.compensatedWorld.isChunkLoaded(minBlockX >> 4, maxBlockZ >> 4) && player.compensatedWorld.isChunkLoaded(maxBlockX >> 4, minBlockZ >> 4) && player.compensatedWorld.isChunkLoaded(maxBlockX >> 4, maxBlockZ >> 4)) {
         if (player.bukkitPlayer != null) {
            World world = player.bukkitPlayer.getWorld();
            FoliaScheduler.getRegionScheduler().execute(GrimAPI.INSTANCE.getPlugin(), world, minBlockX >> 4, minBlockZ >> 4, () -> {
               boolean flat = PacketEvents.getAPI().getServerManager().getVersion().isNewerThanOrEquals(ServerVersion.V_1_13);
               if (player.getSetbackTeleportUtil().hasAcceptedSpawnTeleport) {
                  if (world.isChunkLoaded(minBlockX >> 4, minBlockZ >> 4) && world.isChunkLoaded(minBlockX >> 4, maxBlockZ >> 4) && world.isChunkLoaded(maxBlockX >> 4, minBlockZ >> 4) && world.isChunkLoaded(maxBlockX >> 4, maxBlockZ >> 4)) {
                     int minSection = player.compensatedWorld.getMinHeight() >> 4;
                     int minBlock = minSection << 4;
                     int maxBlock = player.compensatedWorld.getMaxHeight() - 1;
                     int minBlockY = Math.max(minBlock, mY);
                     int maxBlockY = Math.min(maxBlock, mxY);
                     int minChunkX = minBlockX >> 4;
                     int maxChunkX = maxBlockX >> 4;
                     int minChunkY = minBlockY >> 4;
                     int maxChunkY = maxBlockY >> 4;
                     int minChunkZ = minBlockZ >> 4;
                     int maxChunkZ = maxBlockZ >> 4;

                     for(int currChunkZ = minChunkZ; currChunkZ <= maxChunkZ; ++currChunkZ) {
                        int minZ = currChunkZ == minChunkZ ? minBlockZ & 15 : 0;
                        int maxZ = currChunkZ == maxChunkZ ? maxBlockZ & 15 : 15;

                        for(int currChunkX = minChunkX; currChunkX <= maxChunkX; ++currChunkX) {
                           int minX = currChunkX == minChunkX ? minBlockX & 15 : 0;
                           int maxX = currChunkX == maxChunkX ? maxBlockX & 15 : 15;
                           Chunk chunk = world.getChunkAt(currChunkX, currChunkZ);

                           for(int currChunkY = minChunkY; currChunkY <= maxChunkY; ++currChunkY) {
                              int minY = currChunkY == minChunkY ? minBlockY & 15 : 0;
                              int maxY = currChunkY == maxChunkY ? maxBlockY & 15 : 15;
                              int totalBlocks = (maxX - minX + 1) * (maxZ - minZ + 1) * (maxY - minY + 1);
                              WrapperPlayServerMultiBlockChange.EncodedBlock[] encodedBlocks = new WrapperPlayServerMultiBlockChange.EncodedBlock[totalBlocks];
                              int blockIndex = 0;

                              for(int currZ = minZ; currZ <= maxZ; ++currZ) {
                                 for(int currX = minX; currX <= maxX; ++currX) {
                                    for(int currY = minY; currY <= maxY; ++currY) {
                                       Block block = chunk.getBlock(currX, currY | currChunkY << 4, currZ);
                                       int blockId;
                                       if (flat) {
                                          blockId = (Integer)blockDataToId.computeIfAbsent(block.getBlockData(), (data) -> {
                                             return WrappedBlockState.getByString(PacketEvents.getAPI().getServerManager().getVersion().toClientVersion(), data.getAsString(false)).getGlobalId();
                                          });
                                       } else {
                                          blockId = block.getType().getId() << 4 | block.getData();
                                       }

                                       encodedBlocks[blockIndex++] = new WrapperPlayServerMultiBlockChange.EncodedBlock(blockId, currX, currY | currChunkY << 4, currZ);
                                    }
                                 }
                              }

                              WrapperPlayServerMultiBlockChange packet = new WrapperPlayServerMultiBlockChange(new Vector3i(currChunkX, currChunkY, currChunkZ), true, encodedBlocks);
                              ChannelHelper.runInEventLoop(player.user.getChannel(), () -> {
                                 player.user.sendPacket((PacketWrapper)packet);
                              });
                           }
                        }
                     }

                  }
               }
            });
         }
      }
   }

   public static void resyncPosition(GrimPlayer player, Vector3i pos, int sequence) {
      if (player.bukkitPlayer != null) {
         int chunkX = pos.x >> 4;
         int chunkZ = pos.z >> 4;
         World world = player.bukkitPlayer.getWorld();
         FoliaScheduler.getRegionScheduler().execute(GrimAPI.INSTANCE.getPlugin(), world, chunkX, chunkZ, () -> {
            if (player.bukkitPlayer.isOnline() && player.getSetbackTeleportUtil().hasAcceptedSpawnTeleport) {
               if (player.compensatedWorld.isChunkLoaded(chunkX, chunkZ)) {
                  if (!(player.bukkitPlayer.getLocation().distance(new Location(world, (double)pos.x, (double)pos.y, (double)pos.z)) >= 64.0D)) {
                     if (world.isChunkLoaded(chunkX, chunkZ)) {
                        Block block = world.getChunkAt(chunkX, chunkZ).getBlock(pos.x & 15, pos.y, pos.z & 15);
                        int blockId;
                        if (PacketEvents.getAPI().getServerManager().getVersion().isNewerThanOrEquals(ServerVersion.V_1_13)) {
                           blockId = (Integer)blockDataToId.computeIfAbsent(block.getBlockData(), (data) -> {
                              return WrappedBlockState.getByString(PacketEvents.getAPI().getServerManager().getVersion().toClientVersion(), data.getAsString(false)).getGlobalId();
                           });
                        } else {
                           blockId = block.getType().getId() << 4 | block.getData();
                        }

                        player.user.sendPacket((PacketWrapper)(new WrapperPlayServerBlockChange(pos, blockId)));
                        if (PacketEvents.getAPI().getServerManager().getVersion().isNewerThanOrEquals(ServerVersion.V_1_19)) {
                           player.user.sendPacket((PacketWrapper)(new WrapperPlayServerAcknowledgeBlockChanges(sequence)));
                        }

                     }
                  }
               }
            }
         });
      }
   }
}
