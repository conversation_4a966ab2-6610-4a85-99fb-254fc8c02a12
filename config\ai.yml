# AI Configuration for GrimAC
# This file configures the AI-powered anti-cheat features

# Main AI Settings
ai:
  # Enable AI-powered detection (requires OpenAI API key)
  enabled: false
  
  # Enable training mode to collect data and improve AI
  training-mode: false
  
  # Thread pool size for AI processing
  thread-pool-size: 4
  
  # OpenAI Configuration
  openai:
    # Your OpenAI API key (required for AI features)
    # Get your API key from: https://platform.openai.com/api-keys
    api-key: "********************************************************************************************************************************************************************"
    
    # OpenAI model to use
    model: "gpt-4"
    
    # Maximum tokens per request
    max-tokens: 1000
    
    # Temperature for AI responses (0.0-1.0, lower = more deterministic)
    temperature: 0.3
  
  # Cache Settings
  cache:
    # How long to cache AI analysis results (in minutes)
    expiration-minutes: 30
    
    # Maximum number of cached results
    max-size: 1000
  
  # Detection Thresholds (0.0-1.0, higher = more strict)
  thresholds:
    # Movement behavior detection threshold
    movement: 0.7
    
    # Combat behavior detection threshold
    combat: 0.8
    
    # Exploit detection threshold
    exploit: 0.9
    
    # Building pattern detection threshold
    building: 0.6
  
  # Training Configuration
  training:
    # Minimum amount of data needed before training
    min-data-size: 100
    
    # Maximum amount of data to use for training
    max-data-size: 10000
    
    # Number of data points to process in each training batch
    batch-size: 50
    
    # Enable automatic training
    auto-training: true
    
    # How often to run automatic training (in hours)
    interval-hours: 24
  
  # Data Collection Settings
  data-collection:
    # Collect movement data for AI analysis
    movement: true
    
    # Collect combat data for AI analysis
    combat: true
    
    # Collect exploit data for AI analysis
    exploit: true
    
    # Collect building data for AI analysis
    building: true
    
    # How long to keep collected data (in days)
    retention-days: 30

# AI-Enhanced Check Settings
ai-checks:
  # AI Movement Analysis
  movement:
    enabled: true
    # Confidence threshold for flagging (0.0-1.0)
    flag-threshold: 0.7
    # Confidence threshold for setbacks (0.0-1.0)
    setback-threshold: 0.8
    # Weight of AI analysis vs traditional checks (0.0-1.0)
    ai-weight: 0.5
  
  # AI Combat Analysis
  combat:
    enabled: true
    flag-threshold: 0.8
    setback-threshold: 0.9
    ai-weight: 0.6
  
  # AI Exploit Detection
  exploit:
    enabled: true
    flag-threshold: 0.9
    setback-threshold: 0.95
    ai-weight: 0.7
  
  # AI Building Analysis
  building:
    enabled: true
    flag-threshold: 0.6
    setback-threshold: 0.8
    ai-weight: 0.4

# AI Alerts and Logging
ai-alerts:
  # Send alerts for AI detections
  enabled: true
  
  # Minimum confidence level to send alerts (0.0-1.0)
  min-confidence: 0.7
  
  # Include AI reasoning in alerts
  include-reasoning: true
  
  # Log AI analysis to console
  log-to-console: false
    
  # Log AI training sessions
  log-training: true

# Performance Settings
performance:
  # Maximum time to wait for AI analysis (in milliseconds)
  max-analysis-time: 5000
  
  # Skip AI analysis if server TPS is below this threshold
  min-tps-threshold: 18.0
  
  # Maximum number of concurrent AI analyses
  max-concurrent-analyses: 10
