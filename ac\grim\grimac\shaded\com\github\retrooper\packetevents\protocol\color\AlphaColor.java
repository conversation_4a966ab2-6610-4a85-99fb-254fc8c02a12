package ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.color;

import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.nbt.NBT;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.nbt.NBTFloat;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.nbt.NBTInt;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.nbt.NBTList;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.nbt.NBTNumber;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.nbt.NBTType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.ClientVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.util.MathUtil;
import ac.grim.grimac.shaded.jetbrains.annotations.Range;

public final class AlphaColor extends Color {
   public static final AlphaColor WHITE = new AlphaColor(-1);
   private final int alpha;

   public AlphaColor(@Range(from = 0L,to = 255L) int red, @Range(from = 0L,to = 255L) int green, @Range(from = 0L,to = 255L) int blue) {
      this(255, red, green, blue);
   }

   public AlphaColor(@Range(from = 0L,to = 255L) int alpha, @Range(from = 0L,to = 255L) int red, @Range(from = 0L,to = 255L) int green, @Range(from = 0L,to = 255L) int blue) {
      super(red, green, blue);
      this.alpha = alpha;
   }

   public AlphaColor(@Range(from = 0L,to = 1L) float red, @Range(from = 0L,to = 1L) float green, @Range(from = 0L,to = 1L) float blue) {
      this(1.0F, red, green, blue);
   }

   public AlphaColor(@Range(from = 0L,to = 1L) float alpha, @Range(from = 0L,to = 1L) float red, @Range(from = 0L,to = 1L) float green, @Range(from = 0L,to = 1L) float blue) {
      super(red, green, blue);
      this.alpha = MathUtil.floor(alpha * 255.0F);
   }

   public AlphaColor(int rgb) {
      this(rgb >> 24 & 255, rgb >> 16 & 255, rgb >> 8 & 255, rgb & 255);
   }

   public static AlphaColor decode(NBT nbt, ClientVersion version) {
      if (nbt instanceof NBTNumber) {
         return new AlphaColor(((NBTNumber)nbt).getAsInt());
      } else {
         NBTList<?> list = (NBTList)nbt;
         float red = ((NBTNumber)list.getTag(0)).getAsFloat();
         float green = ((NBTNumber)list.getTag(1)).getAsFloat();
         float blue = ((NBTNumber)list.getTag(2)).getAsFloat();
         float alpha = ((NBTNumber)list.getTag(3)).getAsFloat();
         return new AlphaColor(alpha, red, green, blue);
      }
   }

   public static NBT encode(AlphaColor color, ClientVersion version) {
      if (version.isNewerThanOrEquals(ClientVersion.V_1_21_2)) {
         return new NBTInt(color.asRGB());
      } else {
         NBTList<NBTFloat> list = new NBTList(NBTType.FLOAT, 4);
         list.addTag(new NBTFloat((float)color.red));
         list.addTag(new NBTFloat((float)color.green));
         list.addTag(new NBTFloat((float)color.blue));
         list.addTag(new NBTFloat((float)color.alpha));
         return list;
      }
   }

   public AlphaColor withAlpha(@Range(from = 0L,to = 255L) int alpha) {
      return new AlphaColor(alpha, this.red, this.green, this.blue);
   }

   public AlphaColor withRed(@Range(from = 0L,to = 255L) int red) {
      return new AlphaColor(this.alpha, red, this.green, this.blue);
   }

   public AlphaColor withGreen(@Range(from = 0L,to = 255L) int green) {
      return new AlphaColor(this.alpha, this.red, green, this.blue);
   }

   public AlphaColor withBlue(@Range(from = 0L,to = 255L) int blue) {
      return new AlphaColor(this.alpha, this.red, this.green, blue);
   }

   public int asRGB() {
      return this.alpha << 24 | this.red << 16 | this.green << 8 | this.blue;
   }

   @Range(
      from = 0L,
      to = 255L
   )
   public int alpha() {
      return this.alpha;
   }

   @Range(
      from = 0L,
      to = 255L
   )
   public int red() {
      return this.red;
   }

   @Range(
      from = 0L,
      to = 255L
   )
   public int green() {
      return this.green;
   }

   @Range(
      from = 0L,
      to = 255L
   )
   public int blue() {
      return this.blue;
   }
}
