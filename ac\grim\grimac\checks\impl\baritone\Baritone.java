package ac.grim.grimac.checks.impl.baritone;

import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.CheckData;
import ac.grim.grimac.checks.impl.aim.processor.AimProcessor;
import ac.grim.grimac.checks.type.RotationCheck;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.utils.anticheat.update.RotationUpdate;
import ac.grim.grimac.utils.data.HeadRotation;
import ac.grim.grimac.utils.math.GrimMath;

@CheckData(
   name = "Baritone"
)
public class Baritone extends Check implements RotationCheck {
   private int verbose;

   public Baritone(GrimPlayer playerData) {
      super(playerData);
   }

   public void process(RotationUpdate rotationUpdate) {
      HeadRotation from = rotationUpdate.getFrom();
      HeadRotation to = rotationUpdate.getTo();
      float deltaPitch = Math.abs(to.getPitch() - from.getPitch());
      if (rotationUpdate.getDeltaXRot() == 0.0F && deltaPitch > 0.0F && deltaPitch < 1.0F && Math.abs(to.getPitch()) != 90.0F) {
         if (rotationUpdate.getProcessor().divisorY < GrimMath.MINIMUM_DIVISOR) {
            ++this.verbose;
            if (this.verbose > 8) {
               this.flagAndAlert("Divisor " + AimProcessor.convertToSensitivity(rotationUpdate.getProcessor().divisorX));
            }
         } else {
            this.verbose = 0;
         }
      }

   }
}
