package ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.component.builtin.item;

import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.PacketWrapper;
import ac.grim.grimac.shaded.jetbrains.annotations.Nullable;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

public class ItemProfile {
   @Nullable
   private String name;
   @Nullable
   private UUID id;
   private List<ItemProfile.Property> properties;

   public ItemProfile(@Nullable String name, @Nullable UUID id, List<ItemProfile.Property> properties) {
      this.name = name;
      this.id = id;
      this.properties = properties;
   }

   public static ItemProfile read(PacketWrapper<?> wrapper) {
      String name = (String)wrapper.readOptional((ew) -> {
         return ew.readString(16);
      });
      UUID id = (UUID)wrapper.readOptional(PacketWrapper::readUUID);
      List<ItemProfile.Property> properties = wrapper.readList(ItemProfile.Property::read);
      return new ItemProfile(name, id, properties);
   }

   public static void write(PacketWrapper<?> wrapper, ItemProfile profile) {
      wrapper.writeOptional(profile.name, (ew, name) -> {
         ew.writeString(name, 16);
      });
      wrapper.writeOptional(profile.id, PacketWrapper::writeUUID);
      wrapper.writeList(profile.properties, ItemProfile.Property::write);
   }

   @Nullable
   public String getName() {
      return this.name;
   }

   public void setName(@Nullable String name) {
      this.name = name;
   }

   @Nullable
   public UUID getId() {
      return this.id;
   }

   public void setId(@Nullable UUID id) {
      this.id = id;
   }

   public void addProperty(ItemProfile.Property property) {
      this.properties.add(property);
   }

   public List<ItemProfile.Property> getProperties() {
      return this.properties;
   }

   public void setProperties(List<ItemProfile.Property> properties) {
      this.properties = properties;
   }

   public boolean equals(Object obj) {
      if (this == obj) {
         return true;
      } else if (!(obj instanceof ItemProfile)) {
         return false;
      } else {
         ItemProfile that = (ItemProfile)obj;
         if (!Objects.equals(this.name, that.name)) {
            return false;
         } else {
            return !Objects.equals(this.id, that.id) ? false : this.properties.equals(that.properties);
         }
      }
   }

   public int hashCode() {
      return Objects.hash(new Object[]{this.name, this.id, this.properties});
   }

   public static class Property {
      private String name;
      private String value;
      @Nullable
      private String signature;

      public Property(String name, String value, @Nullable String signature) {
         this.name = name;
         this.value = value;
         this.signature = signature;
      }

      public static ItemProfile.Property read(PacketWrapper<?> wrapper) {
         String name = wrapper.readString(64);
         String value = wrapper.readString(32767);
         String signature = (String)wrapper.readOptional((ew) -> {
            return ew.readString(1024);
         });
         return new ItemProfile.Property(name, value, signature);
      }

      public static void write(PacketWrapper<?> wrapper, ItemProfile.Property property) {
         wrapper.writeString(property.name, 64);
         wrapper.writeString(property.value, 32767);
         wrapper.writeOptional(property.signature, (ew, signature) -> {
            ew.writeString(signature, 1024);
         });
      }

      public String getName() {
         return this.name;
      }

      public void setName(String name) {
         this.name = name;
      }

      public String getValue() {
         return this.value;
      }

      public void setValue(String value) {
         this.value = value;
      }

      @Nullable
      public String getSignature() {
         return this.signature;
      }

      public void setSignature(@Nullable String signature) {
         this.signature = signature;
      }

      public boolean equals(Object obj) {
         if (this == obj) {
            return true;
         } else if (!(obj instanceof ItemProfile.Property)) {
            return false;
         } else {
            ItemProfile.Property property = (ItemProfile.Property)obj;
            if (!this.name.equals(property.name)) {
               return false;
            } else {
               return !this.value.equals(property.value) ? false : Objects.equals(this.signature, property.signature);
            }
         }
      }

      public int hashCode() {
         return Objects.hash(new Object[]{this.name, this.value, this.signature});
      }
   }
}
