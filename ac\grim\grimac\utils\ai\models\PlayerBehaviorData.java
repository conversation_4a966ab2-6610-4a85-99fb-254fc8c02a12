package ac.grim.grimac.utils.ai.models;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * Comprehensive player behavior data for AI analysis
 */
public class PlayerBehaviorData {
    private final UUID playerId;
    private final String playerName;
    private final long createdTime;
    
    // Movement data
    private final Queue<MovementData> movementHistory;
    private final MovementStatistics movementStats;
    
    // Combat data
    private final Queue<CombatData> combatHistory;
    private final CombatStatistics combatStats;
    
    // Building data
    private final Queue<BuildingData> buildingHistory;
    private final BuildingStatistics buildingStats;
    
    // Exploit detection data
    private final Queue<ExploitData> exploitHistory;
    
    // Violation history
    private final List<ViolationRecord> violationHistory;
    
    // Session data
    private long sessionStartTime;
    private int totalPacketsReceived;
    private int suspiciousEvents;
    
    public PlayerBehaviorData(UUID playerId, String playerName) {
        this.playerId = playerId;
        this.playerName = playerName;
        this.createdTime = System.currentTimeMillis();
        this.sessionStartTime = System.currentTimeMillis();
        
        // Initialize data structures with limited size to prevent memory issues
        this.movementHistory = new ConcurrentLinkedQueue<>();
        this.combatHistory = new ConcurrentLinkedQueue<>();
        this.buildingHistory = new ConcurrentLinkedQueue<>();
        this.exploitHistory = new ConcurrentLinkedQueue<>();
        this.violationHistory = new ArrayList<>();
        
        this.movementStats = new MovementStatistics();
        this.combatStats = new CombatStatistics();
        this.buildingStats = new BuildingStatistics();
        
        this.totalPacketsReceived = 0;
        this.suspiciousEvents = 0;
    }
    
    // Movement data methods
    public void addMovementData(MovementData data) {
        movementHistory.offer(data);
        movementStats.update(data);
        
        // Keep only last 1000 movement records
        while (movementHistory.size() > 1000) {
            movementHistory.poll();
        }
    }
    
    // Combat data methods
    public void addCombatData(CombatData data) {
        combatHistory.offer(data);
        combatStats.update(data);
        
        // Keep only last 500 combat records
        while (combatHistory.size() > 500) {
            combatHistory.poll();
        }
    }
    
    // Building data methods
    public void addBuildingData(BuildingData data) {
        buildingHistory.offer(data);
        buildingStats.update(data);
        
        // Keep only last 500 building records
        while (buildingHistory.size() > 500) {
            buildingHistory.poll();
        }
    }
    
    // Exploit data methods
    public void addExploitData(ExploitData data) {
        exploitHistory.offer(data);
        
        // Keep only last 200 exploit records
        while (exploitHistory.size() > 200) {
            exploitHistory.poll();
        }
    }
    
    // Violation methods
    public void addViolation(ViolationRecord violation) {
        violationHistory.add(violation);
        suspiciousEvents++;
        
        // Keep only last 100 violations
        while (violationHistory.size() > 100) {
            violationHistory.remove(0);
        }
    }
    
    // Session methods
    public void incrementPacketCount() {
        totalPacketsReceived++;
    }
    
    public void resetSession() {
        sessionStartTime = System.currentTimeMillis();
        totalPacketsReceived = 0;
        suspiciousEvents = 0;
    }
    
    // Getters
    public UUID getPlayerId() { return playerId; }
    public String getPlayerName() { return playerName; }
    public long getCreatedTime() { return createdTime; }
    public long getSessionStartTime() { return sessionStartTime; }
    public int getTotalPacketsReceived() { return totalPacketsReceived; }
    public int getSuspiciousEvents() { return suspiciousEvents; }
    
    public Queue<MovementData> getMovementHistory() { return new ConcurrentLinkedQueue<>(movementHistory); }
    public Queue<CombatData> getCombatHistory() { return new ConcurrentLinkedQueue<>(combatHistory); }
    public Queue<BuildingData> getBuildingHistory() { return new ConcurrentLinkedQueue<>(buildingHistory); }
    public Queue<ExploitData> getExploitHistory() { return new ConcurrentLinkedQueue<>(exploitHistory); }
    public List<ViolationRecord> getViolationHistory() { return new ArrayList<>(violationHistory); }
    
    public MovementStatistics getMovementStats() { return movementStats; }
    public CombatStatistics getCombatStats() { return combatStats; }
    public BuildingStatistics getBuildingStats() { return buildingStats; }
    
    // Utility methods
    public long getSessionDurationMs() {
        return System.currentTimeMillis() - sessionStartTime;
    }
    
    public double getSuspiciousEventRate() {
        return totalPacketsReceived > 0 ? (double) suspiciousEvents / totalPacketsReceived : 0.0;
    }
    
    public Map<String, Object> getBehaviorSummary() {
        Map<String, Object> summary = new HashMap<>();
        summary.put("playerId", playerId.toString());
        summary.put("playerName", playerName);
        summary.put("sessionDurationMs", getSessionDurationMs());
        summary.put("totalPackets", totalPacketsReceived);
        summary.put("suspiciousEvents", suspiciousEvents);
        summary.put("suspiciousRate", getSuspiciousEventRate());
        summary.put("movementRecords", movementHistory.size());
        summary.put("combatRecords", combatHistory.size());
        summary.put("buildingRecords", buildingHistory.size());
        summary.put("exploitRecords", exploitHistory.size());
        summary.put("violations", violationHistory.size());
        return summary;
    }
    
    // Pattern analysis methods
    public List<String> getRecentPatterns() {
        List<String> patterns = new ArrayList<>();
        
        // Analyze movement patterns
        if (movementStats.hasUnusualPatterns()) {
            patterns.add("unusual_movement");
        }
        
        // Analyze combat patterns
        if (combatStats.hasUnusualPatterns()) {
            patterns.add("unusual_combat");
        }
        
        // Analyze building patterns
        if (buildingStats.hasUnusualPatterns()) {
            patterns.add("unusual_building");
        }
        
        return patterns;
    }
}
