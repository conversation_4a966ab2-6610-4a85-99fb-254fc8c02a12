package ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.color;

import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.nbt.NBT;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.nbt.NBTFloat;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.nbt.NBTInt;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.nbt.NBTList;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.nbt.NBTNumber;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.nbt.NBTType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.ClientVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.util.MathUtil;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.PacketWrapper;
import ac.grim.grimac.shaded.jetbrains.annotations.NotNull;
import ac.grim.grimac.shaded.jetbrains.annotations.Range;
import ac.grim.grimac.shaded.kyori.adventure.util.RGBLike;

public class Color implements RGBLike {
   protected static final int BIT_MASK = 255;
   protected final int red;
   protected final int green;
   protected final int blue;

   public Color(@Range(from = 0L,to = 255L) int red, @Range(from = 0L,to = 255L) int green, @Range(from = 0L,to = 255L) int blue) {
      this.red = red;
      this.green = green;
      this.blue = blue;
   }

   public Color(@Range(from = 0L,to = 1L) float red, @Range(from = 0L,to = 1L) float green, @Range(from = 0L,to = 1L) float blue) {
      this(MathUtil.floor(red * 255.0F), MathUtil.floor(green * 255.0F), MathUtil.floor(blue * 255.0F));
   }

   public Color(int rgb) {
      this(rgb >> 16 & 255, rgb >> 8 & 255, rgb & 255);
   }

   public static Color read(PacketWrapper<?> wrapper) {
      return new Color(wrapper.readInt());
   }

   public static void write(PacketWrapper<?> wrapper, Color color) {
      wrapper.writeInt(color.asRGB());
   }

   public static Color decode(NBT nbt, ClientVersion version) {
      if (nbt instanceof NBTNumber) {
         return new Color(((NBTNumber)nbt).getAsInt());
      } else {
         NBTList<?> list = (NBTList)nbt;
         float red = ((NBTNumber)list.getTag(0)).getAsFloat();
         float green = ((NBTNumber)list.getTag(1)).getAsFloat();
         float blue = ((NBTNumber)list.getTag(2)).getAsFloat();
         return new Color(red, green, blue);
      }
   }

   public static NBT encode(Color color, ClientVersion version) {
      if (version.isNewerThanOrEquals(ClientVersion.V_1_21_2)) {
         return new NBTInt(color.asRGB());
      } else {
         NBTList<NBTFloat> list = new NBTList(NBTType.FLOAT, 3);
         list.addTag(new NBTFloat((float)color.red));
         list.addTag(new NBTFloat((float)color.green));
         list.addTag(new NBTFloat((float)color.blue));
         return list;
      }
   }

   @NotNull
   public Color withRed(@Range(from = 0L,to = 255L) int red) {
      return new Color(red, this.green, this.blue);
   }

   @NotNull
   public Color withGreen(@Range(from = 0L,to = 255L) int green) {
      return new Color(this.red, green, this.blue);
   }

   @NotNull
   public Color withBlue(@Range(from = 0L,to = 255L) int blue) {
      return new Color(this.red, this.green, blue);
   }

   public int asRGB() {
      return this.red << 16 | this.green << 8 | this.blue;
   }

   @Range(
      from = 0L,
      to = 255L
   )
   public int red() {
      return this.red;
   }

   @Range(
      from = 0L,
      to = 255L
   )
   public int green() {
      return this.green;
   }

   @Range(
      from = 0L,
      to = 255L
   )
   public int blue() {
      return this.blue;
   }
}
