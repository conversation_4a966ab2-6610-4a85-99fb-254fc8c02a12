package ac.grim.grimac.shaded.com.github.retrooper.packetevents.netty.buffer;

public interface ByteBufAllocationOperator {
   Object wrappedBuffer(byte[] var1);

   Object copiedBuffer(byte[] var1);

   Object buffer();

   Object buffer(int var1);

   Object directBuffer();

   Object directBuffer(int var1);

   Object compositeBuffer();

   Object compositeBuffer(int var1);

   Object emptyBuffer();
}
