package ac.grim.grimac.checks.impl.sprint;

import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.CheckData;
import ac.grim.grimac.checks.type.PostPredictionCheck;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketReceiveEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.packettype.PacketType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.ClientVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientEntityAction;
import ac.grim.grimac.utils.anticheat.update.PredictionComplete;

@CheckData(
   name = "SprintE",
   description = "Sprinting while colliding with a wall",
   setback = 5.0D,
   experimental = true
)
public class SprintE extends Check implements PostPredictionCheck {
   private boolean startedSprintingThisTick;
   private boolean wasHorizontalCollision;

   public SprintE(GrimPlayer player) {
      super(player);
   }

   public void onPacketReceive(PacketReceiveEvent event) {
      if (event.getPacketType() == PacketType.Play.Client.ENTITY_ACTION && (new WrapperPlayClientEntityAction(event)).getAction() == WrapperPlayClientEntityAction.Action.START_SPRINTING) {
         this.startedSprintingThisTick = true;
      }

   }

   public void onPredictionComplete(PredictionComplete predictionComplete) {
      if (predictionComplete.isChecked()) {
         if (!this.player.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_18)) {
            if (this.wasHorizontalCollision && !this.startedSprintingThisTick && (!this.player.wasTouchingWater || this.player.getClientVersion().isOlderThan(ClientVersion.V_1_13))) {
               if (this.player.isSprinting) {
                  this.flagAndAlertWithSetback();
               } else {
                  this.reward();
               }
            }

            this.wasHorizontalCollision = this.player.horizontalCollision;
            this.startedSprintingThisTick = false;
         }
      }
   }
}
