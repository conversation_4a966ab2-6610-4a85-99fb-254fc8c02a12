package ac.grim.grimac.manager.player.handlers;

import ac.grim.grimac.api.handler.ResyncHandler;
import ac.grim.grimac.events.packets.patch.ResyncWorldUtil;
import ac.grim.grimac.player.GrimPlayer;

public class BukkitResyncHandler implements ResyncHandler {
   private final GrimPlayer player;

   public void resync(int minBlockX, int minBlockY, int minBlockZ, int maxBlockX, int maxBlockY, int maxBlockZ) {
      ResyncWorldUtil.resyncPositions(this.player, minBlockX, minBlockY, minBlockZ, maxBlockX, maxBlockY, maxBlockZ);
   }

   public BukkitResyncHandler(GrimPlayer player) {
      this.player = player;
   }
}
