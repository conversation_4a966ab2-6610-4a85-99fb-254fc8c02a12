package ac.grim.grimac.manager.init.start;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.manager.init.Initable;
import ac.grim.grimac.shaded.io.github.retrooper.packetevents.util.folia.FoliaScheduler;
import ac.grim.grimac.utils.anticheat.LogUtil;
import org.bukkit.Bukkit;

public class TickRunner implements Initable {
   public void start() {
      LogUtil.info("Registering tick schedulers...");
      if (FoliaScheduler.isFolia()) {
         FoliaScheduler.getAsyncScheduler().runAtFixedRate(GrimAPI.INSTANCE.getPlugin(), (dummy) -> {
            GrimAPI.INSTANCE.getTickManager().tickSync();
            GrimAPI.INSTANCE.getTickManager().tickAsync();
         }, 1L, 1L);
      } else {
         Bukkit.getScheduler().runTaskTimer(GrimAPI.INSTANCE.getPlugin(), () -> {
            GrimAPI.INSTANCE.getTickManager().tickSync();
         }, 0L, 1L);
         Bukkit.getScheduler().runTaskTimerAsynchronously(GrimAPI.INSTANCE.getPlugin(), () -> {
            GrimAPI.INSTANCE.getTickManager().tickAsync();
         }, 0L, 1L);
      }

   }
}
