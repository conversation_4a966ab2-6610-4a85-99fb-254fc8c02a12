package ac.grim.grimac.commands;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.shaded.acf.BaseCommand;
import ac.grim.grimac.shaded.acf.annotation.CommandAlias;
import ac.grim.grimac.shaded.acf.annotation.CommandPermission;
import ac.grim.grimac.shaded.acf.annotation.Subcommand;
import ac.grim.grimac.shaded.kyori.adventure.text.Component;
import ac.grim.grimac.utils.anticheat.LogUtil;
import ac.grim.grimac.utils.anticheat.MessageUtil;
import java.util.Iterator;
import org.bukkit.entity.Player;

@CommandAlias("grim|grimac")
public class GrimSendAlert extends BaseCommand {
   @Subcommand("sendalert")
   @CommandPermission("grim.sendalert")
   public void sendAlert(String string) {
      string = MessageUtil.replacePlaceholders((Object)null, (String)string);
      Component message = MessageUtil.miniMessage(string);
      Iterator var3 = GrimAPI.INSTANCE.getAlertManager().getEnabledAlerts().iterator();

      while(var3.hasNext()) {
         Player bukkitPlayer = (Player)var3.next();
         MessageUtil.sendMessage(bukkitPlayer, message);
      }

      if (GrimAPI.INSTANCE.getConfigManager().isPrintAlertsToConsole()) {
         LogUtil.console(message);
      }

   }
}
