package ac.grim.grimac.checks.impl.scaffolding;

import ac.grim.grimac.checks.CheckData;
import ac.grim.grimac.checks.type.BlockPlaceCheck;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.world.states.type.StateTypes;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.util.Vector3f;
import ac.grim.grimac.utils.anticheat.update.BlockPlace;
import ac.grim.grimac.utils.nmsutil.Materials;

@CheckData(
   name = "FabricatedPlace"
)
public class FabricatedPlace extends BlockPlaceCheck {
   public FabricatedPlace(GrimPlayer player) {
      super(player);
   }

   public void onBlockPlace(BlockPlace place) {
      Vector3f cursor = place.getCursor();
      if (cursor != null) {
         double allowed = !Materials.isShapeExceedsCube(place.getPlacedAgainstMaterial()) && place.getPlacedAgainstMaterial() != StateTypes.LECTERN ? 1.0D : 1.5D;
         double minAllowed = 1.0D - allowed;
         if (((double)cursor.getX() < minAllowed || (double)cursor.getY() < minAllowed || (double)cursor.getZ() < minAllowed || (double)cursor.getX() > allowed || (double)cursor.getY() > allowed || (double)cursor.getZ() > allowed) && this.flagAndAlert() && this.shouldModifyPackets() && this.shouldCancel()) {
            place.resync();
         }

      }
   }
}
