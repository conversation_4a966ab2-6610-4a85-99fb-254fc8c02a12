package ac.grim.grimac.commands;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.shaded.acf.BaseCommand;
import ac.grim.grimac.shaded.acf.annotation.CommandAlias;
import ac.grim.grimac.shaded.acf.annotation.CommandPermission;
import ac.grim.grimac.shaded.acf.annotation.Subcommand;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.PacketEvents;
import ac.grim.grimac.shaded.io.github.retrooper.packetevents.util.folia.FoliaScheduler;
import ac.grim.grimac.shaded.io.github.retrooper.packetevents.util.viaversion.ViaVersionUtil;
import ac.grim.grimac.utils.anticheat.MessageUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import org.bukkit.Bukkit;
import org.bukkit.command.CommandSender;
import org.bukkit.plugin.Plugin;

@CommandAlias("grim|grimac")
public class GrimDump extends BaseCommand {
   private String link = null;
   private final Gson gson = (new GsonBuilder()).setPrettyPrinting().create();
   private static final boolean PAPER = hasClass("com.destroystokyo.paper.PaperConfig") || hasClass("io.papermc.paper.configuration.Configuration");

   @Subcommand("dump")
   @CommandPermission("grim.dump")
   public void onCommand(CommandSender sender) {
      if (this.link != null) {
         MessageUtil.sendMessage(sender, MessageUtil.miniMessage(GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("upload-log", "%prefix% &fUploaded debug to: %url%").replace("%url%", this.link)));
      } else {
         GrimLog.sendLogAsync(sender, this.generateDump(), (string) -> {
            this.link = string;
         }, "text/yaml");
      }
   }

   private String generateDump() {
      JsonObject base = new JsonObject();
      base.addProperty("type", "dump");
      base.addProperty("timestamp", System.currentTimeMillis());
      JsonObject versions = new JsonObject();
      base.add("versions", versions);
      versions.addProperty("grim", GrimAPI.INSTANCE.getExternalAPI().getGrimVersion());
      versions.addProperty("packetevents", PacketEvents.getAPI().getVersion().toString());
      versions.addProperty("server", PacketEvents.getAPI().getServerManager().getVersion().getReleaseName());
      versions.addProperty("implementation", Bukkit.getVersion());
      JsonArray properties = new JsonArray();
      base.add("properties", properties);
      if (PAPER) {
         properties.add("paper");
      }

      if (FoliaScheduler.isFolia()) {
         properties.add("folia");
      }

      if (ViaVersionUtil.isAvailable()) {
         properties.add("viaversion");
      }

      JsonObject system = new JsonObject();
      base.add("system", system);
      system.addProperty("os", System.getProperty("os.name"));
      system.addProperty("java", System.getProperty("java.version"));
      JsonArray plugins = new JsonArray();
      base.add("plugins", plugins);
      Plugin[] var6 = Bukkit.getPluginManager().getPlugins();
      int var7 = var6.length;

      for(int var8 = 0; var8 < var7; ++var8) {
         Plugin plugin = var6[var8];
         JsonObject pluginJson = new JsonObject();
         pluginJson.addProperty("enabled", plugin.isEnabled());
         pluginJson.addProperty("name", plugin.getName());
         pluginJson.addProperty("version", plugin.getDescription().getVersion());
         plugins.add(pluginJson);
      }

      return this.gson.toJson(base);
   }

   private static boolean hasClass(String className) {
      try {
         Class.forName(className);
         return true;
      } catch (ClassNotFoundException var2) {
         return false;
      }
   }
}
