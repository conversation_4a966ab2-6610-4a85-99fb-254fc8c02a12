package ac.grim.grimac.commands;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.acf.BaseCommand;
import ac.grim.grimac.shaded.acf.annotation.CommandAlias;
import ac.grim.grimac.shaded.acf.annotation.CommandCompletion;
import ac.grim.grimac.shaded.acf.annotation.CommandPermission;
import ac.grim.grimac.shaded.acf.annotation.Optional;
import ac.grim.grimac.shaded.acf.annotation.Subcommand;
import ac.grim.grimac.shaded.acf.bukkit.contexts.OnlinePlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.PacketEvents;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.User;
import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.ConsoleCommandSender;
import org.bukkit.entity.Player;

@CommandAlias("grim|grimac")
public class GrimDebug extends BaseCommand {
   @Subcommand("debug")
   @CommandPermission("grim.debug")
   @CommandCompletion("@players")
   public void onDebug(CommandSender sender, @Optional OnlinePlayer target) {
      Player player = null;
      if (sender instanceof Player) {
         player = (Player)sender;
      }

      GrimPlayer grimPlayer = this.parseTarget(sender, player, target);
      if (grimPlayer != null) {
         if (sender instanceof ConsoleCommandSender) {
            grimPlayer.checkManager.getDebugHandler().toggleConsoleOutput();
         } else {
            grimPlayer.checkManager.getDebugHandler().toggleListener(player);
         }

      }
   }

   private GrimPlayer parseTarget(CommandSender sender, Player player, OnlinePlayer target) {
      Player targetPlayer = target == null ? player : target.getPlayer();
      if (player == null && target == null) {
         sender.sendMessage(String.valueOf(ChatColor.RED) + "You must specify a target as the console!");
         return null;
      } else {
         GrimPlayer grimPlayer = GrimAPI.INSTANCE.getPlayerDataManager().getPlayer(targetPlayer);
         if (grimPlayer == null) {
            User user = PacketEvents.getAPI().getPlayerManager().getUser(targetPlayer);
            sender.sendMessage(String.valueOf(ChatColor.RED) + "This player is exempt from all checks!");
            if (user == null) {
               sender.sendMessage(String.valueOf(ChatColor.RED) + "Unknown PacketEvents user");
            } else {
               boolean isExempt = GrimAPI.INSTANCE.getPlayerDataManager().shouldCheck(user);
               if (!isExempt) {
                  String var10001 = String.valueOf(ChatColor.RED);
                  sender.sendMessage(var10001 + "User connection state: " + String.valueOf(user.getConnectionState()));
               }
            }
         }

         return grimPlayer;
      }
   }

   @Subcommand("consoledebug")
   @CommandPermission("grim.consoledebug")
   @CommandCompletion("@players")
   public void onConsoleDebug(CommandSender sender, @Optional OnlinePlayer target) {
      Player player = null;
      if (sender instanceof Player) {
         player = (Player)sender;
      }

      GrimPlayer grimPlayer = this.parseTarget(sender, player, target);
      if (grimPlayer != null) {
         boolean isOutput = grimPlayer.checkManager.getDebugHandler().toggleConsoleOutput();
         String var10001 = grimPlayer.bukkitPlayer == null ? grimPlayer.user.getProfile().getName() : grimPlayer.bukkitPlayer.getName();
         sender.sendMessage("Console output for " + var10001 + " is now " + isOutput);
      }
   }
}
