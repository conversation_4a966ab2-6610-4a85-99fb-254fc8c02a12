package ac.grim.grimac.checks.impl.combat;

import ac.grim.grimac.api.config.ConfigManager;
import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.CheckData;
import ac.grim.grimac.checks.type.PacketCheck;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketReceiveEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.attribute.Attributes;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.entity.type.EntityType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.entity.type.EntityTypes;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.packettype.PacketType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.ClientVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.GameMode;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.util.Vector3d;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientInteractEntity;
import ac.grim.grimac.shaded.fastutil.ints.Int2ObjectMap;
import ac.grim.grimac.shaded.fastutil.ints.Int2ObjectOpenHashMap;
import ac.grim.grimac.shaded.fastutil.objects.ObjectIterator;
import ac.grim.grimac.shaded.jetbrains.annotations.NotNull;
import ac.grim.grimac.utils.collisions.datatypes.SimpleCollisionBox;
import ac.grim.grimac.utils.data.packetentity.PacketEntity;
import ac.grim.grimac.utils.data.packetentity.dragon.PacketEntityEnderDragonPart;
import ac.grim.grimac.utils.nmsutil.ReachUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import org.bukkit.util.Vector;

@CheckData(
   name = "Reach",
   setback = 10.0D
)
public class Reach extends Check implements PacketCheck {
   private final Int2ObjectMap<Vector3d> playerAttackQueue = new Int2ObjectOpenHashMap();
   private static final List<EntityType> blacklisted;
   private boolean cancelImpossibleHits;
   private double threshold;
   private double cancelBuffer;
   private static final Reach.CheckResult NONE;

   public Reach(GrimPlayer player) {
      super(player);
   }

   public void onPacketReceive(PacketReceiveEvent event) {
      if (!this.player.disableGrim && event.getPacketType() == PacketType.Play.Client.INTERACT_ENTITY) {
         WrapperPlayClientInteractEntity action = new WrapperPlayClientInteractEntity(event);
         if (this.player.getSetbackTeleportUtil().shouldBlockMovement()) {
            event.setCancelled(true);
            this.player.onPacketCancel();
            return;
         }

         PacketEntity entity = (PacketEntity)this.player.compensatedEntities.entityMap.get(action.getEntityId());
         if (entity == null || entity instanceof PacketEntityEnderDragonPart) {
            if (this.shouldModifyPackets() && this.player.compensatedEntities.serverPositionsMap.containsKey(action.getEntityId())) {
               event.setCancelled(true);
               this.player.onPacketCancel();
            }

            return;
         }

         if (entity.isDead) {
            return;
         }

         if (entity.getType() == EntityTypes.ARMOR_STAND && this.player.getClientVersion().isOlderThan(ClientVersion.V_1_8)) {
            return;
         }

         if (this.player.gamemode == GameMode.CREATIVE || this.player.gamemode == GameMode.SPECTATOR) {
            return;
         }

         if (this.player.inVehicle()) {
            return;
         }

         if (entity.riding != null) {
            return;
         }

         boolean tooManyAttacks = this.playerAttackQueue.size() > 10;
         if (!tooManyAttacks) {
            this.playerAttackQueue.put(action.getEntityId(), new Vector3d(this.player.x, this.player.y, this.player.z));
         }

         boolean knownInvalid = this.isKnownInvalid(entity);
         if (this.shouldModifyPackets() && this.cancelImpossibleHits && knownInvalid || tooManyAttacks) {
            event.setCancelled(true);
            this.player.onPacketCancel();
         }
      }

      if (this.isUpdate(event.getPacketType())) {
         this.tickBetterReachCheckWithAngle();
      }

   }

   private boolean isKnownInvalid(PacketEntity reachEntity) {
      if ((blacklisted.contains(reachEntity.getType()) || !reachEntity.isLivingEntity()) && reachEntity.getType() != EntityTypes.END_CRYSTAL) {
         return false;
      } else if (this.player.gamemode != GameMode.CREATIVE && this.player.gamemode != GameMode.SPECTATOR) {
         if (this.player.inVehicle()) {
            return false;
         } else if (this.cancelBuffer != 0.0D) {
            Reach.CheckResult result = this.checkReach(reachEntity, new Vector3d(this.player.x, this.player.y, this.player.z), true);
            return result.isFlag();
         } else {
            SimpleCollisionBox targetBox = reachEntity.getPossibleCollisionBoxes();
            if (reachEntity.getType() == EntityTypes.END_CRYSTAL) {
               targetBox = new SimpleCollisionBox(reachEntity.trackedServerPosition.getPos().subtract(1.0D, 0.0D, 1.0D), reachEntity.trackedServerPosition.getPos().add(1.0D, 2.0D, 1.0D));
            }

            return ReachUtils.getMinReachToBox(this.player, targetBox) > this.player.compensatedEntities.self.getAttributeValue(Attributes.ENTITY_INTERACTION_RANGE);
         }
      } else {
         return false;
      }
   }

   private void tickBetterReachCheckWithAngle() {
      ObjectIterator var1 = this.playerAttackQueue.int2ObjectEntrySet().iterator();

      while(var1.hasNext()) {
         Int2ObjectMap.Entry<Vector3d> attack = (Int2ObjectMap.Entry)var1.next();
         PacketEntity reachEntity = (PacketEntity)this.player.compensatedEntities.entityMap.get(attack.getIntKey());
         if (reachEntity != null) {
            Reach.CheckResult result = this.checkReach(reachEntity, (Vector3d)attack.getValue(), false);
            String var10001;
            String added;
            switch(result.type().ordinal()) {
            case 0:
               added = reachEntity.getType() == EntityTypes.PLAYER ? "" : ", type=" + reachEntity.getType().getName().getKey();
               var10001 = result.verbose();
               this.flagAndAlert(var10001 + added);
               break;
            case 1:
               added = reachEntity.getType() == EntityTypes.PLAYER ? "" : "type=" + reachEntity.getType().getName().getKey();
               Hitboxes var10000 = (Hitboxes)this.player.checkManager.getPacketCheck(Hitboxes.class);
               var10001 = result.verbose();
               var10000.flagAndAlert(var10001 + added);
            }
         }
      }

      this.playerAttackQueue.clear();
   }

   @NotNull
   private Reach.CheckResult checkReach(PacketEntity reachEntity, Vector3d from, boolean isPrediction) {
      SimpleCollisionBox targetBox = reachEntity.getPossibleCollisionBoxes();
      if (reachEntity.getType() == EntityTypes.END_CRYSTAL) {
         targetBox = new SimpleCollisionBox(reachEntity.trackedServerPosition.getPos().subtract(1.0D, 0.0D, 1.0D), reachEntity.trackedServerPosition.getPos().add(1.0D, 2.0D, 1.0D));
      }

      if (this.player.getClientVersion().isOlderThan(ClientVersion.V_1_9)) {
         targetBox.expand(0.10000000149011612D);
      }

      targetBox.expand(this.threshold);
      if (!this.player.packetStateData.didLastLastMovementIncludePosition || this.player.canSkipTicks()) {
         targetBox.expand(this.player.getMovementThreshold());
      }

      double minDistance = Double.MAX_VALUE;
      List<Vector> possibleLookDirs = new ArrayList(Collections.singletonList(ReachUtils.getLook(this.player, this.player.xRot, this.player.yRot)));
      if (!isPrediction) {
         ((List)possibleLookDirs).add(ReachUtils.getLook(this.player, this.player.lastXRot, this.player.yRot));
         if (this.player.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_9)) {
            ((List)possibleLookDirs).add(ReachUtils.getLook(this.player, this.player.lastXRot, this.player.lastYRot));
         }

         if (this.player.getClientVersion().isOlderThan(ClientVersion.V_1_8)) {
            possibleLookDirs = Collections.singletonList(ReachUtils.getLook(this.player, this.player.xRot, this.player.yRot));
         }
      }

      double maxReach = this.player.compensatedEntities.self.getAttributeValue(Attributes.ENTITY_INTERACTION_RANGE);
      double distance = maxReach + 3.0D;
      double[] possibleEyeHeights = this.player.getPossibleEyeHeights();
      Vector eyePos = new Vector(from.getX(), 0.0D, from.getZ());
      Iterator var14 = ((List)possibleLookDirs).iterator();

      while(true) {
         while(var14.hasNext()) {
            Vector lookVec = (Vector)var14.next();
            double[] var16 = possibleEyeHeights;
            int var17 = possibleEyeHeights.length;

            for(int var18 = 0; var18 < var17; ++var18) {
               double eye = var16[var18];
               eyePos.setY(from.getY() + eye);
               Vector endReachPos = eyePos.clone().add(new Vector(lookVec.getX() * distance, lookVec.getY() * distance, lookVec.getZ() * distance));
               Vector intercept = (Vector)ReachUtils.calculateIntercept(targetBox, eyePos, endReachPos).first();
               if (ReachUtils.isVecInside(targetBox, eyePos)) {
                  minDistance = 0.0D;
                  break;
               }

               if (intercept != null) {
                  minDistance = Math.min(eyePos.distance(intercept), minDistance);
               }
            }
         }

         if (!blacklisted.contains(reachEntity.getType()) && reachEntity.isLivingEntity() || reachEntity.getType() == EntityTypes.END_CRYSTAL) {
            if (minDistance == Double.MAX_VALUE) {
               this.cancelBuffer = 1.0D;
               return new Reach.CheckResult(Reach.ResultType.HITBOX, "");
            }

            if (minDistance > maxReach) {
               this.cancelBuffer = 1.0D;
               Reach.ResultType var10002 = Reach.ResultType.REACH;
               Object[] var10004 = new Object[]{minDistance};
               return new Reach.CheckResult(var10002, String.format("%.5f", var10004) + " blocks");
            }

            this.cancelBuffer = Math.max(0.0D, this.cancelBuffer - 0.25D);
         }

         return NONE;
      }
   }

   public void onReload(ConfigManager config) {
      this.cancelImpossibleHits = config.getBooleanElse("Reach.block-impossible-hits", true);
      this.threshold = config.getDoubleElse("Reach.threshold", 5.0E-4D);
   }

   static {
      blacklisted = Arrays.asList(EntityTypes.BOAT, EntityTypes.CHEST_BOAT, EntityTypes.SHULKER);
      NONE = new Reach.CheckResult(Reach.ResultType.NONE, "");
   }

   private static record CheckResult(Reach.ResultType type, String verbose) {
      private CheckResult(Reach.ResultType type, String verbose) {
         this.type = type;
         this.verbose = verbose;
      }

      public boolean isFlag() {
         return this.type != Reach.ResultType.NONE;
      }

      public Reach.ResultType type() {
         return this.type;
      }

      public String verbose() {
         return this.verbose;
      }
   }

   private static enum ResultType {
      REACH,
      HITBOX,
      NONE;

      // $FF: synthetic method
      private static Reach.ResultType[] $values() {
         return new Reach.ResultType[]{REACH, HITBOX, NONE};
      }
   }
}
