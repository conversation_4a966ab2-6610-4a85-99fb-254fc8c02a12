package ac.grim.grimac.commands;

import ac.grim.grimac.predictionengine.MovementCheckRunner;
import ac.grim.grimac.shaded.acf.BaseCommand;
import ac.grim.grimac.shaded.acf.annotation.CommandAlias;
import ac.grim.grimac.shaded.acf.annotation.CommandPermission;
import ac.grim.grimac.shaded.acf.annotation.Subcommand;
import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;

@CommandAlias("grim|grimac")
public class GrimPerf extends BaseCommand {
   @Subcommand("perf|performance")
   @CommandPermission("grim.performance")
   public void onPerformance(CommandSender sender) {
      double millis = MovementCheckRunner.predictionNanos / 1000000.0D;
      double longMillis = MovementCheckRunner.longPredictionNanos / 1000000.0D;
      String var10001 = String.valueOf(ChatColor.GRAY);
      sender.sendMessage(var10001 + "Milliseconds per prediction (avg. 500): " + String.valueOf(ChatColor.WHITE) + millis);
      var10001 = String.valueOf(ChatColor.GRAY);
      sender.sendMessage(var10001 + "Milliseconds per prediction (avg. 20k): " + String.valueOf(ChatColor.WHITE) + longMillis);
   }
}
