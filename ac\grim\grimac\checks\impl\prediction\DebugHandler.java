package ac.grim.grimac.checks.impl.prediction;

import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.type.PostPredictionCheck;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.utils.anticheat.LogUtil;
import ac.grim.grimac.utils.anticheat.update.PredictionComplete;
import ac.grim.grimac.utils.lists.EvictingQueue;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

public class DebugHandler extends Check implements PostPredictionCheck {
   Set<Player> listeners = new CopyOnWriteArraySet(new HashSet());
   boolean outputToConsole = false;
   boolean enabledFlags = false;
   boolean lastMovementIsFlag = false;
   EvictingQueue<String> predicted = new EvictingQueue(5);
   EvictingQueue<String> actually = new EvictingQueue(5);
   EvictingQueue<String> offset = new EvictingQueue(5);

   public DebugHandler(GrimPlayer player) {
      super(player);
   }

   public void onPredictionComplete(PredictionComplete predictionComplete) {
      if (predictionComplete.isChecked()) {
         double offset = predictionComplete.getOffset();
         if (!this.listeners.isEmpty() || this.outputToConsole) {
            if (this.player.predictedVelocity.vector.lengthSquared() != 0.0D || offset != 0.0D) {
               ChatColor color = this.pickColor(offset, offset);
               Vector predicted = this.player.predictedVelocity.vector;
               Vector actually = this.player.actualMovement;
               ChatColor xColor = this.pickColor(Math.abs(predicted.getX() - actually.getX()), offset);
               ChatColor yColor = this.pickColor(Math.abs(predicted.getY() - actually.getY()), offset);
               ChatColor zColor = this.pickColor(Math.abs(predicted.getZ() - actually.getZ()), offset);
               String var10000 = String.valueOf(color);
               String p = var10000 + "P: " + String.valueOf(xColor) + predicted.getX() + " " + String.valueOf(yColor) + predicted.getY() + " " + String.valueOf(zColor) + predicted.getZ();
               var10000 = String.valueOf(color);
               String a = var10000 + "A: " + String.valueOf(xColor) + actually.getX() + " " + String.valueOf(yColor) + actually.getY() + " " + String.valueOf(zColor) + actually.getZ();
               String canSkipTick = (this.player.couldSkipTick + " ").substring(0, 1);
               String actualMovementSkip = (this.player.skippedTickInActualMovement + " ").substring(0, 1);
               var10000 = String.valueOf(ChatColor.GRAY);
               String o = var10000 + canSkipTick + "→0.03→" + actualMovementSkip + String.valueOf(color) + " O: " + offset;
               String prefix = this.player.bukkitPlayer == null ? "null" : this.player.bukkitPlayer.getName() + " ";
               boolean thisFlag = color != ChatColor.GRAY && color != ChatColor.GREEN;
               if (this.enabledFlags) {
                  if (this.lastMovementIsFlag) {
                     this.predicted.clear();
                     this.actually.clear();
                     this.offset.clear();
                  }

                  this.predicted.add(p);
                  this.actually.add(a);
                  this.offset.add(o);
                  this.lastMovementIsFlag = thisFlag;
               }

               if (thisFlag) {
                  for(int i = 0; i < this.predicted.size(); ++i) {
                     this.player.user.sendMessage((String)this.predicted.get(i));
                     this.player.user.sendMessage((String)this.actually.get(i));
                     this.player.user.sendMessage((String)this.offset.get(i));
                  }
               }

               Iterator var19 = this.listeners.iterator();

               while(var19.hasNext()) {
                  Player player = (Player)var19.next();
                  String var10001 = player == this.getPlayer().bukkitPlayer ? "" : prefix;
                  player.sendMessage(var10001 + p);
                  var10001 = player == this.getPlayer().bukkitPlayer ? "" : prefix;
                  player.sendMessage(var10001 + a);
                  var10001 = player == this.getPlayer().bukkitPlayer ? "" : prefix;
                  player.sendMessage(var10001 + o);
               }

               this.listeners.removeIf((playerx) -> {
                  return !playerx.isOnline();
               });
               if (this.outputToConsole) {
                  LogUtil.info(prefix + p);
                  LogUtil.info(prefix + a);
                  LogUtil.info(prefix + o);
               }

            }
         }
      }
   }

   private ChatColor pickColor(double offset, double totalOffset) {
      if (this.player.getSetbackTeleportUtil().blockOffsets) {
         return ChatColor.GRAY;
      } else if (!(offset <= 0.0D) && !(totalOffset <= 0.0D)) {
         if (offset < 1.0E-4D) {
            return ChatColor.GREEN;
         } else {
            return offset < 0.01D ? ChatColor.YELLOW : ChatColor.RED;
         }
      } else {
         return ChatColor.GRAY;
      }
   }

   public void toggleListener(Player player) {
      if (!this.listeners.remove(player)) {
         this.listeners.add(player);
      }

   }

   public boolean toggleConsoleOutput() {
      this.outputToConsole = !this.outputToConsole;
      return this.outputToConsole;
   }
}
