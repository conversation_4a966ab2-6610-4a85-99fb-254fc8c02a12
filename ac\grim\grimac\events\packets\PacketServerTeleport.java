package ac.grim.grimac.events.packets;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.PacketEvents;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketListenerAbstract;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketListenerPriority;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketSendEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.manager.server.ServerVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.packettype.PacketType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.ClientVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.teleport.RelativeFlag;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.util.Vector3d;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerPlayerPositionAndLook;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerVehicleMove;
import ac.grim.grimac.utils.data.Pair;
import java.util.List;
import java.util.Objects;
import org.bukkit.Location;
import org.bukkit.World;

public class PacketServerTeleport extends PacketListenerAbstract {
   public PacketServerTeleport() {
      super(PacketListenerPriority.LOW);
   }

   public void onPacketSend(PacketSendEvent event) {
      List var10000;
      GrimPlayer player;
      if (event.getPacketType() == PacketType.Play.Server.PLAYER_POSITION_AND_LOOK) {
         WrapperPlayServerPlayerPositionAndLook teleport = new WrapperPlayServerPlayerPositionAndLook(event);
         player = GrimAPI.INSTANCE.getPlayerDataManager().getPlayer(event.getUser());
         Vector3d pos = new Vector3d(teleport.getX(), teleport.getY(), teleport.getZ());
         if (player == null) {
            return;
         }

         if (player.getSetbackTeleportUtil().getRequiredSetBack() == null) {
            player.x = teleport.getX();
            player.y = teleport.getY();
            player.z = teleport.getZ();
            player.xRot = teleport.getYaw();
            player.yRot = teleport.getPitch();
            player.lastX = teleport.getX();
            player.lastY = teleport.getY();
            player.lastZ = teleport.getZ();
            player.lastXRot = teleport.getYaw();
            player.lastYRot = teleport.getPitch();
            player.pollData();
         }

         if (player.getClientVersion().isOlderThanOrEquals(ClientVersion.V_1_8)) {
            if (teleport.isRelativeFlag(RelativeFlag.X)) {
               pos = pos.add(new Vector3d(player.x, 0.0D, 0.0D));
               teleport.setRelative(RelativeFlag.X, false);
            }

            if (teleport.isRelativeFlag(RelativeFlag.Y)) {
               pos = pos.add(new Vector3d(0.0D, player.y, 0.0D));
               teleport.setRelative(RelativeFlag.Y, false);
            }

            if (teleport.isRelativeFlag(RelativeFlag.Z)) {
               pos = pos.add(new Vector3d(0.0D, 0.0D, player.z));
               teleport.setRelative(RelativeFlag.Z, false);
            }

            teleport.setX(pos.getX());
            teleport.setY(pos.getY());
            teleport.setZ(pos.getZ());
         }

         player.sendTransaction();
         int lastTransactionSent = player.lastTransactionSent.get();
         var10000 = event.getTasksAfterSend();
         Objects.requireNonNull(player);
         var10000.add(player::sendTransaction);
         if (teleport.isDismountVehicle()) {
            event.getTasksAfterSend().add(() -> {
               player.compensatedEntities.self.eject();
            });
         }

         if (PacketEvents.getAPI().getServerManager().getVersion().isOlderThan(ServerVersion.V_1_8)) {
            pos = pos.withY(pos.getY() - 1.62D);
         }

         Location target = new Location((World)null, pos.getX(), pos.getY(), pos.getZ());
         player.getSetbackTeleportUtil().addSentTeleport(target, teleport.getDeltaMovement(), lastTransactionSent, teleport.getRelativeFlags(), true, teleport.getTeleportId());
      }

      if (event.getPacketType() == PacketType.Play.Server.VEHICLE_MOVE) {
         WrapperPlayServerVehicleMove vehicleMove = new WrapperPlayServerVehicleMove(event);
         player = GrimAPI.INSTANCE.getPlayerDataManager().getPlayer(event.getUser());
         if (player == null) {
            return;
         }

         player.sendTransaction();
         int lastTransactionSent = player.lastTransactionSent.get();
         Vector3d finalPos = vehicleMove.getPosition();
         var10000 = event.getTasksAfterSend();
         Objects.requireNonNull(player);
         var10000.add(player::sendTransaction);
         player.vehicleData.vehicleTeleports.add(new Pair(lastTransactionSent, finalPos));
      }

   }
}
