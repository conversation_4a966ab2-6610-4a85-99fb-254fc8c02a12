package ac.grim.grimac.predictionengine.movementtick;

import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.attribute.Attributes;
import ac.grim.grimac.utils.data.packetentity.PacketEntityRideable;
import org.bukkit.util.Vector;

public class MovementTickerPig extends MovementTickerRideable {
   public MovementTickerPig(GrimPlayer player) {
      super(player);
      this.movementInput = new Vector(0, 0, 1);
   }

   public float getSteeringSpeed() {
      PacketEntityRideable pig = (PacketEntityRideable)this.player.compensatedEntities.self.getRiding();
      return (float)pig.getAttributeValue(Attributes.MOVEMENT_SPEED) * 0.225F;
   }
}
