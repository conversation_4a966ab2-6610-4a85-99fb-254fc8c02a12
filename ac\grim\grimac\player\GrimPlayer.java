package ac.grim.grimac.player;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.api.AbstractCheck;
import ac.grim.grimac.api.GrimUser;
import ac.grim.grimac.api.config.ConfigManager;
import ac.grim.grimac.api.feature.FeatureManager;
import ac.grim.grimac.api.handler.ResyncHandler;
import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.impl.aim.processor.AimProcessor;
import ac.grim.grimac.checks.impl.misc.ClientBrand;
import ac.grim.grimac.checks.impl.misc.TransactionOrder;
import ac.grim.grimac.events.packets.CheckManagerListener;
import ac.grim.grimac.manager.ActionManager;
import ac.grim.grimac.manager.CheckManager;
import ac.grim.grimac.manager.LastInstanceManager;
import ac.grim.grimac.manager.PunishmentManager;
import ac.grim.grimac.manager.SetbackTeleportUtil;
import ac.grim.grimac.manager.player.features.FeatureManagerImpl;
import ac.grim.grimac.manager.player.handlers.BukkitResyncHandler;
import ac.grim.grimac.predictionengine.MovementCheckRunner;
import ac.grim.grimac.predictionengine.PointThreeEstimator;
import ac.grim.grimac.predictionengine.UncertaintyHandler;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.PacketEvents;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketSendEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.manager.server.ServerVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.netty.channel.ChannelHelper;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.ConnectionState;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.attribute.Attributes;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.component.ComponentTypes;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.component.builtin.item.ItemEquippable;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.entity.type.EntityTypes;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.item.ItemStack;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.item.type.ItemTypes;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.ClientVersion;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.EquipmentSlot;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.GameMode;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.User;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.world.BlockFace;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.world.dimension.DimensionType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.util.Vector3d;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.util.Vector3i;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.PacketWrapper;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerDisconnect;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerEntityTeleport;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerEntityVelocity;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerPing;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerWindowConfirmation;
import ac.grim.grimac.shaded.io.github.retrooper.packetevents.adventure.serializer.legacy.LegacyComponentSerializer;
import ac.grim.grimac.shaded.io.github.retrooper.packetevents.util.folia.FoliaScheduler;
import ac.grim.grimac.shaded.io.github.retrooper.packetevents.util.viaversion.ViaVersionUtil;
import ac.grim.grimac.shaded.jetbrains.annotations.Contract;
import ac.grim.grimac.shaded.jetbrains.annotations.Nullable;
import ac.grim.grimac.shaded.kyori.adventure.text.Component;
import ac.grim.grimac.shaded.kyori.adventure.text.TranslatableComponent;
import ac.grim.grimac.utils.anticheat.LogUtil;
import ac.grim.grimac.utils.anticheat.MessageUtil;
import ac.grim.grimac.utils.anticheat.update.BlockBreak;
import ac.grim.grimac.utils.change.PlayerBlockHistory;
import ac.grim.grimac.utils.collisions.datatypes.SimpleCollisionBox;
import ac.grim.grimac.utils.data.BlockPlaceSnapshot;
import ac.grim.grimac.utils.data.MainSupportingBlockData;
import ac.grim.grimac.utils.data.PacketStateData;
import ac.grim.grimac.utils.data.Pair;
import ac.grim.grimac.utils.data.TrackerData;
import ac.grim.grimac.utils.data.VectorData;
import ac.grim.grimac.utils.data.VehicleData;
import ac.grim.grimac.utils.data.VelocityData;
import ac.grim.grimac.utils.data.packetentity.PacketEntity;
import ac.grim.grimac.utils.data.packetentity.PacketEntitySelf;
import ac.grim.grimac.utils.data.tags.SyncedTags;
import ac.grim.grimac.utils.enums.FluidTag;
import ac.grim.grimac.utils.enums.Pose;
import ac.grim.grimac.utils.latency.CompensatedEntities;
import ac.grim.grimac.utils.latency.CompensatedFireworks;
import ac.grim.grimac.utils.latency.CompensatedInventory;
import ac.grim.grimac.utils.latency.CompensatedWorld;
import ac.grim.grimac.utils.latency.LatencyUtils;
import ac.grim.grimac.utils.math.GrimMath;
import ac.grim.grimac.utils.math.TrigHandler;
import ac.grim.grimac.utils.nmsutil.BlockProperties;
import ac.grim.grimac.utils.nmsutil.GetBoundingBox;
import com.viaversion.viaversion.api.Via;
import com.viaversion.viaversion.api.connection.UserConnection;
import com.viaversion.viaversion.api.protocol.packet.PacketTracker;
import io.netty.channel.Channel;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Objects;
import java.util.Optional;
import java.util.Queue;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

public class GrimPlayer implements GrimUser {
   public UUID uuid;
   public final User user;
   public int entityID;
   @Nullable
   public Player bukkitPlayer;
   public final Queue<Pair<Short, Long>> transactionsSent = new ConcurrentLinkedQueue();
   public final Set<Short> didWeSendThatTrans = ConcurrentHashMap.newKeySet();
   private final AtomicInteger transactionIDCounter = new AtomicInteger(0);
   public AtomicInteger lastTransactionSent = new AtomicInteger(0);
   public AtomicInteger lastTransactionReceived = new AtomicInteger(0);
   public CheckManager checkManager;
   public ActionManager actionManager;
   public PunishmentManager punishmentManager;
   public MovementCheckRunner movementCheckRunner;
   public SyncedTags tagManager;
   public Vector clientVelocity = new Vector();
   PacketTracker packetTracker;
   private long transactionPing = 0L;
   public long lastTransSent = 0L;
   public long lastTransReceived = 0L;
   private long playerClockAtLeast = System.nanoTime();
   public double lastWasClimbing = 0.0D;
   public boolean canSwimHop = false;
   public int riptideSpinAttackTicks = 0;
   public int powderSnowFrozenTicks = 0;
   public boolean hasGravity = true;
   public final long joinTime = System.currentTimeMillis();
   public boolean playerEntityHasGravity = true;
   public VectorData predictedVelocity;
   public Vector actualMovement;
   public Vector stuckSpeedMultiplier;
   public UncertaintyHandler uncertaintyHandler;
   public double gravity;
   public float friction;
   public double speed;
   public Vector3d filterMojangStupidityOnMojangStupidity;
   public double x;
   public double y;
   public double z;
   public double lastX;
   public double lastY;
   public double lastZ;
   public float xRot;
   public float yRot;
   public float lastXRot;
   public float lastYRot;
   public boolean onGround;
   public boolean lastOnGround;
   public boolean isSneaking;
   public boolean wasSneaking;
   public boolean isSprinting;
   public boolean lastSprinting;
   public boolean lastSprintingForSpeed;
   public boolean isFlying;
   public boolean canFly;
   public boolean wasFlying;
   public boolean isSwimming;
   public boolean wasSwimming;
   public boolean isClimbing;
   public boolean isGliding;
   public boolean wasGliding;
   public boolean isRiptidePose;
   public double fallDistance;
   public SimpleCollisionBox boundingBox;
   public Pose pose;
   public Pose lastPose;
   public boolean isSlowMovement;
   public boolean isInBed;
   public boolean lastInBed;
   public int food;
   public float depthStriderLevel;
   public float sneakingSpeedMultiplier;
   public float flySpeed;
   public VehicleData vehicleData;
   public boolean clientClaimsLastOnGround;
   public boolean wasTouchingWater;
   public boolean wasTouchingLava;
   public boolean slightlyTouchingLava;
   public boolean slightlyTouchingWater;
   public boolean wasEyeInWater;
   public FluidTag fluidOnEyes;
   public boolean horizontalCollision;
   public boolean verticalCollision;
   public boolean clientControlledVerticalCollision;
   public boolean couldSkipTick;
   public boolean skippedTickInActualMovement;
   public LastInstanceManager lastInstanceManager;
   public final CompensatedFireworks fireworks;
   public final CompensatedWorld compensatedWorld;
   public final CompensatedEntities compensatedEntities;
   public LatencyUtils latencyUtils;
   public PointThreeEstimator pointThreeEstimator;
   public TrigHandler trigHandler;
   public PacketStateData packetStateData;
   public Vector baseTickAddition;
   public Vector baseTickWaterPushing;
   public Vector startTickClientVel;
   public int movementPackets;
   public VelocityData firstBreadKB;
   public VelocityData likelyKB;
   public VelocityData firstBreadExplosion;
   public VelocityData likelyExplosions;
   public int minAttackSlow;
   public int maxAttackSlow;
   public GameMode gamemode;
   public DimensionType dimensionType;
   public Vector3d bedPosition;
   public long lastBlockPlaceUseItem;
   public long lastBlockBreak;
   public AtomicInteger cancelledPackets;
   public MainSupportingBlockData mainSupportingBlockData;
   public double[][] possibleEyeHeights;
   public int totalFlyingPacketsSent;
   public Queue<BlockPlaceSnapshot> placeUseItemPackets;
   public Queue<BlockBreak> queuedBreaks;
   public PlayerBlockHistory blockHistory;
   public boolean disableGrim;
   public boolean noModifyPacketPermission;
   public boolean noSetbackPermission;
   private boolean debugPacketCancel;
   private int spamThreshold;
   private int maxTransactionTime;
   private boolean ignoreDuplicatePacketRotation;
   private boolean experimentalChecks;
   private boolean cancelDuplicatePacket;
   private boolean exemptElytra;
   private boolean resetItemUsageOnAttack;
   private boolean resetItemUsageOnItemUpdate;
   private boolean resetItemUsageOnSlotChange;
   private final FeatureManagerImpl featureManager;
   private ResyncHandler resyncHandler;

   public GrimPlayer(User user) {
      this.predictedVelocity = new VectorData(new Vector(), VectorData.VectorType.Normal);
      this.actualMovement = new Vector();
      this.stuckSpeedMultiplier = new Vector(1, 1, 1);
      this.filterMojangStupidityOnMojangStupidity = new Vector3d();
      this.isRiptidePose = false;
      this.pose = Pose.STANDING;
      this.lastPose = Pose.STANDING;
      this.isSlowMovement = false;
      this.isInBed = false;
      this.lastInBed = false;
      this.food = 20;
      this.sneakingSpeedMultiplier = 0.3F;
      this.vehicleData = new VehicleData();
      this.wasTouchingWater = false;
      this.wasTouchingLava = false;
      this.slightlyTouchingLava = false;
      this.slightlyTouchingWater = false;
      this.wasEyeInWater = false;
      this.couldSkipTick = false;
      this.skippedTickInActualMovement = false;
      this.baseTickAddition = new Vector();
      this.baseTickWaterPushing = new Vector();
      this.startTickClientVel = new Vector();
      this.movementPackets = 0;
      this.firstBreadKB = null;
      this.likelyKB = null;
      this.firstBreadExplosion = null;
      this.likelyExplosions = null;
      this.minAttackSlow = 0;
      this.maxAttackSlow = 0;
      this.lastBlockPlaceUseItem = 0L;
      this.lastBlockBreak = 0L;
      this.cancelledPackets = new AtomicInteger(0);
      this.mainSupportingBlockData = new MainSupportingBlockData((Vector3i)null, false);
      this.possibleEyeHeights = new double[3][];
      this.placeUseItemPackets = new LinkedBlockingQueue();
      this.queuedBreaks = new LinkedBlockingQueue();
      this.blockHistory = new PlayerBlockHistory();
      this.disableGrim = false;
      this.noModifyPacketPermission = false;
      this.noSetbackPermission = false;
      this.debugPacketCancel = false;
      this.spamThreshold = 100;
      this.maxTransactionTime = 60;
      this.ignoreDuplicatePacketRotation = false;
      this.experimentalChecks = false;
      this.cancelDuplicatePacket = true;
      this.exemptElytra = false;
      this.featureManager = new FeatureManagerImpl(this);
      this.resyncHandler = new BukkitResyncHandler(this);
      this.user = user;
      this.uuid = user.getUUID();
      this.boundingBox = GetBoundingBox.getBoundingBoxFromPosAndSizeRaw(this.x, this.y, this.z, 0.6F, 1.8F);
      this.fireworks = new CompensatedFireworks(this);
      this.lastInstanceManager = new LastInstanceManager(this);
      this.actionManager = new ActionManager(this);
      this.checkManager = new CheckManager(this);
      this.punishmentManager = new PunishmentManager(this);
      this.tagManager = new SyncedTags(this);
      this.movementCheckRunner = new MovementCheckRunner(this);
      this.compensatedWorld = new CompensatedWorld(this);
      this.compensatedEntities = new CompensatedEntities(this);
      this.latencyUtils = new LatencyUtils(this);
      this.trigHandler = new TrigHandler(this);
      this.uncertaintyHandler = new UncertaintyHandler(this);
      this.pointThreeEstimator = new PointThreeEstimator(this);
      this.packetStateData = new PacketStateData();
      this.uncertaintyHandler.riptideEntities.add(0);
      this.uncertaintyHandler.collidingEntities.add(0);
      if (this.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_14)) {
         float scale = (float)this.compensatedEntities.self.getAttributeValue(Attributes.SCALE);
         this.possibleEyeHeights[2] = new double[]{0.4D * (double)scale, 1.62D * (double)scale, 1.27D * (double)scale};
         this.possibleEyeHeights[1] = new double[]{1.27D * (double)scale, 1.62D * (double)scale, 0.4D * (double)scale};
         this.possibleEyeHeights[0] = new double[]{1.62D * (double)scale, 1.27D * (double)scale, 0.4D * (double)scale};
      } else if (this.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_9)) {
         this.possibleEyeHeights[2] = new double[]{0.4D, 1.62D, 1.54D};
         this.possibleEyeHeights[1] = new double[]{1.54D, 1.62D, 0.4D};
         this.possibleEyeHeights[0] = new double[]{1.62D, 1.54D, 0.4D};
      } else {
         this.possibleEyeHeights[1] = new double[]{1.5399999618530273D, 1.6200000047683716D};
         this.possibleEyeHeights[0] = new double[]{1.6200000047683716D, 1.5399999618530273D};
      }

      this.reload();
   }

   public void onPacketCancel() {
      if (this.spamThreshold != -1 && this.cancelledPackets.incrementAndGet() > this.spamThreshold) {
         String var10000 = this.getName();
         LogUtil.info("Disconnecting " + var10000 + " for spamming invalid packets, packets cancelled within a second " + String.valueOf(this.cancelledPackets));
         this.disconnect(MessageUtil.miniMessage(MessageUtil.replacePlaceholders(this, GrimAPI.INSTANCE.getConfigManager().getDisconnectClosed())));
         this.cancelledPackets.set(0);
         if (this.debugPacketCancel) {
            LogUtil.error("Stacktrace for onPacketCancel (debug-packet-cancel=true)", new Exception());
         }
      }

   }

   public Set<VectorData> getPossibleVelocities() {
      Set<VectorData> set = new HashSet();
      if (this.firstBreadKB != null) {
         set.add((new VectorData(this.firstBreadKB.vector.clone(), VectorData.VectorType.Knockback)).returnNewModified(VectorData.VectorType.FirstBreadKnockback));
      }

      if (this.likelyKB != null) {
         set.add(new VectorData(this.likelyKB.vector.clone(), VectorData.VectorType.Knockback));
      }

      set.addAll(this.getPossibleVelocitiesMinusKnockback());
      return set;
   }

   public Set<VectorData> getPossibleVelocitiesMinusKnockback() {
      Set<VectorData> possibleMovements = new HashSet();
      possibleMovements.add(new VectorData(this.clientVelocity, VectorData.VectorType.Normal));
      if (this.canSwimHop && !this.onGround) {
         possibleMovements.add(new VectorData(this.clientVelocity.clone().setY(0.3F), VectorData.VectorType.Swimhop));
      }

      if (this.riptideSpinAttackTicks >= 0 && (Integer)Collections.max(this.uncertaintyHandler.riptideEntities) > 0) {
         possibleMovements.add(new VectorData(this.clientVelocity.clone().multiply(-0.2D), VectorData.VectorType.Trident));
      }

      if (this.lastWasClimbing != 0.0D) {
         possibleMovements.add(new VectorData(this.clientVelocity.clone().setY(this.lastWasClimbing + this.baseTickAddition.getY()), VectorData.VectorType.Climbable));
      }

      Iterator var2 = (new HashSet(possibleMovements)).iterator();

      while(var2.hasNext()) {
         VectorData data = (VectorData)var2.next();
         Iterator var4 = this.uncertaintyHandler.slimePistonBounces.iterator();

         while(var4.hasNext()) {
            BlockFace direction = (BlockFace)var4.next();
            if (direction.getModX() != 0) {
               possibleMovements.add(data.returnNewModified(data.vector.clone().setX(direction.getModX()), VectorData.VectorType.SlimePistonBounce));
            } else if (direction.getModY() != 0) {
               possibleMovements.add(data.returnNewModified(data.vector.clone().setY(direction.getModY()), VectorData.VectorType.SlimePistonBounce));
            } else if (direction.getModZ() != 0) {
               possibleMovements.add(data.returnNewModified(data.vector.clone().setZ(direction.getModZ()), VectorData.VectorType.SlimePistonBounce));
            }
         }
      }

      return possibleMovements;
   }

   public boolean addTransactionResponse(short id) {
      Pair<Short, Long> data = null;
      boolean hasID = false;
      int skipped = 0;

      for(Iterator var5 = this.transactionsSent.iterator(); var5.hasNext(); ++skipped) {
         Pair<Short, Long> iterator = (Pair)var5.next();
         if ((Short)iterator.first() == id) {
            hasID = true;
            break;
         }
      }

      if (hasID) {
         if (this.packetTracker != null) {
            this.packetTracker.setIntervalPackets(this.packetTracker.getIntervalPackets() - 1L);
         }

         if (skipped > 0 && System.currentTimeMillis() - this.joinTime > 5000L) {
            ((TransactionOrder)this.checkManager.getPacketCheck(TransactionOrder.class)).flagAndAlert("skipped: " + skipped);
         }

         do {
            data = (Pair)this.transactionsSent.poll();
            if (data == null) {
               break;
            }

            this.lastTransactionReceived.incrementAndGet();
            this.lastTransReceived = System.currentTimeMillis();
            this.transactionPing = System.nanoTime() - (Long)data.second();
            this.playerClockAtLeast = (Long)data.second();
         } while((Short)data.first() != id);

         CheckManagerListener.handleQueuedPlaces(this, false, 0.0F, 0.0F, System.currentTimeMillis());
         CheckManagerListener.handleQueuedBreaks(this, false, 0.0F, 0.0F, System.currentTimeMillis());
         this.latencyUtils.handleNettySyncTransaction(this.lastTransactionReceived.get());
      }

      return data != null;
   }

   public void baseTickAddWaterPushing(Vector vector) {
      this.baseTickWaterPushing.add(vector);
   }

   public void baseTickAddVector(Vector vector) {
      this.clientVelocity.add(vector);
   }

   public void trackBaseTickAddition(Vector vector) {
      this.baseTickAddition.add(vector);
   }

   public float getMaxUpStep() {
      PacketEntitySelf self = this.compensatedEntities.self;
      PacketEntity riding = self.getRiding();
      if (riding == null) {
         return (float)self.getAttributeValue(Attributes.STEP_HEIGHT);
      } else {
         return riding.isBoat() ? 0.0F : (float)riding.getAttributeValue(Attributes.STEP_HEIGHT);
      }
   }

   public void sendTransaction() {
      this.sendTransaction(false);
   }

   public void sendTransaction(boolean async) {
      if (this.user.getEncoderState() == ConnectionState.PLAY) {
         if (!this.disableGrim || !((double)(System.nanoTime() - this.getPlayerClockAtLeast()) > 1.5E10D)) {
            this.lastTransSent = System.currentTimeMillis();
            short transactionID = (short)(-1 * (this.transactionIDCounter.getAndIncrement() & 32767));

            try {
               Object packet;
               if (PacketEvents.getAPI().getServerManager().getVersion().isNewerThanOrEquals(ServerVersion.V_1_17)) {
                  packet = new WrapperPlayServerPing(transactionID);
               } else {
                  packet = new WrapperPlayServerWindowConfirmation(0, transactionID, false);
               }

               if (async) {
                  ChannelHelper.runInEventLoop(this.user.getChannel(), () -> {
                     this.addTransactionSend(transactionID);
                     this.user.writePacket(packet);
                  });
               } else {
                  this.addTransactionSend(transactionID);
                  this.user.writePacket((PacketWrapper)packet);
               }
            } catch (Exception var4) {
            }

         }
      }
   }

   public void addTransactionSend(short id) {
      this.didWeSendThatTrans.add(id);
   }

   public boolean isEyeInFluid(FluidTag tag) {
      return this.fluidOnEyes == tag;
   }

   public double getEyeHeight() {
      return this.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_9) ? (double)this.pose.eyeHeight : (this.isSneaking ? 1.5399999618530273D : 1.6200000047683716D);
   }

   public void timedOut() {
      this.disconnect(MessageUtil.miniMessage(MessageUtil.replacePlaceholders(this, GrimAPI.INSTANCE.getConfigManager().getDisconnectTimeout())));
   }

   public void disconnect(Component reason) {
      String textReason;
      if (reason instanceof TranslatableComponent) {
         TranslatableComponent translatableComponent = (TranslatableComponent)reason;
         textReason = translatableComponent.key();
      } else {
         textReason = LegacyComponentSerializer.legacySection().serialize(reason);
      }

      String var10000 = this.user.getProfile().getName();
      LogUtil.info("Disconnecting " + var10000 + " for " + ChatColor.stripColor(textReason));

      try {
         this.user.sendPacket((PacketWrapper)(new WrapperPlayServerDisconnect(reason)));
      } catch (Exception var4) {
         LogUtil.warn("Failed to send disconnect packet to disconnect " + this.user.getProfile().getName() + "! Disconnecting anyways.");
      }

      this.user.closeConnection();
      if (this.bukkitPlayer != null) {
         FoliaScheduler.getEntityScheduler().execute(this.bukkitPlayer, GrimAPI.INSTANCE.getPlugin(), () -> {
            this.bukkitPlayer.kickPlayer(textReason);
         }, (Runnable)null, 1L);
      }

   }

   public void pollData() {
      if (this.lastTransSent != 0L && this.lastTransSent + 80L < System.currentTimeMillis()) {
         this.sendTransaction(true);
      }

      if ((double)(System.nanoTime() - this.getPlayerClockAtLeast()) > (double)this.maxTransactionTime * 1.0E9D) {
         this.timedOut();
      }

      if (!GrimAPI.INSTANCE.getPlayerDataManager().shouldCheck(this.user)) {
         GrimAPI.INSTANCE.getPlayerDataManager().remove(this.user);
      }

      if (this.packetTracker == null && ViaVersionUtil.isAvailable() && this.uuid != null) {
         UserConnection connection = Via.getManager().getConnectionManager().getConnectedClient(this.uuid);
         this.packetTracker = connection != null ? connection.getPacketTracker() : null;
      }

      if (this.uuid != null && this.bukkitPlayer == null) {
         this.bukkitPlayer = Bukkit.getPlayer(this.uuid);
         this.updatePermissions();
      }

   }

   public void updateVelocityMovementSkipping() {
      if (!this.couldSkipTick) {
         this.couldSkipTick = this.pointThreeEstimator.determineCanSkipTick(BlockProperties.getFrictionInfluencedSpeed((float)(this.speed * (this.isSprinting ? 1.3D : 1.0D)), this), this.getPossibleVelocitiesMinusKnockback());
      }

      Set<VectorData> knockback = new HashSet();
      if (this.firstBreadKB != null) {
         knockback.add(new VectorData(this.firstBreadKB.vector, VectorData.VectorType.Knockback));
      }

      if (this.likelyKB != null) {
         knockback.add(new VectorData(this.likelyKB.vector, VectorData.VectorType.Knockback));
      }

      boolean kbPointThree = this.pointThreeEstimator.determineCanSkipTick(BlockProperties.getFrictionInfluencedSpeed((float)(this.speed * (this.isSprinting ? 1.3D : 1.0D)), this), knockback);
      this.checkManager.getKnockbackHandler().setPointThree(kbPointThree);
      Set<VectorData> explosion = new HashSet();
      if (this.firstBreadExplosion != null) {
         explosion.add(new VectorData(this.firstBreadExplosion.vector, VectorData.VectorType.Explosion));
      }

      if (this.likelyExplosions != null) {
         explosion.add(new VectorData(this.likelyExplosions.vector, VectorData.VectorType.Explosion));
      }

      boolean explosionPointThree = this.pointThreeEstimator.determineCanSkipTick(BlockProperties.getFrictionInfluencedSpeed((float)(this.speed * (this.isSprinting ? 1.3D : 1.0D)), this), explosion);
      this.checkManager.getExplosionHandler().setPointThree(explosionPointThree);
      if (kbPointThree || explosionPointThree) {
         this.uncertaintyHandler.lastPointThree.reset();
      }

   }

   public void updatePermissions() {
      if (this.bukkitPlayer != null) {
         this.noModifyPacketPermission = this.bukkitPlayer.hasPermission("grim.nomodifypacket");
         this.noSetbackPermission = this.bukkitPlayer.hasPermission("grim.nosetback");
         FoliaScheduler.getAsyncScheduler().runNow(GrimAPI.INSTANCE.getPlugin(), (t) -> {
            Iterator var2 = this.checkManager.allChecks.values().iterator();

            while(var2.hasNext()) {
               AbstractCheck check = (AbstractCheck)var2.next();
               if (check instanceof Check) {
                  ((Check)check).updatePermissions();
               }
            }

         });
      }
   }

   public boolean isPointThree() {
      return this.getClientVersion().isOlderThan(ClientVersion.V_1_18_2);
   }

   public double getMovementThreshold() {
      return this.isPointThree() ? 0.03D : 2.0E-4D;
   }

   public ClientVersion getClientVersion() {
      ClientVersion ver = this.user.getClientVersion();
      return (ClientVersion)Objects.requireNonNullElseGet(ver, () -> {
         return ClientVersion.getById(PacketEvents.getAPI().getServerManager().getVersion().getProtocolVersion());
      });
   }

   public boolean isTickingReliablyFor(int ticks) {
      return !this.canSkipTicks() || (this.inVehicle() || !this.uncertaintyHandler.lastPointThree.hasOccurredSince(ticks)) && !this.uncertaintyHandler.lastVehicleSwitch.hasOccurredSince(1);
   }

   public boolean inVehicle() {
      return this.compensatedEntities.self.inVehicle();
   }

   public CompensatedInventory getInventory() {
      return this.checkManager.getInventory();
   }

   public double[] getPossibleEyeHeights() {
      if (this.getClientVersion().isOlderThan(ClientVersion.V_1_9)) {
         return this.isSneaking ? this.possibleEyeHeights[1] : this.possibleEyeHeights[0];
      } else {
         double[] var10000;
         switch(this.pose) {
         case FALL_FLYING:
         case SPIN_ATTACK:
         case SWIMMING:
            var10000 = this.possibleEyeHeights[2];
            break;
         case NINE_CROUCHING:
         case CROUCHING:
            var10000 = this.possibleEyeHeights[1];
            break;
         default:
            var10000 = this.possibleEyeHeights[0];
         }

         return var10000;
      }
   }

   public int getTransactionPing() {
      return GrimMath.floor((double)this.transactionPing / 1000000.0D);
   }

   public int getKeepAlivePing() {
      return this.bukkitPlayer == null ? -1 : PacketEvents.getAPI().getPlayerManager().getPing(this.bukkitPlayer);
   }

   public SetbackTeleportUtil getSetbackTeleportUtil() {
      return this.checkManager.getSetbackUtil();
   }

   public boolean wouldCollisionResultFlagGroundSpoof(double inputY, double collisionY) {
      boolean verticalCollision = inputY != collisionY;
      boolean calculatedOnGround = verticalCollision && inputY < 0.0D;
      if (this.exemptOnGround()) {
         return false;
      } else if (inputY == -1.0E-7D && collisionY > -1.0E-7D && collisionY <= 0.0D) {
         return false;
      } else {
         return calculatedOnGround != this.onGround;
      }
   }

   public boolean exemptOnGround() {
      return this.inVehicle() || (Double)Collections.max(this.uncertaintyHandler.pistonX) != 0.0D || (Double)Collections.max(this.uncertaintyHandler.pistonY) != 0.0D || (Double)Collections.max(this.uncertaintyHandler.pistonZ) != 0.0D || this.uncertaintyHandler.isStepMovement || this.isFlying || this.compensatedEntities.self.isDead || this.isInBed || this.lastInBed || this.uncertaintyHandler.lastFlyingStatusChange.hasOccurredSince(30) || this.uncertaintyHandler.lastHardCollidingLerpingEntity.hasOccurredSince(3) || this.uncertaintyHandler.isOrWasNearGlitchyBlock;
   }

   public void handleMountVehicle(int vehicleID) {
      this.compensatedEntities.serverPlayerVehicle = vehicleID;
      TrackerData data = this.compensatedEntities.getTrackedEntity(vehicleID);
      if (data != null && PacketEvents.getAPI().getServerManager().getVersion().isNewerThanOrEquals(ServerVersion.V_1_9) && this.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_9) && (EntityTypes.isTypeInstanceOf(data.getEntityType(), EntityTypes.BOAT) || EntityTypes.isTypeInstanceOf(data.getEntityType(), EntityTypes.ABSTRACT_HORSE) || data.getEntityType() == EntityTypes.PIG || data.getEntityType() == EntityTypes.STRIDER)) {
         this.user.writePacket((PacketWrapper)(new WrapperPlayServerEntityVelocity(vehicleID, new Vector3d())));
      }

      this.sendTransaction();
      this.latencyUtils.addRealTimeTask(this.lastTransactionSent.get(), () -> {
         this.vehicleData.wasVehicleSwitch = true;
      });
   }

   public int getRidingVehicleId() {
      return this.compensatedEntities.getPacketEntityID(this.compensatedEntities.self.getRiding());
   }

   public void handleDismountVehicle(PacketSendEvent event) {
      this.sendTransaction();
      this.compensatedEntities.serverPlayerVehicle = null;
      event.getTasksAfterSend().add(() -> {
         if (this.inVehicle()) {
            int ridingId = this.getRidingVehicleId();
            TrackerData data = (TrackerData)this.compensatedEntities.serverPositionsMap.get(ridingId);
            if (data != null) {
               this.user.writePacket((PacketWrapper)(new WrapperPlayServerEntityTeleport(ridingId, new Vector3d(data.getX(), data.getY(), data.getZ()), data.getXRot(), data.getYRot(), false)));
            }
         }

      });
      this.latencyUtils.addRealTimeTask(this.lastTransactionSent.get(), () -> {
         this.vehicleData.wasVehicleSwitch = true;
         if (this.getClientVersion().isOlderThanOrEquals(ClientVersion.V_1_14)) {
            this.compensatedEntities.hasSprintingAttributeEnabled = false;
         }

      });
   }

   public boolean canGlide() {
      if (!this.getClientVersion().isOlderThan(ClientVersion.V_1_21_2) && !PacketEvents.getAPI().getServerManager().getVersion().isOlderThan(ServerVersion.V_1_21_2)) {
         CompensatedInventory inventory = this.getInventory();
         return isGlider(inventory.getHelmet(), EquipmentSlot.CHEST_PLATE) || isGlider(inventory.getChestplate(), EquipmentSlot.LEGGINGS) || isGlider(inventory.getLeggings(), EquipmentSlot.BOOTS) || isGlider(inventory.getBoots(), EquipmentSlot.OFF_HAND);
      } else {
         ItemStack chestPlate = this.getInventory().getChestplate();
         return chestPlate.getType() == ItemTypes.ELYTRA && chestPlate.getDamageValue() < chestPlate.getMaxDamage();
      }
   }

   private static boolean isGlider(ItemStack stack, EquipmentSlot slot) {
      if (stack.hasComponent(ComponentTypes.GLIDER) && stack.getDamageValue() < stack.getMaxDamage()) {
         Optional<ItemEquippable> equippable = stack.getComponent(ComponentTypes.EQUIPPABLE);
         return equippable.isPresent() && ((ItemEquippable)equippable.get()).getSlot() == slot;
      } else {
         return false;
      }
   }

   public void resyncPose() {
      if (this.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_14) && this.bukkitPlayer != null) {
         this.bukkitPlayer.setSneaking(!this.bukkitPlayer.isSneaking());
      }

   }

   public boolean canUseGameMasterBlocks() {
      return this.getClientVersion().isOlderThanOrEquals(ClientVersion.V_1_10) || this.gamemode == GameMode.CREATIVE && this.compensatedEntities.self.getOpLevel() >= 2;
   }

   @Contract(
      pure = true
   )
   public boolean supportsEndTick() {
      return this.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_21_2) && PacketEvents.getAPI().getServerManager().getVersion().isNewerThanOrEquals(ServerVersion.V_1_21_2);
   }

   @Contract(
      pure = true
   )
   public boolean canSkipTicks() {
      return this.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_9) && !this.supportsEndTick();
   }

   public void runSafely(Runnable runnable) {
      ChannelHelper.runInEventLoop(this.user.getChannel(), runnable);
   }

   public int getLastTransactionReceived() {
      return this.lastTransactionReceived.get();
   }

   public int getLastTransactionSent() {
      return this.lastTransactionSent.get();
   }

   public void addRealTimeTask(int transaction, Runnable runnable) {
      this.latencyUtils.addRealTimeTask(transaction, runnable);
   }

   public String getName() {
      return this.user.getName();
   }

   public UUID getUniqueId() {
      return this.user.getProfile().getUUID();
   }

   public String getBrand() {
      return ((ClientBrand)this.checkManager.getPacketCheck(ClientBrand.class)).getBrand();
   }

   @Nullable
   public String getWorldName() {
      return this.bukkitPlayer != null ? this.bukkitPlayer.getWorld().getName() : null;
   }

   @Nullable
   public UUID getWorldUID() {
      return this.bukkitPlayer != null ? this.bukkitPlayer.getWorld().getUID() : null;
   }

   public String getVersionName() {
      return this.getClientVersion().getReleaseName();
   }

   public double getHorizontalSensitivity() {
      return ((AimProcessor)this.checkManager.getRotationCheck(AimProcessor.class)).sensitivityX;
   }

   public double getVerticalSensitivity() {
      return ((AimProcessor)this.checkManager.getRotationCheck(AimProcessor.class)).sensitivityY;
   }

   public boolean isVanillaMath() {
      return this.trigHandler.isVanillaMath();
   }

   public Collection<? extends AbstractCheck> getChecks() {
      return this.checkManager.allChecks.values();
   }

   public void runNettyTaskInMs(Runnable runnable, int ms) {
      Channel channel = (Channel)this.user.getChannel();
      channel.eventLoop().schedule(runnable, (long)ms, TimeUnit.MILLISECONDS);
   }

   public void reload(ConfigManager config) {
      this.featureManager.onReload(config);
      this.debugPacketCancel = config.getBooleanElse("debug-packet-cancel", false);
      this.spamThreshold = config.getIntElse("packet-spam-threshold", 100);
      this.maxTransactionTime = GrimMath.clamp(config.getIntElse("max-transaction-time", 60), 1, 180);
      this.ignoreDuplicatePacketRotation = config.getBooleanElse("ignore-duplicate-packet-rotation", false);
      this.cancelDuplicatePacket = config.getBooleanElse("cancel-duplicate-packet", true);
      this.resetItemUsageOnAttack = config.getBooleanElse("reset-item-usage-on-attack", true);
      this.resetItemUsageOnItemUpdate = config.getBooleanElse("reset-item-usage-on-item-update", true);
      this.resetItemUsageOnSlotChange = config.getBooleanElse("reset-item-usage-on-slot-change", true);
      Iterator var2 = this.checkManager.allChecks.values().iterator();

      while(var2.hasNext()) {
         AbstractCheck value = (AbstractCheck)var2.next();
         value.reload();
      }

      this.punishmentManager.reload(config);
   }

   public void reload() {
      this.reload(GrimAPI.INSTANCE.getConfigManager().getConfig());
   }

   public FeatureManager getFeatureManager() {
      return this.featureManager;
   }

   public void sendMessage(String message) {
      if (this.bukkitPlayer != null) {
         this.bukkitPlayer.sendMessage(message);
      }

   }

   public ResyncHandler getResyncHandler() {
      return this.resyncHandler;
   }

   public void setResyncHandler(ResyncHandler resyncHandler) {
      this.resyncHandler = resyncHandler;
   }

   public long getPlayerClockAtLeast() {
      return this.playerClockAtLeast;
   }

   public boolean isIgnoreDuplicatePacketRotation() {
      return this.ignoreDuplicatePacketRotation;
   }

   public boolean isExperimentalChecks() {
      return this.experimentalChecks;
   }

   public void setExperimentalChecks(boolean experimentalChecks) {
      this.experimentalChecks = experimentalChecks;
   }

   public boolean isCancelDuplicatePacket() {
      return this.cancelDuplicatePacket;
   }

   public boolean isExemptElytra() {
      return this.exemptElytra;
   }

   public void setExemptElytra(boolean exemptElytra) {
      this.exemptElytra = exemptElytra;
   }

   public boolean isResetItemUsageOnAttack() {
      return this.resetItemUsageOnAttack;
   }

   public boolean isResetItemUsageOnItemUpdate() {
      return this.resetItemUsageOnItemUpdate;
   }

   public boolean isResetItemUsageOnSlotChange() {
      return this.resetItemUsageOnSlotChange;
   }
}
