package ac.grim.grimac.utils.ai.training;

import ac.grim.grimac.utils.ai.models.AITrainingData;
import ac.grim.grimac.utils.ai.models.PlayerBehaviorData;
import ac.grim.grimac.utils.ai.models.AIDetectionResult;
import ac.grim.grimac.utils.ai.OpenAIClient;

import java.util.*;
import java.util.stream.Collectors;
import java.util.logging.Logger;

/**
 * Advanced AI training processor that handles data preparation,
 * feature extraction, and model improvement
 */
public class AITrainingProcessor {
    private static final Logger LOGGER = Logger.getLogger("GrimAC-AI-Training-Processor");
    
    private final OpenAIClient openAIClient;
    
    // Training configuration
    private final int minSamplesPerCategory = 20;
    private final double validationSplit = 0.2; // 20% for validation
    private final int maxFeaturesPerSample = 50;
    
    public AITrainingProcessor(OpenAIClient openAIClient) {
        this.openAIClient = openAIClient;
    }
    
    /**
     * Process training data and improve AI model
     */
    public TrainingResult processTrainingData(List<AITrainingData> rawData) {
        LOGGER.info("Starting training data processing with " + rawData.size() + " samples");
        
        try {
            // Step 1: Clean and validate data
            List<AITrainingData> cleanData = cleanTrainingData(rawData);
            LOGGER.info("Cleaned data: " + cleanData.size() + " valid samples");
            
            // Step 2: Balance dataset
            List<AITrainingData> balancedData = balanceDataset(cleanData);
            LOGGER.info("Balanced data: " + balancedData.size() + " samples");
            
            // Step 3: Extract features
            List<TrainingFeatureSet> featureSets = extractFeatures(balancedData);
            LOGGER.info("Extracted features for " + featureSets.size() + " samples");
            
            // Step 4: Split into training and validation
            TrainingValidationSplit split = splitData(featureSets);
            LOGGER.info("Split data - Training: " + split.training.size() + ", Validation: " + split.validation.size());
            
            // Step 5: Train model
            boolean trainingSuccess = trainModel(split.training);
            
            // Step 6: Validate model
            ValidationResult validation = validateModel(split.validation);
            
            // Step 7: Generate training report
            TrainingResult result = new TrainingResult(
                trainingSuccess,
                validation,
                cleanData.size(),
                balancedData.size(),
                featureSets.size()
            );
            
            LOGGER.info("Training completed. Success: " + trainingSuccess + 
                       ", Accuracy: " + validation.accuracy);
            
            return result;
            
        } catch (Exception e) {
            LOGGER.severe("Training processing failed: " + e.getMessage());
            return TrainingResult.createError(e.getMessage());
        }
    }
    
    /**
     * Clean and validate training data
     */
    private List<AITrainingData> cleanTrainingData(List<AITrainingData> rawData) {
        return rawData.stream()
            .filter(this::isValidTrainingData)
            .filter(this::hasCompleteData)
            .filter(this::isRecentData)
            .collect(Collectors.toList());
    }
    
    private boolean isValidTrainingData(AITrainingData data) {
        return data != null && 
               data.getPlayerData() != null && 
               data.getBehaviorData() != null && 
               data.getResult() != null && 
               !data.getResult().isError();
    }
    
    private boolean hasCompleteData(AITrainingData data) {
        PlayerBehaviorData playerData = data.getPlayerData();
        return playerData.getTotalPacketsReceived() > 10 && // Minimum activity
               playerData.getSessionDurationMs() > 30000; // At least 30 seconds
    }
    
    private boolean isRecentData(AITrainingData data) {
        long maxAge = 7 * 24 * 60 * 60 * 1000L; // 7 days
        return System.currentTimeMillis() - data.getTimestamp() < maxAge;
    }
    
    /**
     * Balance dataset to prevent bias
     */
    private List<AITrainingData> balanceDataset(List<AITrainingData> data) {
        Map<String, List<AITrainingData>> categorized = data.stream()
            .collect(Collectors.groupingBy(d -> d.getResult().isSuspicious() ? "suspicious" : "normal"));
        
        List<AITrainingData> suspicious = categorized.getOrDefault("suspicious", new ArrayList<>());
        List<AITrainingData> normal = categorized.getOrDefault("normal", new ArrayList<>());
        
        // Ensure minimum samples per category
        if (suspicious.size() < minSamplesPerCategory || normal.size() < minSamplesPerCategory) {
            LOGGER.warning("Insufficient samples for balanced training. Suspicious: " + 
                          suspicious.size() + ", Normal: " + normal.size());
        }
        
        // Balance by taking equal amounts from each category
        int maxSamples = Math.min(suspicious.size(), normal.size());
        maxSamples = Math.min(maxSamples, 1000); // Cap at 1000 per category
        
        List<AITrainingData> balanced = new ArrayList<>();
        
        // Randomly sample from each category
        Collections.shuffle(suspicious);
        Collections.shuffle(normal);
        
        balanced.addAll(suspicious.stream().limit(maxSamples).collect(Collectors.toList()));
        balanced.addAll(normal.stream().limit(maxSamples).collect(Collectors.toList()));
        
        Collections.shuffle(balanced); // Final shuffle
        
        return balanced;
    }
    
    /**
     * Extract features from training data
     */
    private List<TrainingFeatureSet> extractFeatures(List<AITrainingData> data) {
        return data.stream()
            .map(this::extractFeatureSet)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }
    
    private TrainingFeatureSet extractFeatureSet(AITrainingData data) {
        try {
            Map<String, Double> features = new HashMap<>();
            PlayerBehaviorData playerData = data.getPlayerData();
            
            // Basic player features
            features.put("sessionDuration", (double) playerData.getSessionDurationMs());
            features.put("totalPackets", (double) playerData.getTotalPacketsReceived());
            features.put("suspiciousEvents", (double) playerData.getSuspiciousEvents());
            features.put("suspiciousRate", playerData.getSuspiciousEventRate());
            
            // Movement features
            if (playerData.getMovementStats() != null) {
                features.put("avgSpeed", playerData.getMovementStats().getAverageSpeed());
                features.put("maxSpeed", playerData.getMovementStats().getMaxSpeed());
                features.put("jumpCount", (double) playerData.getMovementStats().getJumpCount());
                features.put("directionChanges", (double) playerData.getMovementStats().getDirectionChanges());
            }
            
            // Combat features
            if (playerData.getCombatStats() != null) {
                features.put("combatAccuracy", playerData.getCombatStats().getAverageAccuracy());
                features.put("avgReach", playerData.getCombatStats().getAverageReachDistance());
                features.put("totalAttacks", (double) playerData.getCombatStats().getTotalAttacks());
            }
            
            // Building features
            if (playerData.getBuildingStats() != null) {
                features.put("buildSpeed", playerData.getBuildingStats().getAverageBuildSpeed());
                features.put("totalBlocks", (double) playerData.getBuildingStats().getTotalBlocks());
            }
            
            // Behavior-specific features
            addBehaviorSpecificFeatures(features, data);
            
            // Pattern features
            addPatternFeatures(features, playerData);
            
            // Normalize features
            normalizeFeatures(features);
            
            return new TrainingFeatureSet(
                features,
                data.getResult().isSuspicious(),
                data.getResult().getConfidence(),
                data.getBehaviorType()
            );
            
        } catch (Exception e) {
            LOGGER.warning("Failed to extract features: " + e.getMessage());
            return null;
        }
    }
    
    private void addBehaviorSpecificFeatures(Map<String, Double> features, AITrainingData data) {
        String behaviorType = data.getBehaviorType();
        Object behaviorData = data.getBehaviorData();
        
        // Add behavior-specific features based on type
        switch (behaviorType.toLowerCase()) {
            case "movement":
                // Movement-specific features would be added here
                break;
            case "combat":
                // Combat-specific features would be added here
                break;
            case "building":
                // Building-specific features would be added here
                break;
            case "exploit":
                // Exploit-specific features would be added here
                break;
        }
    }
    
    private void addPatternFeatures(Map<String, Double> features, PlayerBehaviorData playerData) {
        List<String> patterns = playerData.getRecentPatterns();
        
        // Convert patterns to numerical features
        features.put("hasUnusualMovement", patterns.contains("unusual_movement") ? 1.0 : 0.0);
        features.put("hasUnusualCombat", patterns.contains("unusual_combat") ? 1.0 : 0.0);
        features.put("hasUnusualBuilding", patterns.contains("unusual_building") ? 1.0 : 0.0);
        features.put("patternCount", (double) patterns.size());
    }
    
    private void normalizeFeatures(Map<String, Double> features) {
        // Simple min-max normalization for specific features
        String[] toNormalize = {"sessionDuration", "totalPackets", "totalAttacks", "totalBlocks"};
        
        for (String feature : toNormalize) {
            if (features.containsKey(feature)) {
                double value = features.get(feature);
                // Apply log normalization for large values
                if (value > 100) {
                    features.put(feature, Math.log(value + 1));
                }
            }
        }
    }
    
    private TrainingValidationSplit splitData(List<TrainingFeatureSet> data) {
        Collections.shuffle(data);
        
        int splitIndex = (int) (data.size() * (1.0 - validationSplit));
        
        return new TrainingValidationSplit(
            data.subList(0, splitIndex),
            data.subList(splitIndex, data.size())
        );
    }
    
    private boolean trainModel(List<TrainingFeatureSet> trainingData) {
        try {
            // Convert feature sets to training format for OpenAI
            List<AITrainingData> convertedData = convertFeaturesToTrainingData(trainingData);
            
            // Send to OpenAI for training
            return openAIClient.trainWithData(convertedData);
            
        } catch (Exception e) {
            LOGGER.severe("Model training failed: " + e.getMessage());
            return false;
        }
    }
    
    private ValidationResult validateModel(List<TrainingFeatureSet> validationData) {
        int correct = 0;
        int total = validationData.size();
        
        // This would involve testing the trained model against validation data
        // For now, return a placeholder result
        double accuracy = 0.85; // Placeholder
        
        return new ValidationResult(accuracy, total, correct);
    }
    
    private List<AITrainingData> convertFeaturesToTrainingData(List<TrainingFeatureSet> featureSets) {
        // Convert feature sets back to AITrainingData format for OpenAI
        // This is a simplified conversion
        return new ArrayList<>(); // Placeholder
    }
    
    // Data classes
    public static class TrainingFeatureSet {
        public final Map<String, Double> features;
        public final boolean isSuspicious;
        public final double confidence;
        public final String behaviorType;
        
        public TrainingFeatureSet(Map<String, Double> features, boolean isSuspicious, 
                                double confidence, String behaviorType) {
            this.features = features;
            this.isSuspicious = isSuspicious;
            this.confidence = confidence;
            this.behaviorType = behaviorType;
        }
    }
    
    public static class TrainingValidationSplit {
        public final List<TrainingFeatureSet> training;
        public final List<TrainingFeatureSet> validation;
        
        public TrainingValidationSplit(List<TrainingFeatureSet> training, List<TrainingFeatureSet> validation) {
            this.training = training;
            this.validation = validation;
        }
    }
    
    public static class ValidationResult {
        public final double accuracy;
        public final int totalSamples;
        public final int correctPredictions;
        
        public ValidationResult(double accuracy, int totalSamples, int correctPredictions) {
            this.accuracy = accuracy;
            this.totalSamples = totalSamples;
            this.correctPredictions = correctPredictions;
        }
    }
    
    public static class TrainingResult {
        public final boolean success;
        public final ValidationResult validation;
        public final int originalSamples;
        public final int balancedSamples;
        public final int featureSamples;
        public final String errorMessage;
        
        public TrainingResult(boolean success, ValidationResult validation, 
                            int originalSamples, int balancedSamples, int featureSamples) {
            this.success = success;
            this.validation = validation;
            this.originalSamples = originalSamples;
            this.balancedSamples = balancedSamples;
            this.featureSamples = featureSamples;
            this.errorMessage = null;
        }
        
        private TrainingResult(String errorMessage) {
            this.success = false;
            this.validation = null;
            this.originalSamples = 0;
            this.balancedSamples = 0;
            this.featureSamples = 0;
            this.errorMessage = errorMessage;
        }
        
        public static TrainingResult createError(String errorMessage) {
            return new TrainingResult(errorMessage);
        }
    }
}
