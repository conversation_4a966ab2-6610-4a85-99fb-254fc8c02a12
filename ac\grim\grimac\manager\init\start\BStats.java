package ac.grim.grimac.manager.init.start;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.manager.init.Initable;
import ac.grim.grimac.shaded.io.github.retrooper.packetevents.bstats.bukkit.Metrics;

public class BStats implements Initable {
   public void start() {
      short pluginId = 12820;

      try {
         new Metrics(GrimAPI.INSTANCE.getPlugin(), pluginId);
      } catch (Exception var3) {
      }

   }
}
