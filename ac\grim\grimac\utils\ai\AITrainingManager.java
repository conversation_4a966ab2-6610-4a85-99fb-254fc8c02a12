package ac.grim.grimac.utils.ai;

import ac.grim.grimac.utils.ai.models.AITrainingData;
import ac.grim.grimac.utils.ai.training.AITrainingProcessor;
import ac.grim.grimac.utils.ai.learning.AILearningSystem;

import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.List;
import java.util.ArrayList;
import java.util.logging.Logger;

/**
 * Manages AI training data collection and training processes
 */
public class AITrainingManager {
    private static final Logger LOGGER = Logger.getLogger("GrimAC-AI-Training");

    private final OpenAIClient openAIClient;
    private final AITrainingProcessor trainingProcessor;
    private final AILearningSystem learningSystem;
    private final ConcurrentLinkedQueue<AITrainingData> trainingDataQueue;
    private final AtomicInteger totalTrainingDataCollected;
    private final AtomicInteger successfulTrainingSessions;
    
    // Training configuration
    private final int minDataSizeForTraining = 100;
    private final int maxDataSizePerBatch = 50;
    private final int maxQueueSize = 10000;
    
    public AITrainingManager(OpenAIClient openAIClient) {
        this.openAIClient = openAIClient;
        this.trainingProcessor = new AITrainingProcessor(openAIClient);
        this.learningSystem = new AILearningSystem();
        this.trainingDataQueue = new ConcurrentLinkedQueue<>();
        this.totalTrainingDataCollected = new AtomicInteger(0);
        this.successfulTrainingSessions = new AtomicInteger(0);

        LOGGER.info("AI Training Manager initialized with advanced learning capabilities");
    }
    
    /**
     * Add training data to the queue
     */
    public void addTrainingData(AITrainingData data) {
        if (data == null) {
            return;
        }
        
        // Prevent queue from growing too large
        if (trainingDataQueue.size() >= maxQueueSize) {
            // Remove oldest data to make room
            trainingDataQueue.poll();
            LOGGER.fine("Training queue full, removed oldest data");
        }
        
        trainingDataQueue.offer(data);
        totalTrainingDataCollected.incrementAndGet();
        
        LOGGER.fine("Added training data. Queue size: " + trainingDataQueue.size());
    }
    
    /**
     * Check if there's enough data for training
     */
    public boolean hasEnoughDataForTraining() {
        return trainingDataQueue.size() >= minDataSizeForTraining;
    }
    
    /**
     * Perform AI training with collected data using advanced processing
     */
    public boolean performTraining() {
        if (!hasEnoughDataForTraining()) {
            LOGGER.info("Not enough training data. Current: " + trainingDataQueue.size() +
                       ", Required: " + minDataSizeForTraining);
            return false;
        }

        try {
            LOGGER.info("Starting advanced AI training session with " + trainingDataQueue.size() + " data points");

            // Collect all training data
            List<AITrainingData> allData = new ArrayList<>(trainingDataQueue);

            // Use advanced training processor
            AITrainingProcessor.TrainingResult result = trainingProcessor.processTrainingData(allData);

            if (result.success) {
                successfulTrainingSessions.incrementAndGet();

                // Clear processed data from queue
                trainingDataQueue.clear();

                LOGGER.info("Advanced AI training completed successfully!");
                LOGGER.info("Training Summary:");
                LOGGER.info("  Original samples: " + result.originalSamples);
                LOGGER.info("  Balanced samples: " + result.balancedSamples);
                LOGGER.info("  Feature samples: " + result.featureSamples);
                if (result.validation != null) {
                    LOGGER.info("  Validation accuracy: " + String.format("%.3f", result.validation.accuracy));
                }

                return true;
            } else {
                LOGGER.warning("Advanced AI training failed: " +
                             (result.errorMessage != null ? result.errorMessage : "Unknown error"));
                return false;
            }

        } catch (Exception e) {
            LOGGER.severe("AI training session failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Process a batch of training data
     */
    private boolean processBatch(List<AITrainingData> batch) {
        try {
            LOGGER.fine("Processing training batch with " + batch.size() + " items");
            
            // Filter and validate training data
            List<AITrainingData> validData = filterValidTrainingData(batch);
            
            if (validData.isEmpty()) {
                LOGGER.fine("No valid training data in batch");
                return true; // Not an error, just no valid data
            }
            
            // Send to OpenAI for training
            boolean success = openAIClient.trainWithData(validData);
            
            if (success) {
                LOGGER.fine("Training batch processed successfully");
            } else {
                LOGGER.warning("Training batch processing failed");
            }
            
            return success;
            
        } catch (Exception e) {
            LOGGER.warning("Error processing training batch: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Filter and validate training data
     */
    private List<AITrainingData> filterValidTrainingData(List<AITrainingData> data) {
        List<AITrainingData> validData = new ArrayList<>();
        
        for (AITrainingData item : data) {
            if (isValidTrainingData(item)) {
                validData.add(item);
            }
        }
        
        LOGGER.fine("Filtered " + validData.size() + " valid items from " + data.size() + " total");
        return validData;
    }
    
    /**
     * Validate individual training data item
     */
    private boolean isValidTrainingData(AITrainingData data) {
        if (data == null) {
            return false;
        }
        
        // Check if data has required fields
        if (data.getPlayerData() == null || 
            data.getBehaviorType() == null || 
            data.getBehaviorData() == null ||
            data.getResult() == null) {
            return false;
        }
        
        // Check if result is not an error
        if (data.getResult().isError()) {
            return false;
        }
        
        // Check if data is not too old (older than 7 days)
        long dataAge = System.currentTimeMillis() - data.getTimestamp();
        if (dataAge > 7 * 24 * 60 * 60 * 1000L) { // 7 days in milliseconds
            return false;
        }
        
        return true;
    }
    
    /**
     * Clear all training data
     */
    public void clearTrainingData() {
        int clearedCount = trainingDataQueue.size();
        trainingDataQueue.clear();
        LOGGER.info("Cleared " + clearedCount + " training data items");
    }
    
    /**
     * Get training statistics
     */
    public TrainingStatistics getTrainingStatistics() {
        return new TrainingStatistics(
            trainingDataQueue.size(),
            totalTrainingDataCollected.get(),
            successfulTrainingSessions.get(),
            hasEnoughDataForTraining()
        );
    }
    
    // Getters
    public int getQueueSize() {
        return trainingDataQueue.size();
    }
    
    public int getTotalDataCollected() {
        return totalTrainingDataCollected.get();
    }
    
    public int getSuccessfulTrainingSessions() {
        return successfulTrainingSessions.get();
    }

    /**
     * Get the learning system for adaptive improvements
     */
    public AILearningSystem getLearningSystem() {
        return learningSystem;
    }

    /**
     * Process prediction feedback for learning
     */
    public void processPredictionFeedback(String behaviorType,
                                        ac.grim.grimac.utils.ai.models.AIDetectionResult prediction,
                                        ac.grim.grimac.utils.ai.models.PlayerBehaviorData playerData,
                                        boolean actualResult) {
        learningSystem.processPrediction(behaviorType, prediction, playerData, actualResult);
    }

    /**
     * Get adaptive threshold for behavior type
     */
    public double getAdaptiveThreshold(String behaviorType) {
        return learningSystem.getAdaptiveThreshold(behaviorType);
    }
    
    /**
     * Training statistics data class
     */
    public static class TrainingStatistics {
        private final int currentQueueSize;
        private final int totalDataCollected;
        private final int successfulSessions;
        private final boolean readyForTraining;
        
        public TrainingStatistics(int currentQueueSize, int totalDataCollected, 
                                int successfulSessions, boolean readyForTraining) {
            this.currentQueueSize = currentQueueSize;
            this.totalDataCollected = totalDataCollected;
            this.successfulSessions = successfulSessions;
            this.readyForTraining = readyForTraining;
        }
        
        public int getCurrentQueueSize() { return currentQueueSize; }
        public int getTotalDataCollected() { return totalDataCollected; }
        public int getSuccessfulSessions() { return successfulSessions; }
        public boolean isReadyForTraining() { return readyForTraining; }
        
        @Override
        public String toString() {
            return String.format("TrainingStats{queue=%d, total=%d, sessions=%d, ready=%s}",
                               currentQueueSize, totalDataCollected, successfulSessions, readyForTraining);
        }
    }
}
