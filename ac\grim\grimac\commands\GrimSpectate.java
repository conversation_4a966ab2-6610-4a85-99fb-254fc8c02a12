package ac.grim.grimac.commands;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.acf.BaseCommand;
import ac.grim.grimac.shaded.acf.annotation.CommandAlias;
import ac.grim.grimac.shaded.acf.annotation.CommandCompletion;
import ac.grim.grimac.shaded.acf.annotation.CommandPermission;
import ac.grim.grimac.shaded.acf.annotation.Optional;
import ac.grim.grimac.shaded.acf.annotation.Subcommand;
import ac.grim.grimac.shaded.acf.bukkit.contexts.OnlinePlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.PacketEvents;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.manager.server.ServerVersion;
import ac.grim.grimac.utils.anticheat.MessageUtil;
import ac.grim.grimac.utils.anticheat.MultiLibUtil;
import ac.grim.grimac.utils.reflection.PaperUtils;
import org.bukkit.GameMode;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

@CommandAlias("grim|grimac")
public class GrimSpectate extends BaseCommand {
   @Subcommand("spectate")
   @CommandPermission("grim.spectate")
   @CommandCompletion("@players")
   public void onSpectate(CommandSender sender, @Optional OnlinePlayer target) {
      if (sender instanceof Player) {
         Player player = (Player)sender;
         String message;
         if (target != null && target.getPlayer().getUniqueId().equals(player.getUniqueId())) {
            message = GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("cannot-run-on-self", "%prefix% &cYou cannot use this command on yourself!");
            message = MessageUtil.replacePlaceholders((Object)target.getPlayer(), (String)message);
            MessageUtil.sendMessage(player, MessageUtil.miniMessage(message));
         } else if (target == null || PacketEvents.getAPI().getServerManager().getVersion().isNewerThanOrEquals(ServerVersion.V_1_18) && MultiLibUtil.isExternalPlayer(target.getPlayer())) {
            message = GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("player-not-this-server", "%prefix% &cThis player isn't on this server!");
            message = MessageUtil.replacePlaceholders((Object)(target != null ? target.getPlayer() : null), (String)message);
            MessageUtil.sendMessage(player, MessageUtil.miniMessage(message));
         } else {
            if (GrimAPI.INSTANCE.getSpectateManager().enable(player)) {
               GrimPlayer grimPlayer = GrimAPI.INSTANCE.getPlayerDataManager().getPlayer(player);
               if (grimPlayer != null) {
                  String message = GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("spectate-return", "<click:run_command:/grim stopspectating><hover:show_text:\"/grim stopspectating\">\n%prefix% &fClick here to return to previous location\n</hover></click>");
                  message = MessageUtil.replacePlaceholders((Object)target.getPlayer(), (String)message);
                  grimPlayer.user.sendMessage(MessageUtil.miniMessage(message));
               }
            }

            player.setGameMode(GameMode.SPECTATOR);
            PaperUtils.teleportAsync(player, target.getPlayer().getLocation());
         }
      }
   }
}
