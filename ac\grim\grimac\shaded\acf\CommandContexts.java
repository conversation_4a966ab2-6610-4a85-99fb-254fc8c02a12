package ac.grim.grimac.shaded.acf;

import ac.grim.grimac.shaded.acf.annotation.Single;
import ac.grim.grimac.shaded.acf.annotation.Split;
import ac.grim.grimac.shaded.acf.annotation.Values;
import ac.grim.grimac.shaded.acf.contexts.ContextResolver;
import ac.grim.grimac.shaded.acf.contexts.IssuerAwareContextResolver;
import ac.grim.grimac.shaded.acf.contexts.IssuerOnlyContextResolver;
import ac.grim.grimac.shaded.acf.contexts.OptionalContextResolver;
import ac.grim.grimac.shaded.jetbrains.annotations.NotNull;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CommandContexts<R extends CommandExecutionContext<?, ? extends CommandIssuer>> {
   protected final Map<Class<?>, ContextResolver<?, R>> contextMap = new HashMap();
   protected final CommandManager manager;

   CommandContexts(CommandManager manager) {
      this.manager = manager;
      this.registerIssuerOnlyContext(CommandIssuer.class, (c) -> {
         return c.getIssuer();
      });
      this.registerContext(Short.class, (c) -> {
         String number = c.popFirstArg();

         try {
            return this.parseAndValidateNumber(number, c, -32768, (short)32767).shortValue();
         } catch (NumberFormatException var4) {
            throw new InvalidCommandArgument(MessageKeys.MUST_BE_A_NUMBER, new String[]{"{num}", number});
         }
      });
      this.registerContext(Short.TYPE, (c) -> {
         String number = c.popFirstArg();

         try {
            return this.parseAndValidateNumber(number, c, -32768, (short)32767).shortValue();
         } catch (NumberFormatException var4) {
            throw new InvalidCommandArgument(MessageKeys.MUST_BE_A_NUMBER, new String[]{"{num}", number});
         }
      });
      this.registerContext(Integer.class, (c) -> {
         String number = c.popFirstArg();

         try {
            return this.parseAndValidateNumber(number, c, Integer.MIN_VALUE, Integer.MAX_VALUE).intValue();
         } catch (NumberFormatException var4) {
            throw new InvalidCommandArgument(MessageKeys.MUST_BE_A_NUMBER, new String[]{"{num}", number});
         }
      });
      this.registerContext(Integer.TYPE, (c) -> {
         String number = c.popFirstArg();

         try {
            return this.parseAndValidateNumber(number, c, Integer.MIN_VALUE, Integer.MAX_VALUE).intValue();
         } catch (NumberFormatException var4) {
            throw new InvalidCommandArgument(MessageKeys.MUST_BE_A_NUMBER, new String[]{"{num}", number});
         }
      });
      this.registerContext(Long.class, (c) -> {
         String number = c.popFirstArg();

         try {
            return this.parseAndValidateNumber(number, c, Long.MIN_VALUE, Long.MAX_VALUE).longValue();
         } catch (NumberFormatException var4) {
            throw new InvalidCommandArgument(MessageKeys.MUST_BE_A_NUMBER, new String[]{"{num}", number});
         }
      });
      this.registerContext(Long.TYPE, (c) -> {
         String number = c.popFirstArg();

         try {
            return this.parseAndValidateNumber(number, c, Long.MIN_VALUE, Long.MAX_VALUE).longValue();
         } catch (NumberFormatException var4) {
            throw new InvalidCommandArgument(MessageKeys.MUST_BE_A_NUMBER, new String[]{"{num}", number});
         }
      });
      this.registerContext(Float.class, (c) -> {
         String number = c.popFirstArg();

         try {
            return this.parseAndValidateNumber(number, c, -3.4028235E38F, Float.MAX_VALUE).floatValue();
         } catch (NumberFormatException var4) {
            throw new InvalidCommandArgument(MessageKeys.MUST_BE_A_NUMBER, new String[]{"{num}", number});
         }
      });
      this.registerContext(Float.TYPE, (c) -> {
         String number = c.popFirstArg();

         try {
            return this.parseAndValidateNumber(number, c, -3.4028235E38F, Float.MAX_VALUE).floatValue();
         } catch (NumberFormatException var4) {
            throw new InvalidCommandArgument(MessageKeys.MUST_BE_A_NUMBER, new String[]{"{num}", number});
         }
      });
      this.registerContext(Double.class, (c) -> {
         String number = c.popFirstArg();

         try {
            return this.parseAndValidateNumber(number, c, -1.7976931348623157E308D, Double.MAX_VALUE).doubleValue();
         } catch (NumberFormatException var4) {
            throw new InvalidCommandArgument(MessageKeys.MUST_BE_A_NUMBER, new String[]{"{num}", number});
         }
      });
      this.registerContext(Double.TYPE, (c) -> {
         String number = c.popFirstArg();

         try {
            return this.parseAndValidateNumber(number, c, -1.7976931348623157E308D, Double.MAX_VALUE).doubleValue();
         } catch (NumberFormatException var4) {
            throw new InvalidCommandArgument(MessageKeys.MUST_BE_A_NUMBER, new String[]{"{num}", number});
         }
      });
      this.registerContext(Number.class, (c) -> {
         String number = c.popFirstArg();

         try {
            return this.parseAndValidateNumber(number, c, -1.7976931348623157E308D, Double.MAX_VALUE);
         } catch (NumberFormatException var4) {
            throw new InvalidCommandArgument(MessageKeys.MUST_BE_A_NUMBER, new String[]{"{num}", number});
         }
      });
      this.registerContext(BigDecimal.class, (c) -> {
         String numberStr = c.popFirstArg();

         try {
            BigDecimal number = ACFUtil.parseBigNumber(numberStr, c.hasFlag("suffixes"));
            this.validateMinMax(c, number);
            return number;
         } catch (NumberFormatException var4) {
            throw new InvalidCommandArgument(MessageKeys.MUST_BE_A_NUMBER, new String[]{"{num}", numberStr});
         }
      });
      this.registerContext(BigInteger.class, (c) -> {
         String numberStr = c.popFirstArg();

         try {
            BigDecimal number = ACFUtil.parseBigNumber(numberStr, c.hasFlag("suffixes"));
            this.validateMinMax(c, number);
            return number.toBigIntegerExact();
         } catch (NumberFormatException var4) {
            throw new InvalidCommandArgument(MessageKeys.MUST_BE_A_NUMBER, new String[]{"{num}", numberStr});
         }
      });
      this.registerContext(Boolean.class, (c) -> {
         return ACFUtil.isTruthy(c.popFirstArg());
      });
      this.registerContext(Boolean.TYPE, (c) -> {
         return ACFUtil.isTruthy(c.popFirstArg());
      });
      this.registerContext(Character.TYPE, (c) -> {
         String s = c.popFirstArg();
         if (s.length() > 1) {
            throw new InvalidCommandArgument(MessageKeys.MUST_BE_MAX_LENGTH, new String[]{"{max}", String.valueOf(1)});
         } else {
            return s.charAt(0);
         }
      });
      this.registerContext(String.class, (c) -> {
         if (c.hasAnnotation(Values.class)) {
            return c.popFirstArg();
         } else {
            String ret = c.isLastArg() && !c.hasAnnotation(Single.class) ? ACFUtil.join((Collection)c.getArgs()) : c.popFirstArg();
            Integer minLen = c.getFlagValue("minlen", (Integer)null);
            Integer maxLen = c.getFlagValue("maxlen", (Integer)null);
            if (minLen != null && ret.length() < minLen) {
               throw new InvalidCommandArgument(MessageKeys.MUST_BE_MIN_LENGTH, new String[]{"{min}", String.valueOf(minLen)});
            } else if (maxLen != null && ret.length() > maxLen) {
               throw new InvalidCommandArgument(MessageKeys.MUST_BE_MAX_LENGTH, new String[]{"{max}", String.valueOf(maxLen)});
            } else {
               return ret;
            }
         }
      });
      this.registerContext(String[].class, (c) -> {
         List<String> args = c.getArgs();
         String val;
         if (c.isLastArg() && !c.hasAnnotation(Single.class)) {
            val = ACFUtil.join((Collection)args);
         } else {
            val = c.popFirstArg();
         }

         String split = c.getAnnotationValue(Split.class, 8);
         if (split != null) {
            if (val.isEmpty()) {
               throw new InvalidCommandArgument();
            } else {
               return ACFPatterns.getPattern(split).split(val);
            }
         } else {
            if (!c.isLastArg()) {
               ACFUtil.sneaky(new IllegalStateException("Weird Command signature... String[] should be last or @Split"));
            }

            String[] result = (String[])args.toArray(new String[0]);
            args.clear();
            return result;
         }
      });
      this.registerContext(Enum.class, (c) -> {
         String first = c.popFirstArg();
         Class<? extends Enum<?>> enumCls = c.getParam().getType();
         Enum<?> match = ACFUtil.simpleMatch(enumCls, first);
         if (match == null) {
            List<String> names = ACFUtil.enumNames(enumCls);
            throw new InvalidCommandArgument(MessageKeys.PLEASE_SPECIFY_ONE_OF, new String[]{"{valid}", ACFUtil.join((Collection)names, ", ")});
         } else {
            return match;
         }
      });
      this.registerOptionalContext(CommandHelp.class, (c) -> {
         String first = c.getFirstArg();
         String last = c.getLastArg();
         Integer page = 1;
         List<String> search = null;
         if (last != null && ACFUtil.isInteger(last)) {
            c.popLastArg();
            page = ACFUtil.parseInt(last);
            if (page == null) {
               throw new InvalidCommandArgument(MessageKeys.MUST_BE_A_NUMBER, new String[]{"{num}", last});
            }

            if (!c.getArgs().isEmpty()) {
               search = c.getArgs();
            }
         } else if (first != null && ACFUtil.isInteger(first)) {
            c.popFirstArg();
            page = ACFUtil.parseInt(first);
            if (page == null) {
               throw new InvalidCommandArgument(MessageKeys.MUST_BE_A_NUMBER, new String[]{"{num}", first});
            }

            if (!c.getArgs().isEmpty()) {
               search = c.getArgs();
            }
         } else if (!c.getArgs().isEmpty()) {
            search = c.getArgs();
         }

         CommandHelp commandHelp = manager.generateCommandHelp();
         commandHelp.setPage(page);
         Integer perPage = c.getFlagValue("perpage", (Integer)null);
         if (perPage != null) {
            commandHelp.setPerPage(perPage);
         }

         if (search != null) {
            String cmd = String.join(" ", search);
            if (commandHelp.testExactMatch(cmd)) {
               return commandHelp;
            }
         }

         commandHelp.setSearch(search);
         return commandHelp;
      });
   }

   @NotNull
   private Number parseAndValidateNumber(String number, R c, Number minValue, Number maxValue) throws InvalidCommandArgument {
      Number val = ACFUtil.parseNumber(number, c.hasFlag("suffixes"));
      this.validateMinMax(c, val, minValue, maxValue);
      return val;
   }

   private void validateMinMax(R c, Number val) throws InvalidCommandArgument {
      this.validateMinMax(c, val, (Number)null, (Number)null);
   }

   private void validateMinMax(R c, Number val, Number minValue, Number maxValue) throws InvalidCommandArgument {
      Number minValue = c.getFlagValue("min", minValue);
      Number maxValue = c.getFlagValue("max", maxValue);
      if (maxValue != null && val.doubleValue() > maxValue.doubleValue()) {
         throw new InvalidCommandArgument(MessageKeys.PLEASE_SPECIFY_AT_MOST, new String[]{"{max}", String.valueOf(maxValue)});
      } else if (minValue != null && val.doubleValue() < minValue.doubleValue()) {
         throw new InvalidCommandArgument(MessageKeys.PLEASE_SPECIFY_AT_LEAST, new String[]{"{min}", String.valueOf(minValue)});
      }
   }

   /** @deprecated */
   @Deprecated
   public <T> void registerSenderAwareContext(Class<T> context, IssuerAwareContextResolver<T, R> supplier) {
      this.contextMap.put(context, supplier);
   }

   public <T> void registerIssuerAwareContext(Class<T> context, IssuerAwareContextResolver<T, R> supplier) {
      this.contextMap.put(context, supplier);
   }

   public <T> void registerIssuerOnlyContext(Class<T> context, IssuerOnlyContextResolver<T, R> supplier) {
      this.contextMap.put(context, supplier);
   }

   public <T> void registerOptionalContext(Class<T> context, OptionalContextResolver<T, R> supplier) {
      this.contextMap.put(context, supplier);
   }

   public <T> void registerContext(Class<T> context, ContextResolver<T, R> supplier) {
      this.contextMap.put(context, supplier);
   }

   public ContextResolver<?, R> getResolver(Class<?> type) {
      Class rootType = type;

      while(type != Object.class) {
         ContextResolver<?, R> resolver = (ContextResolver)this.contextMap.get(type);
         if (resolver != null) {
            return resolver;
         }

         if ((type = type.getSuperclass()) == null) {
            break;
         }
      }

      this.manager.log(LogLevel.ERROR, "Could not find context resolver", new IllegalStateException("No context resolver defined for " + rootType.getName()));
      return null;
   }
}
