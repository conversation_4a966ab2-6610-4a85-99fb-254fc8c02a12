package ac.grim.grimac;

import ac.grim.grimac.manager.AIManager;
import ac.grim.grimac.manager.AlertManagerImpl;
import ac.grim.grimac.manager.DiscordManager;
import ac.grim.grimac.manager.InitManager;
import ac.grim.grimac.manager.SpectateManager;
import ac.grim.grimac.manager.TickManager;
import ac.grim.grimac.manager.config.BaseConfigManager;
import ac.grim.grimac.utils.anticheat.PlayerDataManager;
import org.bukkit.plugin.java.JavaPlugin;

public enum GrimAPI {
   INSTANCE;

   private final BaseConfigManager configManager = new BaseConfigManager();
   private final AlertManagerImpl alertManager = new AlertManagerImpl();
   private final SpectateManager spectateManager = new SpectateManager();
   private final DiscordManager discordManager = new DiscordManager();
   private final PlayerDataManager playerDataManager = new PlayerDataManager();
   private final TickManager tickManager = new TickManager();
   private final GrimExternalAPI externalAPI = new GrimExternalAPI(this);
   private AIManager aiManager;
   private InitManager initManager;
   private JavaPlugin plugin;

   public void load(JavaPlugin plugin) {
      this.plugin = plugin;
      this.initManager = new InitManager();
      this.initManager.load();
   }

   public void start(JavaPlugin plugin) {
      this.plugin = plugin;
      this.initManager.start();

      // Initialize AI Manager after other components
      try {
         this.aiManager = new AIManager();
      } catch (Exception e) {
         plugin.getLogger().warning("Failed to initialize AI Manager: " + e.getMessage());
      }
   }

   public void stop(JavaPlugin plugin) {
      this.plugin = plugin;

      // Shutdown AI Manager first
      if (this.aiManager != null) {
         try {
            this.aiManager.shutdown();
         } catch (Exception e) {
            plugin.getLogger().warning("Error shutting down AI Manager: " + e.getMessage());
         }
      }

      this.initManager.stop();
   }

   public BaseConfigManager getConfigManager() {
      return this.configManager;
   }

   public AlertManagerImpl getAlertManager() {
      return this.alertManager;
   }

   public SpectateManager getSpectateManager() {
      return this.spectateManager;
   }

   public DiscordManager getDiscordManager() {
      return this.discordManager;
   }

   public PlayerDataManager getPlayerDataManager() {
      return this.playerDataManager;
   }

   public TickManager getTickManager() {
      return this.tickManager;
   }

   public GrimExternalAPI getExternalAPI() {
      return this.externalAPI;
   }

   public InitManager getInitManager() {
      return this.initManager;
   }

   public AIManager getAIManager() {
      return this.aiManager;
   }

   public JavaPlugin getPlugin() {
      return this.plugin;
   }

   // $FF: synthetic method
   private static GrimAPI[] $values() {
      return new GrimAPI[]{INSTANCE};
   }
}
