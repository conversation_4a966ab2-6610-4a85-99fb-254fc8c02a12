package ac.grim.grimac.checks.impl.badpackets;

import ac.grim.grimac.checks.Check;
import ac.grim.grimac.checks.CheckData;
import ac.grim.grimac.checks.type.PacketCheck;
import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketReceiveEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.event.PacketSendEvent;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.packettype.PacketType;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.client.WrapperPlayClientKeepAlive;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerKeepAlive;
import ac.grim.grimac.utils.data.Pair;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.Queue;

@CheckData(
   name = "BadPacketsO"
)
public class BadPacketsO extends Check implements PacketCheck {
   Queue<Pair<Long, Long>> keepaliveMap = new LinkedList();

   public BadPacketsO(GrimPlayer player) {
      super(player);
   }

   public void onPacketSend(PacketSendEvent event) {
      if (event.getPacketType() == PacketType.Play.Server.KEEP_ALIVE) {
         WrapperPlayServerKeepAlive packet = new WrapperPlayServerKeepAlive(event);
         this.keepaliveMap.add(new Pair(packet.getId(), System.nanoTime()));
      }

   }

   public void onPacketReceive(PacketReceiveEvent event) {
      if (event.getPacketType() == PacketType.Play.Client.KEEP_ALIVE) {
         WrapperPlayClientKeepAlive packet = new WrapperPlayClientKeepAlive(event);
         long id = packet.getId();
         boolean hasID = false;
         Iterator var6 = this.keepaliveMap.iterator();

         while(var6.hasNext()) {
            Pair<Long, Long> iterator = (Pair)var6.next();
            if ((Long)iterator.first() == id) {
               hasID = true;
               break;
            }
         }

         Pair data;
         if (!hasID) {
            if (this.flagAndAlert("id=" + id) && this.shouldModifyPackets()) {
               event.setCancelled(true);
               this.player.onPacketCancel();
            }
         } else {
            do {
               data = (Pair)this.keepaliveMap.poll();
            } while(data != null && (Long)data.first() != id);
         }
      }

   }
}
