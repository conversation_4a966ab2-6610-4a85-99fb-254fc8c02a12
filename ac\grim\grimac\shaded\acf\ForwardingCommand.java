package ac.grim.grimac.shaded.acf;

import java.util.Collections;
import java.util.List;
import java.util.Set;

public class ForwardingCommand extends BaseCommand {
   private final BaseCommand command;
   private final String[] baseArgs;
   private final RegisteredCommand regCommand;

   ForwardingCommand(BaseCommand baseCommand, RegisteredCommand regCommand, String[] baseArgs) {
      this.regCommand = regCommand;
      this.commandName = baseCommand.commandName;
      this.command = baseCommand;
      this.baseArgs = baseArgs;
      this.manager = baseCommand.manager;
      this.subCommands.put("__default", regCommand);
   }

   public List<RegisteredCommand> getRegisteredCommands() {
      return Collections.singletonList(this.regCommand);
   }

   public CommandOperationContext getLastCommandOperationContext() {
      return this.command.getLastCommandOperationContext();
   }

   public Set<String> getRequiredPermissions() {
      return this.command.getRequiredPermissions();
   }

   public boolean hasPermission(Object issuer) {
      return this.command.hasPermission(issuer);
   }

   public boolean requiresPermission(String permission) {
      return this.command.requiresPermission(permission);
   }

   public boolean hasPermission(CommandIssuer sender) {
      return this.command.hasPermission(sender);
   }

   public List<String> tabComplete(CommandIssuer issuer, RootCommand rootCommand, String[] args, boolean isAsync) throws IllegalArgumentException {
      return this.command.tabComplete(issuer, rootCommand, args, isAsync);
   }

   public void execute(CommandIssuer issuer, CommandRouter.CommandRouteResult result) {
      result = new CommandRouter.CommandRouteResult(this.regCommand, result.args, ACFUtil.join(this.baseArgs), result.commandLabel);
      this.command.execute(issuer, result);
   }

   BaseCommand getCommand() {
      return this.command;
   }
}
