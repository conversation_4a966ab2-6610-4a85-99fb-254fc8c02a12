package ac.grim.grimac.commands;

import ac.grim.grimac.GrimAPI;
import ac.grim.grimac.shaded.acf.BaseCommand;
import ac.grim.grimac.shaded.acf.annotation.CommandAlias;
import ac.grim.grimac.shaded.acf.annotation.CommandPermission;
import ac.grim.grimac.shaded.acf.annotation.Subcommand;
import ac.grim.grimac.utils.anticheat.MessageUtil;
import org.bukkit.command.CommandSender;

@CommandAlias("grim|grimac")
public class GrimReload extends BaseCommand {
   @Subcommand("reload")
   @CommandPermission("grim.reload")
   public void onReload(CommandSender sender) {
      String reloading = GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("reloading", "%prefix% &7Reloading config...");
      MessageUtil.sendMessage(sender, MessageUtil.miniMessage(MessageUtil.replacePlaceholders((Object)sender, (String)reloading)));
      GrimAPI.INSTANCE.getExternalAPI().reloadAsync().exceptionally((throwable) -> {
         return false;
      }).thenAccept((bool) -> {
         String message = bool ? GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("reloaded", "%prefix% &fConfig has been reloaded.") : GrimAPI.INSTANCE.getConfigManager().getConfig().getStringElse("reload-failed", "%prefix% &cFailed to reload config.");
         MessageUtil.sendMessage(sender, MessageUtil.miniMessage(MessageUtil.replacePlaceholders((Object)sender, (String)message)));
      });
   }
}
