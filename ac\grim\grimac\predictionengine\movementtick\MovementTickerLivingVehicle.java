package ac.grim.grimac.predictionengine.movementtick;

import ac.grim.grimac.player.GrimPlayer;
import ac.grim.grimac.predictionengine.predictions.rideable.PredictionEngineRideableLava;
import ac.grim.grimac.predictionengine.predictions.rideable.PredictionEngineRideableNormal;
import ac.grim.grimac.predictionengine.predictions.rideable.PredictionEngineRideableWater;
import ac.grim.grimac.predictionengine.predictions.rideable.PredictionEngineRideableWaterLegacy;
import ac.grim.grimac.shaded.com.github.retrooper.packetevents.protocol.player.ClientVersion;
import ac.grim.grimac.utils.nmsutil.BlockProperties;
import org.bukkit.util.Vector;

public class MovementTickerLivingVehicle extends MovementTicker {
   Vector movementInput = new Vector();

   public MovementTickerLivingVehicle(GrimPlayer player) {
      super(player);
   }

   public void doWaterMove(float swimSpeed, boolean isFalling, float swimFriction) {
      if (this.player.getClientVersion().isNewerThanOrEquals(ClientVersion.V_1_13)) {
         (new PredictionEngineRideableWater(this.movementInput)).guessBestMovement(swimSpeed, this.player, isFalling, this.player.gravity, swimFriction, this.player.lastY);
      } else {
         (new PredictionEngineRideableWaterLegacy(this.movementInput)).guessBestMovement(swimSpeed, this.player, this.player.gravity, swimFriction, this.player.lastY);
      }

   }

   public void doLavaMove() {
      (new PredictionEngineRideableLava(this.movementInput)).guessBestMovement(0.02F, this.player);
   }

   public void doNormalMove(float blockFriction) {
      (new PredictionEngineRideableNormal(this.movementInput)).guessBestMovement(BlockProperties.getFrictionInfluencedSpeed(blockFriction, this.player), this.player);
   }
}
